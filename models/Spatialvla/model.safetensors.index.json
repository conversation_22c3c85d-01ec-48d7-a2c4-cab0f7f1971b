{"metadata": {"total_size": 8055709462}, "weight_map": {"language_model.lm_head.weight": "model-00002-of-00002.safetensors", "language_model.model.embed_tokens.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.0.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.0.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.0.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.0.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.0.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.0.post_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.0.pre_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.0.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.0.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.0.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.0.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.1.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.1.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.1.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.1.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.1.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.1.post_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.1.pre_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.1.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.1.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.1.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.1.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.10.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.10.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.10.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.10.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.10.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.10.post_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.10.pre_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.10.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.10.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.10.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.10.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.11.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.11.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.11.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.11.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.11.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.11.post_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.11.pre_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.11.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.11.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.11.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.11.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.12.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.12.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.12.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.12.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.12.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.12.post_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.12.pre_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.12.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.12.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.12.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.12.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.13.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.13.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.13.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.13.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.13.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.13.post_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.13.pre_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.13.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.13.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.13.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.13.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.14.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.14.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.14.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.14.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.14.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.14.post_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.14.pre_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.14.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.14.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.14.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.14.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.15.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.15.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.15.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.15.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.15.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.15.post_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.15.pre_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.15.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.15.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.15.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.15.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.16.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.16.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.16.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.16.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.16.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.16.post_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.16.pre_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.16.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.16.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.16.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.16.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.17.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.17.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.17.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.17.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.17.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.17.post_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.17.pre_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.17.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.17.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.17.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.17.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.18.input_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.18.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.18.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.18.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.18.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.18.post_feedforward_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.18.pre_feedforward_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.18.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.18.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.18.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.18.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.19.input_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.19.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.19.mlp.gate_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.19.mlp.up_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.19.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.19.post_feedforward_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.19.pre_feedforward_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.19.self_attn.k_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.19.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.19.self_attn.q_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.19.self_attn.v_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.2.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.2.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.2.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.2.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.2.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.2.post_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.2.pre_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.2.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.2.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.2.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.2.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.20.input_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.20.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.20.mlp.gate_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.20.mlp.up_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.20.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.20.post_feedforward_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.20.pre_feedforward_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.20.self_attn.k_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.20.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.20.self_attn.q_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.20.self_attn.v_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.21.input_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.21.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.21.mlp.gate_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.21.mlp.up_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.21.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.21.post_feedforward_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.21.pre_feedforward_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.21.self_attn.k_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.21.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.21.self_attn.q_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.21.self_attn.v_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.22.input_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.22.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.22.mlp.gate_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.22.mlp.up_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.22.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.22.post_feedforward_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.22.pre_feedforward_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.22.self_attn.k_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.22.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.22.self_attn.q_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.22.self_attn.v_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.23.input_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.23.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.23.mlp.gate_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.23.mlp.up_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.23.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.23.post_feedforward_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.23.pre_feedforward_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.23.self_attn.k_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.23.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.23.self_attn.q_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.23.self_attn.v_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.24.input_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.24.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.24.mlp.gate_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.24.mlp.up_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.24.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.24.post_feedforward_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.24.pre_feedforward_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.24.self_attn.k_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.24.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.24.self_attn.q_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.24.self_attn.v_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.25.input_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.25.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.25.mlp.gate_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.25.mlp.up_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.25.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.25.post_feedforward_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.25.pre_feedforward_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.25.self_attn.k_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.25.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.25.self_attn.q_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.25.self_attn.v_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.3.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.3.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.3.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.3.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.3.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.3.post_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.3.pre_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.3.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.3.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.3.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.3.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.4.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.4.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.4.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.4.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.4.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.4.post_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.4.pre_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.4.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.4.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.4.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.4.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.5.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.5.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.5.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.5.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.5.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.5.post_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.5.pre_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.5.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.5.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.5.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.5.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.6.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.6.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.6.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.6.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.6.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.6.post_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.6.pre_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.6.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.6.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.6.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.6.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.7.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.7.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.7.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.7.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.7.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.7.post_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.7.pre_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.7.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.7.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.7.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.7.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.8.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.8.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.8.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.8.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.8.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.8.post_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.8.pre_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.8.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.8.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.8.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.8.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.9.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.9.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.9.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.9.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.9.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.9.post_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.9.pre_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.9.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.9.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.9.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.9.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.norm.weight": "model-00002-of-00002.safetensors", "multi_modal_projector.linear.bias": "model-00001-of-00002.safetensors", "multi_modal_projector.linear.weight": "model-00001-of-00002.safetensors", "position_embedding_3d.position_embedding_head.0.bias": "model-00002-of-00002.safetensors", "position_embedding_3d.position_embedding_head.0.weight": "model-00002-of-00002.safetensors", "position_embedding_3d.position_embedding_head.1.bias": "model-00002-of-00002.safetensors", "position_embedding_3d.position_embedding_head.1.weight": "model-00002-of-00002.safetensors", "position_embedding_3d.position_embedding_head.3.bias": "model-00002-of-00002.safetensors", "position_embedding_3d.position_embedding_head.3.weight": "model-00002-of-00002.safetensors", "spatial_embed_tokens.weight": "model-00002-of-00002.safetensors", "vision_tower.vision_model.embeddings.patch_embedding.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.embeddings.patch_embedding.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.embeddings.position_embedding.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.0.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.0.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.0.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.0.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.0.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.0.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.0.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.0.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.1.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.1.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.1.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.1.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.1.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.1.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.1.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.1.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.10.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.10.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.10.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.10.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.10.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.10.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.10.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.10.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.11.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.11.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.11.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.11.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.11.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.11.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.11.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.11.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.12.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.12.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.12.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.12.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.12.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.12.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.12.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.12.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.12.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.12.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.12.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.12.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.12.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.12.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.12.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.12.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.13.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.13.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.13.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.13.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.13.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.13.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.13.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.13.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.13.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.13.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.13.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.13.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.13.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.13.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.13.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.13.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.14.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.14.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.14.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.14.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.14.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.14.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.14.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.14.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.14.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.14.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.14.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.14.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.14.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.14.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.14.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.14.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.15.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.15.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.15.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.15.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.15.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.15.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.15.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.15.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.15.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.15.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.15.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.15.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.15.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.15.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.15.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.15.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.16.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.16.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.16.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.16.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.16.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.16.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.16.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.16.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.16.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.16.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.16.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.16.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.16.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.16.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.16.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.16.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.17.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.17.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.17.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.17.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.17.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.17.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.17.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.17.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.17.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.17.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.17.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.17.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.17.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.17.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.17.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.17.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.18.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.18.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.18.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.18.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.18.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.18.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.18.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.18.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.18.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.18.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.18.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.18.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.18.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.18.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.18.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.18.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.19.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.19.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.19.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.19.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.19.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.19.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.19.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.19.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.19.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.19.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.19.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.19.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.19.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.19.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.19.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.19.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.2.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.2.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.2.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.2.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.2.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.2.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.2.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.2.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.20.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.20.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.20.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.20.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.20.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.20.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.20.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.20.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.20.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.20.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.20.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.20.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.20.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.20.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.20.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.20.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.21.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.21.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.21.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.21.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.21.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.21.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.21.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.21.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.21.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.21.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.21.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.21.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.21.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.21.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.21.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.21.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.22.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.22.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.22.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.22.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.22.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.22.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.22.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.22.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.22.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.22.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.22.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.22.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.22.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.22.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.22.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.22.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.23.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.23.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.23.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.23.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.23.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.23.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.23.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.23.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.23.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.23.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.23.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.23.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.23.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.23.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.23.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.23.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.24.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.24.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.24.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.24.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.24.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.24.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.24.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.24.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.24.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.24.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.24.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.24.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.24.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.24.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.24.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.24.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.25.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.25.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.25.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.25.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.25.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.25.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.25.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.25.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.25.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.25.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.25.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.25.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.25.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.25.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.25.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.25.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.26.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.26.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.26.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.26.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.26.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.26.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.26.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.26.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.26.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.26.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.26.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.26.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.26.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.26.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.26.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.26.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.3.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.3.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.3.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.3.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.3.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.3.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.3.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.3.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.4.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.4.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.4.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.4.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.4.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.4.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.4.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.4.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.5.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.5.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.5.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.5.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.5.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.5.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.5.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.5.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.6.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.6.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.6.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.6.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.6.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.6.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.6.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.6.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.7.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.7.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.7.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.7.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.7.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.7.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.7.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.7.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.8.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.8.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.8.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.8.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.8.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.8.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.8.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.8.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.9.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.9.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.9.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.9.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.9.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.9.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.9.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.9.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.post_layernorm.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.post_layernorm.weight": "model-00001-of-00002.safetensors", "vision_zoe_model.backbone.embeddings.cls_token": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.embeddings.patch_embeddings.projection.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.embeddings.patch_embeddings.projection.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.0.attention.attention.key.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.0.attention.attention.query.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.0.attention.attention.query.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.0.attention.attention.relative_position_bias.relative_position_bias_table": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.0.attention.attention.value.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.0.attention.attention.value.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.0.attention.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.0.attention.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.0.intermediate.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.0.intermediate.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.0.lambda_1": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.0.lambda_2": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.0.layernorm_after.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.0.layernorm_after.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.0.layernorm_before.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.0.layernorm_before.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.0.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.0.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.1.attention.attention.key.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.1.attention.attention.query.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.1.attention.attention.query.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.1.attention.attention.relative_position_bias.relative_position_bias_table": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.1.attention.attention.value.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.1.attention.attention.value.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.1.attention.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.1.attention.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.1.intermediate.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.1.intermediate.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.1.lambda_1": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.1.lambda_2": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.1.layernorm_after.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.1.layernorm_after.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.1.layernorm_before.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.1.layernorm_before.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.1.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.1.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.10.attention.attention.key.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.10.attention.attention.query.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.10.attention.attention.query.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.10.attention.attention.relative_position_bias.relative_position_bias_table": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.10.attention.attention.value.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.10.attention.attention.value.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.10.attention.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.10.attention.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.10.intermediate.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.10.intermediate.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.10.lambda_1": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.10.lambda_2": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.10.layernorm_after.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.10.layernorm_after.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.10.layernorm_before.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.10.layernorm_before.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.10.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.10.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.11.attention.attention.key.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.11.attention.attention.query.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.11.attention.attention.query.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.11.attention.attention.relative_position_bias.relative_position_bias_table": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.11.attention.attention.value.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.11.attention.attention.value.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.11.attention.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.11.attention.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.11.intermediate.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.11.intermediate.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.11.lambda_1": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.11.lambda_2": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.11.layernorm_after.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.11.layernorm_after.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.11.layernorm_before.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.11.layernorm_before.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.11.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.11.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.12.attention.attention.key.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.12.attention.attention.query.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.12.attention.attention.query.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.12.attention.attention.relative_position_bias.relative_position_bias_table": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.12.attention.attention.value.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.12.attention.attention.value.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.12.attention.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.12.attention.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.12.intermediate.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.12.intermediate.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.12.lambda_1": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.12.lambda_2": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.12.layernorm_after.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.12.layernorm_after.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.12.layernorm_before.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.12.layernorm_before.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.12.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.12.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.13.attention.attention.key.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.13.attention.attention.query.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.13.attention.attention.query.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.13.attention.attention.relative_position_bias.relative_position_bias_table": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.13.attention.attention.value.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.13.attention.attention.value.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.13.attention.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.13.attention.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.13.intermediate.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.13.intermediate.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.13.lambda_1": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.13.lambda_2": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.13.layernorm_after.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.13.layernorm_after.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.13.layernorm_before.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.13.layernorm_before.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.13.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.13.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.14.attention.attention.key.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.14.attention.attention.query.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.14.attention.attention.query.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.14.attention.attention.relative_position_bias.relative_position_bias_table": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.14.attention.attention.value.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.14.attention.attention.value.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.14.attention.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.14.attention.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.14.intermediate.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.14.intermediate.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.14.lambda_1": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.14.lambda_2": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.14.layernorm_after.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.14.layernorm_after.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.14.layernorm_before.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.14.layernorm_before.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.14.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.14.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.15.attention.attention.key.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.15.attention.attention.query.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.15.attention.attention.query.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.15.attention.attention.relative_position_bias.relative_position_bias_table": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.15.attention.attention.value.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.15.attention.attention.value.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.15.attention.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.15.attention.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.15.intermediate.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.15.intermediate.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.15.lambda_1": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.15.lambda_2": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.15.layernorm_after.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.15.layernorm_after.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.15.layernorm_before.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.15.layernorm_before.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.15.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.15.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.16.attention.attention.key.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.16.attention.attention.query.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.16.attention.attention.query.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.16.attention.attention.relative_position_bias.relative_position_bias_table": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.16.attention.attention.value.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.16.attention.attention.value.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.16.attention.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.16.attention.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.16.intermediate.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.16.intermediate.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.16.lambda_1": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.16.lambda_2": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.16.layernorm_after.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.16.layernorm_after.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.16.layernorm_before.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.16.layernorm_before.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.16.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.16.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.17.attention.attention.key.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.17.attention.attention.query.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.17.attention.attention.query.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.17.attention.attention.relative_position_bias.relative_position_bias_table": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.17.attention.attention.value.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.17.attention.attention.value.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.17.attention.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.17.attention.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.17.intermediate.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.17.intermediate.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.17.lambda_1": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.17.lambda_2": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.17.layernorm_after.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.17.layernorm_after.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.17.layernorm_before.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.17.layernorm_before.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.17.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.17.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.18.attention.attention.key.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.18.attention.attention.query.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.18.attention.attention.query.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.18.attention.attention.relative_position_bias.relative_position_bias_table": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.18.attention.attention.value.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.18.attention.attention.value.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.18.attention.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.18.attention.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.18.intermediate.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.18.intermediate.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.18.lambda_1": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.18.lambda_2": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.18.layernorm_after.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.18.layernorm_after.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.18.layernorm_before.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.18.layernorm_before.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.18.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.18.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.19.attention.attention.key.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.19.attention.attention.query.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.19.attention.attention.query.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.19.attention.attention.relative_position_bias.relative_position_bias_table": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.19.attention.attention.value.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.19.attention.attention.value.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.19.attention.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.19.attention.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.19.intermediate.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.19.intermediate.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.19.lambda_1": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.19.lambda_2": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.19.layernorm_after.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.19.layernorm_after.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.19.layernorm_before.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.19.layernorm_before.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.19.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.19.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.2.attention.attention.key.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.2.attention.attention.query.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.2.attention.attention.query.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.2.attention.attention.relative_position_bias.relative_position_bias_table": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.2.attention.attention.value.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.2.attention.attention.value.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.2.attention.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.2.attention.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.2.intermediate.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.2.intermediate.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.2.lambda_1": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.2.lambda_2": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.2.layernorm_after.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.2.layernorm_after.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.2.layernorm_before.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.2.layernorm_before.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.2.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.2.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.20.attention.attention.key.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.20.attention.attention.query.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.20.attention.attention.query.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.20.attention.attention.relative_position_bias.relative_position_bias_table": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.20.attention.attention.value.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.20.attention.attention.value.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.20.attention.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.20.attention.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.20.intermediate.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.20.intermediate.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.20.lambda_1": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.20.lambda_2": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.20.layernorm_after.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.20.layernorm_after.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.20.layernorm_before.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.20.layernorm_before.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.20.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.20.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.21.attention.attention.key.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.21.attention.attention.query.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.21.attention.attention.query.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.21.attention.attention.relative_position_bias.relative_position_bias_table": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.21.attention.attention.value.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.21.attention.attention.value.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.21.attention.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.21.attention.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.21.intermediate.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.21.intermediate.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.21.lambda_1": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.21.lambda_2": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.21.layernorm_after.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.21.layernorm_after.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.21.layernorm_before.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.21.layernorm_before.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.21.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.21.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.22.attention.attention.key.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.22.attention.attention.query.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.22.attention.attention.query.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.22.attention.attention.relative_position_bias.relative_position_bias_table": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.22.attention.attention.value.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.22.attention.attention.value.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.22.attention.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.22.attention.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.22.intermediate.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.22.intermediate.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.22.lambda_1": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.22.lambda_2": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.22.layernorm_after.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.22.layernorm_after.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.22.layernorm_before.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.22.layernorm_before.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.22.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.22.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.23.attention.attention.key.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.23.attention.attention.query.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.23.attention.attention.query.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.23.attention.attention.relative_position_bias.relative_position_bias_table": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.23.attention.attention.value.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.23.attention.attention.value.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.23.attention.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.23.attention.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.23.intermediate.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.23.intermediate.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.23.lambda_1": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.23.lambda_2": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.23.layernorm_after.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.23.layernorm_after.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.23.layernorm_before.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.23.layernorm_before.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.23.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.23.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.3.attention.attention.key.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.3.attention.attention.query.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.3.attention.attention.query.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.3.attention.attention.relative_position_bias.relative_position_bias_table": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.3.attention.attention.value.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.3.attention.attention.value.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.3.attention.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.3.attention.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.3.intermediate.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.3.intermediate.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.3.lambda_1": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.3.lambda_2": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.3.layernorm_after.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.3.layernorm_after.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.3.layernorm_before.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.3.layernorm_before.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.3.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.3.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.4.attention.attention.key.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.4.attention.attention.query.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.4.attention.attention.query.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.4.attention.attention.relative_position_bias.relative_position_bias_table": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.4.attention.attention.value.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.4.attention.attention.value.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.4.attention.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.4.attention.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.4.intermediate.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.4.intermediate.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.4.lambda_1": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.4.lambda_2": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.4.layernorm_after.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.4.layernorm_after.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.4.layernorm_before.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.4.layernorm_before.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.4.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.4.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.5.attention.attention.key.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.5.attention.attention.query.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.5.attention.attention.query.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.5.attention.attention.relative_position_bias.relative_position_bias_table": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.5.attention.attention.value.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.5.attention.attention.value.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.5.attention.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.5.attention.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.5.intermediate.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.5.intermediate.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.5.lambda_1": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.5.lambda_2": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.5.layernorm_after.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.5.layernorm_after.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.5.layernorm_before.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.5.layernorm_before.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.5.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.5.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.6.attention.attention.key.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.6.attention.attention.query.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.6.attention.attention.query.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.6.attention.attention.relative_position_bias.relative_position_bias_table": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.6.attention.attention.value.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.6.attention.attention.value.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.6.attention.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.6.attention.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.6.intermediate.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.6.intermediate.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.6.lambda_1": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.6.lambda_2": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.6.layernorm_after.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.6.layernorm_after.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.6.layernorm_before.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.6.layernorm_before.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.6.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.6.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.7.attention.attention.key.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.7.attention.attention.query.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.7.attention.attention.query.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.7.attention.attention.relative_position_bias.relative_position_bias_table": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.7.attention.attention.value.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.7.attention.attention.value.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.7.attention.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.7.attention.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.7.intermediate.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.7.intermediate.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.7.lambda_1": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.7.lambda_2": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.7.layernorm_after.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.7.layernorm_after.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.7.layernorm_before.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.7.layernorm_before.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.7.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.7.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.8.attention.attention.key.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.8.attention.attention.query.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.8.attention.attention.query.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.8.attention.attention.relative_position_bias.relative_position_bias_table": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.8.attention.attention.value.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.8.attention.attention.value.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.8.attention.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.8.attention.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.8.intermediate.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.8.intermediate.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.8.lambda_1": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.8.lambda_2": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.8.layernorm_after.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.8.layernorm_after.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.8.layernorm_before.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.8.layernorm_before.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.8.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.8.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.9.attention.attention.key.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.9.attention.attention.query.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.9.attention.attention.query.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.9.attention.attention.relative_position_bias.relative_position_bias_table": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.9.attention.attention.value.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.9.attention.attention.value.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.9.attention.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.9.attention.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.9.intermediate.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.9.intermediate.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.9.lambda_1": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.9.lambda_2": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.9.layernorm_after.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.9.layernorm_after.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.9.layernorm_before.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.9.layernorm_before.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.9.output.dense.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.backbone.encoder.layer.9.output.dense.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.attractors.kitti.0.conv1.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.attractors.kitti.0.conv1.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.attractors.kitti.0.conv2.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.attractors.kitti.0.conv2.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.attractors.kitti.1.conv1.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.attractors.kitti.1.conv1.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.attractors.kitti.1.conv2.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.attractors.kitti.1.conv2.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.attractors.kitti.2.conv1.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.attractors.kitti.2.conv1.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.attractors.kitti.2.conv2.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.attractors.kitti.2.conv2.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.attractors.kitti.3.conv1.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.attractors.kitti.3.conv1.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.attractors.kitti.3.conv2.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.attractors.kitti.3.conv2.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.attractors.nyu.0.conv1.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.attractors.nyu.0.conv1.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.attractors.nyu.0.conv2.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.attractors.nyu.0.conv2.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.attractors.nyu.1.conv1.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.attractors.nyu.1.conv1.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.attractors.nyu.1.conv2.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.attractors.nyu.1.conv2.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.attractors.nyu.2.conv1.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.attractors.nyu.2.conv1.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.attractors.nyu.2.conv2.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.attractors.nyu.2.conv2.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.attractors.nyu.3.conv1.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.attractors.nyu.3.conv1.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.attractors.nyu.3.conv2.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.attractors.nyu.3.conv2.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.conditional_log_binomial.kitti.mlp.0.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.conditional_log_binomial.kitti.mlp.0.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.conditional_log_binomial.kitti.mlp.2.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.conditional_log_binomial.kitti.mlp.2.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.conditional_log_binomial.nyu.mlp.0.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.conditional_log_binomial.nyu.mlp.0.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.conditional_log_binomial.nyu.mlp.2.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.conditional_log_binomial.nyu.mlp.2.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.conv2.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.conv2.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.mlp_classifier.linear1.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.mlp_classifier.linear1.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.mlp_classifier.linear2.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.mlp_classifier.linear2.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.embedding_convPxP.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.embedding_convPxP.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.0.linear1.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.0.linear1.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.0.linear2.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.0.linear2.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.0.norm1.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.0.norm1.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.0.norm2.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.0.norm2.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.0.self_attn.key.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.0.self_attn.key.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.0.self_attn.out_proj.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.0.self_attn.out_proj.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.0.self_attn.query.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.0.self_attn.query.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.0.self_attn.value.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.0.self_attn.value.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.1.linear1.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.1.linear1.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.1.linear2.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.1.linear2.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.1.norm1.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.1.norm1.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.1.norm2.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.1.norm2.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.1.self_attn.key.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.1.self_attn.key.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.1.self_attn.out_proj.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.1.self_attn.out_proj.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.1.self_attn.query.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.1.self_attn.query.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.1.self_attn.value.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.1.self_attn.value.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.2.linear1.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.2.linear1.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.2.linear2.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.2.linear2.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.2.norm1.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.2.norm1.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.2.norm2.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.2.norm2.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.2.self_attn.key.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.2.self_attn.key.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.2.self_attn.out_proj.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.2.self_attn.out_proj.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.2.self_attn.query.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.2.self_attn.query.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.2.self_attn.value.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.2.self_attn.value.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.3.linear1.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.3.linear1.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.3.linear2.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.3.linear2.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.3.norm1.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.3.norm1.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.3.norm2.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.3.norm2.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.3.self_attn.key.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.3.self_attn.key.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.3.self_attn.out_proj.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.3.self_attn.out_proj.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.3.self_attn.query.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.3.self_attn.query.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.3.self_attn.value.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.patch_transformer.transformer_encoder.3.self_attn.value.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.projectors.0.conv1.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.projectors.0.conv1.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.projectors.0.conv2.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.projectors.0.conv2.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.projectors.1.conv1.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.projectors.1.conv1.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.projectors.1.conv2.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.projectors.1.conv2.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.projectors.2.conv1.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.projectors.2.conv1.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.projectors.2.conv2.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.projectors.2.conv2.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.projectors.3.conv1.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.projectors.3.conv1.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.projectors.3.conv2.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.projectors.3.conv2.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.seed_bin_regressors.kitti.conv1.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.seed_bin_regressors.kitti.conv1.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.seed_bin_regressors.kitti.conv2.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.seed_bin_regressors.kitti.conv2.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.seed_bin_regressors.nyu.conv1.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.seed_bin_regressors.nyu.conv1.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.seed_bin_regressors.nyu.conv2.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.seed_bin_regressors.nyu.conv2.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.seed_projector.conv1.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.seed_projector.conv1.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.seed_projector.conv2.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.metric_head.seed_projector.conv2.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.convs.0.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.convs.1.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.convs.2.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.convs.3.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.0.projection.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.0.projection.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.0.residual_layer1.convolution1.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.0.residual_layer1.convolution1.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.0.residual_layer1.convolution2.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.0.residual_layer1.convolution2.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.0.residual_layer2.convolution1.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.0.residual_layer2.convolution1.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.0.residual_layer2.convolution2.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.0.residual_layer2.convolution2.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.1.projection.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.1.projection.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.1.residual_layer1.convolution1.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.1.residual_layer1.convolution1.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.1.residual_layer1.convolution2.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.1.residual_layer1.convolution2.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.1.residual_layer2.convolution1.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.1.residual_layer2.convolution1.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.1.residual_layer2.convolution2.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.1.residual_layer2.convolution2.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.2.projection.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.2.projection.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.2.residual_layer1.convolution1.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.2.residual_layer1.convolution1.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.2.residual_layer1.convolution2.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.2.residual_layer1.convolution2.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.2.residual_layer2.convolution1.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.2.residual_layer2.convolution1.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.2.residual_layer2.convolution2.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.2.residual_layer2.convolution2.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.3.projection.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.3.projection.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.3.residual_layer1.convolution1.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.3.residual_layer1.convolution1.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.3.residual_layer1.convolution2.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.3.residual_layer1.convolution2.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.3.residual_layer2.convolution1.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.3.residual_layer2.convolution1.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.3.residual_layer2.convolution2.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.fusion_stage.layers.3.residual_layer2.convolution2.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.reassemble_stage.layers.0.projection.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.reassemble_stage.layers.0.projection.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.reassemble_stage.layers.0.resize.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.reassemble_stage.layers.0.resize.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.reassemble_stage.layers.1.projection.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.reassemble_stage.layers.1.projection.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.reassemble_stage.layers.1.resize.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.reassemble_stage.layers.1.resize.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.reassemble_stage.layers.2.projection.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.reassemble_stage.layers.2.projection.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.reassemble_stage.layers.3.projection.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.reassemble_stage.layers.3.projection.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.reassemble_stage.layers.3.resize.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.reassemble_stage.layers.3.resize.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.reassemble_stage.readout_projects.0.0.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.reassemble_stage.readout_projects.0.0.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.reassemble_stage.readout_projects.1.0.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.reassemble_stage.readout_projects.1.0.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.reassemble_stage.readout_projects.2.0.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.reassemble_stage.readout_projects.2.0.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.reassemble_stage.readout_projects.3.0.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.neck.reassemble_stage.readout_projects.3.0.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.relative_head.conv1.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.relative_head.conv1.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.relative_head.conv2.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.relative_head.conv2.weight": "model-00002-of-00002.safetensors", "vision_zoe_model.relative_head.conv3.bias": "model-00002-of-00002.safetensors", "vision_zoe_model.relative_head.conv3.weight": "model-00002-of-00002.safetensors"}}