{"_vocab_size": 265347, "action_token_begin_idx": 257153, "architectures": ["SpatialVLAForConditionalGeneration"], "auto_map": {"AutoConfig": "configuration_spatialvla.SpatialVLAConfig", "AutoModel": "modeling_spatialvla.SpatialVLAForConditionalGeneration"}, "bos_token_id": 2, "ego3d_patch_reso": 2, "eos_token_id": 1, "hidden_size": 2048, "image_token_index": 257152, "model_type": "spatial<PERSON><PERSON>", "n_freqs": 8, "num_hidden_layers": 26, "pad_token_id": 0, "projection_dim": 2304, "spatial_token_num": 8194, "text_config": {"architectures": ["Gemma2ForCausalLM"], "eos_token_id": [1, 107], "hidden_act": "gelu_pytorch_tanh", "hidden_size": 2304, "intermediate_size": 9216, "model_type": "gemma2", "num_hidden_layers": 26, "num_image_tokens": 256, "num_key_value_heads": 4, "tie_word_embeddings": false, "torch_dtype": "bfloat16", "vocab_size": 265347}, "torch_dtype": "bfloat16", "transformers_version": "4.47.0", "use_spatial_token": true, "use_vision_zoe": true, "vision_config": {"hidden_size": 1152, "intermediate_size": 4304, "model_type": "siglip_vision_model", "num_attention_heads": 16, "num_hidden_layers": 27, "num_image_tokens": 256, "num_positions": 256, "patch_size": 14, "projection_dim": 2304, "torch_dtype": "bfloat16", "vision_use_head": false}, "vision_zoe_config": {"_attn_implementation_autoset": false, "_name_or_path": "Intel/zoedepth-nyu-kitti", "add_cross_attention": false, "add_projection": false, "architectures": ["ZoeDepthForDepthEstimation"], "attractor_alpha": 1000, "attractor_gamma": 2, "attractor_kind": "mean", "backbone": null, "backbone_config": {"_attn_implementation_autoset": false, "_name_or_path": "", "add_cross_attention": false, "add_fpn": false, "architectures": null, "attention_probs_dropout_prob": 0.0, "auxiliary_channels": 256, "auxiliary_concat_input": false, "auxiliary_loss_weight": 0.4, "auxiliary_num_convs": 1, "bad_words_ids": null, "begin_suppress_tokens": null, "bos_token_id": null, "chunk_size_feed_forward": 0, "cross_attention_hidden_size": null, "decoder_start_token_id": null, "diversity_penalty": 0.0, "do_sample": false, "drop_path_rate": 0.1, "early_stopping": false, "encoder_no_repeat_ngram_size": 0, "eos_token_id": null, "exponential_decay_length_penalty": null, "finetuning_task": null, "forced_bos_token_id": null, "forced_eos_token_id": null, "hidden_act": "gelu", "hidden_dropout_prob": 0.0, "hidden_size": 1024, "id2label": {"0": "LABEL_0", "1": "LABEL_1"}, "image_size": 384, "initializer_range": 0.02, "intermediate_size": 4096, "is_decoder": false, "is_encoder_decoder": false, "label2id": {"LABEL_0": 0, "LABEL_1": 1}, "layer_norm_eps": 1e-12, "layer_scale_init_value": 0.1, "length_penalty": 1.0, "max_length": 20, "min_length": 0, "model_type": "beit", "no_repeat_ngram_size": 0, "num_attention_heads": 16, "num_beam_groups": 1, "num_beams": 1, "num_channels": 3, "num_hidden_layers": 24, "num_return_sequences": 1, "out_features": ["stage6", "stage12", "stage18", "stage24"], "out_indices": [6, 12, 18, 24], "output_attentions": false, "output_hidden_states": false, "output_scores": false, "pad_token_id": null, "patch_size": 16, "pool_scales": [1, 2, 3, 6], "prefix": null, "problem_type": null, "pruned_heads": {}, "remove_invalid_values": false, "repetition_penalty": 1.0, "reshape_hidden_states": false, "return_dict": true, "return_dict_in_generate": false, "semantic_loss_ignore_index": 255, "sep_token_id": null, "stage_names": ["stem", "stage1", "stage2", "stage3", "stage4", "stage5", "stage6", "stage7", "stage8", "stage9", "stage10", "stage11", "stage12", "stage13", "stage14", "stage15", "stage16", "stage17", "stage18", "stage19", "stage20", "stage21", "stage22", "stage23", "stage24"], "suppress_tokens": null, "task_specific_params": null, "temperature": 1.0, "tf_legacy_loss": false, "tie_encoder_decoder": false, "tie_word_embeddings": true, "tokenizer_class": null, "top_k": 50, "top_p": 1.0, "torch_dtype": null, "torchscript": false, "typical_p": 1.0, "use_absolute_position_embeddings": false, "use_auxiliary_head": true, "use_bfloat16": false, "use_mask_token": false, "use_mean_pooling": true, "use_relative_position_bias": true, "use_shared_relative_position_bias": false, "vocab_size": 8192}, "backbone_hidden_size": 1024, "bad_words_ids": null, "batch_norm_eps": 1e-05, "begin_suppress_tokens": null, "bin_centers_type": "softplus", "bin_configurations": [{"max_depth": 10.0, "min_depth": 0.001, "n_bins": 64, "name": "nyu"}, {"max_depth": 80.0, "min_depth": 0.001, "n_bins": 64, "name": "kitti"}], "bin_embedding_dim": 128, "bos_token_id": null, "bottleneck_features": 256, "chunk_size_feed_forward": 0, "cross_attention_hidden_size": null, "decoder_start_token_id": null, "diversity_penalty": 0.0, "do_sample": false, "early_stopping": false, "encoder_no_repeat_ngram_size": 0, "eos_token_id": null, "exponential_decay_length_penalty": null, "finetuning_task": null, "forced_bos_token_id": null, "forced_eos_token_id": null, "fusion_hidden_size": 256, "head_in_index": -1, "hidden_act": "gelu", "id2label": {"0": "LABEL_0", "1": "LABEL_1"}, "initializer_range": 0.02, "is_decoder": false, "is_encoder_decoder": false, "label2id": {"LABEL_0": 0, "LABEL_1": 1}, "length_penalty": 1.0, "max_length": 20, "max_temp": 50.0, "min_length": 0, "min_temp": 0.0212, "model_type": "zoedepth", "neck_hidden_sizes": [256, 512, 1024, 1024], "no_repeat_ngram_size": 0, "num_attractors": [16, 8, 4, 1], "num_beam_groups": 1, "num_beams": 1, "num_patch_transformer_layers": 4, "num_relative_features": 32, "num_return_sequences": 1, "output_attentions": false, "output_hidden_states": false, "output_scores": false, "pad_token_id": null, "patch_transformer_hidden_size": 128, "patch_transformer_intermediate_size": 1024, "patch_transformer_num_attention_heads": 4, "prefix": null, "problem_type": null, "pruned_heads": {}, "readout_type": "project", "reassemble_factors": [4, 2, 1, 0.5], "remove_invalid_values": false, "repetition_penalty": 1.0, "return_dict": true, "return_dict_in_generate": false, "sep_token_id": null, "suppress_tokens": null, "task_specific_params": null, "temperature": 1.0, "tf_legacy_loss": false, "tie_encoder_decoder": false, "tie_word_embeddings": true, "tokenizer_class": null, "top_k": 50, "top_p": 1.0, "torch_dtype": "bfloat16", "torchscript": false, "typical_p": 1.0, "use_batch_norm_in_fusion_residual": false, "use_bfloat16": false, "use_bias_in_fusion_residual": null, "use_pretrained_backbone": false}}