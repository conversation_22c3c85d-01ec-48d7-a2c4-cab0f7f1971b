{"action_chunk_size": 4, "action_config": {"distribution": "gaussian", "num_bins": {"gripper": 2, "rotation": {"pitch_bins": 16, "roll_bins": 16, "yaw_bins": 16}, "total": 8194, "translation": {"phi_bins": 32, "r_bins": 8, "theta_bins": 16}}, "use_spherical": true}, "auto_map": {"AutoProcessor": "processing_spatialvla.SpatialVLAProcessor"}, "bin_policy": {"rotation": {"pitch_bins": [-1.0, -0.6785015894338633, -0.516796358161167, -0.3978678314258641, -0.29907867426319246, -0.21158608510441518, -0.13081651669135252, -0.05392877158612959, 0.02113881590329744, 0.0961313749999302, 0.17278161860263358, 0.25310821063971767, 0.33985580585203445, 0.4373796767941653, 0.5539451994131283, 0.7100308525313351, 0.9999999999999999], "roll_bins": [-1.0, -0.7121298287894609, -0.5564581819056097, -0.440071773405789, -0.3426461358467384, -0.25595819395001274, -0.17566893098554964, -0.09904102149491184, -0.024059205927849478, 0.05100802578115137, 0.12790631705350436, 0.20869987492610076, 0.2962359118858219, 0.3951018734752948, 0.5141779624401348, 0.6762450862353777, 1.0], "yaw_bins": [-1.0, -0.6910047644696934, -0.5313988287371314, -0.4133376866679583, -0.3150057290436059, -0.22777658299365705, -0.14715771012527992, -0.07034330907230311, 0.004712965738136004, 0.07975252682496348, 0.15651401950954372, 0.23703420508371892, 0.32409736463921823, 0.4221473708283458, 0.5396818128475004, 0.6980345545587262, 1.0]}, "translation": {"phi_bins": [-3.1415926535897927, -2.5597806593194092, -2.1899702111786126, -1.9071489188814448, -1.6724463283141142, -1.4683467869586326, -1.2853487663890668, -1.1176672338183495, -0.961484031585327, -0.8141204989748655, -0.6736024210639718, -0.5384120746595923, -0.40733740832383114, -0.279375002438531, -0.15366425283265983, -0.029440234757304742, 0.0940021938080639, 0.2173378027339352, 0.34123726674747146, 0.46639302836823826, 0.5935473848733163, 0.7235258808185444, 0.857280204661428, 0.9959469801163238, 1.1409329906705301, 1.2940454053271015, 1.4577019170652383, 1.6352913749303837, 1.8318407243899377, 2.0553733807372363, 2.320069275631962, 2.6552436426949604, 3.141592653589793], "r_bins": [2.220446049250313e-16, 0.19677118231539265, 0.3506298590504556, 0.4881976731379496, 0.621970275186659, 0.7620978861167458, 0.9228346010157172, 1.1393317208802278, 1.7320508075688767], "theta_bins": [0.0, 0.7067187338585303, 0.9814199309359143, 1.1752042640550222, 1.3331175751173345, 1.4713205387280388, 1.5977846301055496, 1.7172771763957553, 1.8331248472067783, 1.9480194771467687, 2.0644993054216925, 2.1853608246107656, 2.314189357400805, 2.456314355008026, 2.621028843347318, 2.828352346005421, 3.141592653589793]}}, "intrinsic_config": {"bridge_orig/1.0.0": {"height": 480, "intrinsic": [[623.588, 0, 319.501], [0, 623.588, 239.545], [0, 0, 1]], "width": 640}, "default": {"height": 480, "intrinsic": [[623.588, 0, 319.501], [0, 623.588, 239.545], [0, 0, 1]], "width": 640}}, "num_obs_steps": 1, "obs_delta": 1, "processor_class": "SpatialVLAProcessor", "statistics": {"fractal20220817_data/0.1.0": {"action": {"mean": [0.006987507455050945, 0.006265853065997362, -0.012625162489712238, 0.04333285242319107, -0.005756276659667492, 0.0009130403632298112, 0.5354204773902893], "std": [0.06921109557151794, 0.05970889702439308, 0.0735311210155487, 0.1561058759689331, 0.1316441297531128, 0.14593777060508728, 0.49711623787879944], "max": [2.9984593391418457, 22.09052848815918, 2.7507524490356445, 1.570636510848999, 1.5321086645126343, 1.5691522359848022, 1.0], "min": [-2.0204520225524902, -5.497899532318115, -2.031663417816162, -1.569917917251587, -1.569892168045044, -1.570419430732727, 0.0], "q01": [-0.22453527510166169, -0.14820013284683228, -0.231589707583189, -0.3517994859814644, -0.4193011274933815, -0.43643461108207704, 0.0], "q99": [0.17824687153100965, 0.14938379630446405, 0.21842354819178575, 0.5892666035890578, 0.35272657424211445, 0.44796681255102094, 1.0], "mask": [true, true, true, true, true, true, false]}, "proprio": {"mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "num_transitions": 3786400, "num_trajectories": 87212}, "kuka/0.1.0": {"action": {"mean": [-0.00046687963185831904, 0.00040137648466043174, -0.0012807906605303288, 0.0, 0.0, -0.037225183099508286, 0.4131543040275574], "std": [0.020832739770412445, 0.029158642515540123, 0.0642285868525505, 0.0, 0.0, 0.14224639534950256, 0.4908643662929535], "max": [0.1697135865688324, 0.2777623236179352, 0.43710532784461975, 0.0, 0.0, 1.9684287309646606, 1.0], "min": [-0.159867063164711, -0.2892282009124756, -0.2795473635196686, 0.0, 0.0, -1.9875637292861938, 0.0], "q01": [-0.06619441494345665, -0.08713878810405731, -0.15083016991615295, 0.0, 0.0, -0.5415697038173676, 0.0], "q99": [0.06601839080452929, 0.08732476785779003, 0.18168179214000715, 0.0, 0.0, 0.2923380345106127, 1.0], "mask": [true, true, true, true, true, true, false]}, "proprio": {"mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "num_transitions": 2455879, "num_trajectories": 209880}, "bridge_orig/1.0.0": {"action": {"mean": [0.00023341714404523373, 0.00013004327774979174, -0.00012762591359205544, -0.0001556579809403047, -0.00040393328526988626, 0.00023558337124995887, 0.5764582753181458], "std": [0.009765734896063805, 0.013689505867660046, 0.012667152099311352, 0.028534479439258575, 0.03063790127635002, 0.07691770792007446, 0.4973658621311188], "max": [0.41691166162490845, 0.25864794850349426, 0.21218234300613403, 3.122201919555664, 1.8618112802505493, 6.280478477478027, 1.0], "min": [-0.4007510244846344, -0.13874775171279907, -0.22553899884223938, -3.2010786533355713, -1.8618112802505493, -6.279075622558594, 0.0], "q01": [-0.02872725307941437, -0.04170349963009357, -0.026093858778476715, -0.08092105075716972, -0.09288699507713317, -0.20718276381492615, 0.0], "q99": [0.028309678435325586, 0.040855254605412394, 0.040161586627364146, 0.08192047759890528, 0.07792850524187081, 0.20382574498653397, 1.0], "mask": [true, true, true, true, true, true, false]}, "proprio": {"mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "num_transitions": 2135463, "num_trajectories": 60064}, "taco_play/0.1.0": {"action": {"mean": [-0.0038459226489067078, 0.009671436622738838, 0.01278059184551239, -0.0054037850350141525, -0.009606562554836273, -0.0024807206355035305, 0.4263913035392761], "std": [0.23254045844078064, 0.3629826307296753, 0.2869291603565216, 0.261770635843277, 0.24388927221298218, 0.5216501355171204, 0.49469029903411865], "max": [1.4915844202041626, 2.1842432022094727, 2.6836395263671875, 5.035226821899414, 2.665864944458008, 4.250768661499023, 1.0], "min": [-4.242457866668701, -3.192805051803589, -1.3371467590332031, -4.202683448791504, -2.6722638607025146, -3.3467135429382324, 0.0], "q01": [-0.7106140398979186, -1.056944659948349, -0.5878450274467468, -0.7682853937149048, -0.7180147767066956, -1.5527938604354858, 0.0], "q99": [0.6482916426658629, 1.0051310062408447, 0.9480248689651489, 0.6926478147506714, 0.6351067513227462, 1.628010264635086, 1.0], "mask": [true, true, true, true, true, true, false]}, "proprio": {"mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "num_transitions": 237798, "num_trajectories": 3603}, "jaco_play/0.1.0": {"action": {"mean": [0.0009658387862145901, -0.005800850689411163, -0.003950685728341341, 0.0, 0.0, 0.0, 0.34934908151626587], "std": [0.12234985828399658, 0.09678783267736435, 0.1115543395280838, 0.0, 0.0, 0.0, 0.47682321071624756], "max": [0.*****************, 0.*****************, 0.*****************, 0.0, 0.0, 0.0, 1.0], "min": [-0.*****************, -0.*****************, -0.*****************, 0.0, 0.0, 0.0, 0.0], "q01": [-0.*****************, -0.*****************, -0.*****************, 0.0, 0.0, 0.0, 0.0], "q99": [0.*****************, 0.*****************, 0.*****************, 0.0, 0.0, 0.0, 1.0], "mask": [true, true, true, true, true, true, false]}, "proprio": {"mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "num_transitions": 77965, "num_trajectories": 1085}, "berkeley_cable_routing/0.1.0": {"action": {"mean": [-0.*****************, 0.023608991876244545, 0.*****************, 0.0, 0.0, 0.*****************, 0.0], "std": [0.*****************, 0.*****************, 0.*****************, 0.0, 0.0, 0.****************, 0.0], "max": [0.****************, 1.0, 1.0, 0.0, 0.0, 1.0, 0.0], "min": [-0.****************, -0.****************, -0.****************, 0.0, 0.0, -1.0, 0.0], "q01": [-0.****************, -0.****************, -0.****************, 0.0, 0.0, -0.****************, 0.0], "q99": [0.*****************, 0.****************, 0.***************, 0.0, 0.0, 0.***************, 0.0], "mask": [true, true, true, true, true, true, false]}, "proprio": {"mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "num_transitions": 42328, "num_trajectories": 1647}, "roboturk/0.1.0": {"action": {"mean": [0.001444889116100967, -0.0015945355407893658, -0.0011753803119063377, 0.002301239175722003, -0.0009382442804053426, -0.00011485860886750743, 0.5746025443077087], "std": [0.0493537075817585, 0.06354564428329468, 0.06116492301225662, 0.0955340564250946, 0.08420011401176453, 0.06517910957336426, 0.4945177137851715], "max": [0.39124172925949097, 0.4601028263568878, 0.4870833456516266, 1.816888689994812, 1.8240282535552979, 1.4824820756912231, 1.0], "min": [-0.6546999216079712, -0.6365841031074524, -0.4217723608016968, -1.6695482730865479, -1.8023357391357422, -1.4630827903747559, 0.0], "q01": [-0.1342635464668274, -0.19996687173843383, -0.1482972100377083, -0.20720748245716095, -0.09676413893699647, -0.18075634717941286, 0.0], "q99": [0.14956976801157001, 0.1805950567126275, 0.18841815620660796, 0.21615413755178453, 0.09457383215427405, 0.18543301910162005, 1.0], "mask": [true, true, true, true, true, true, false]}, "proprio": {"mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "num_transitions": 187507, "num_trajectories": 1995}, "viola/0.1.0": {"action": {"mean": [0.04761853069067001, -0.029204534366726875, 0.055867329239845276, -0.0026185200549662113, 0.006867341697216034, -0.016821356490254402, 0.7323777675628662], "std": [0.39157867431640625, 0.40765219926834106, 0.40077903866767883, 0.10023998469114304, 0.08443189412355423, 0.10375089943408966, 0.442600816488266], "max": [1.0, 1.0, 1.0, 0.375, 0.36321428418159485, 0.375, 1.0], "min": [-1.0, -1.0, -1.0, -0.375, -0.375, -0.375, 0.0], "q01": [-0.9628571271896362, -1.0, -1.0, -0.26249998807907104, -0.21321429312229156, -0.3385714292526245, 0.0], "q99": [0.9114285707473755, 0.868571400642395, 1.0, 0.2817857265472412, 0.2239285707473755, 0.3557142913341522, 1.0], "mask": [true, true, true, true, true, true, false]}, "proprio": {"mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "num_transitions": 76324, "num_trajectories": 150}, "berkeley_autolab_ur5/0.1.0": {"action": {"mean": [0.0005683613708242774, 0.0012176961172372103, -0.0005296385497786105, 0.00021029777417425066, 6.069485243642703e-05, 0.0012049867073073983, 0.6298308372497559], "std": [0.011533073149621487, 0.007990497164428234, 0.009577799588441849, 0.009432999417185783, 0.016427574679255486, 0.011054049246013165, 0.482679545879364], "max": [0.019999999552965164, 0.019999999552965164, 0.019999999552965164, 0.06666667014360428, 0.06666667014360428, 0.06666667014360428, 1.0], "min": [-0.019999999552965164, -0.019999999552965164, -0.019999999552965164, -0.06666667014360428, -0.06666667014360428, -0.06666667014360428, 0.0], "q01": [-0.019999999552965164, -0.019999999552965164, -0.019999999552965164, -0.02628571353852749, -0.06666667014360428, -0.03847619146108627, 0.0], "q99": [0.019999999552965164, 0.019999999552965164, 0.019999999552965164, 0.031809523701667786, 0.06666667014360428, 0.036571428179740906, 1.0], "mask": [true, true, true, true, true, true, false]}, "proprio": {"mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "num_transitions": 97939, "num_trajectories": 1000}, "toto/0.1.0": {"action": {"mean": [0.3854214549064636, 0.007769507821649313, 0.3632742166519165, -0.665202796459198, 0.1890396624803543, 0.0329875648021698, 0.0], "std": [0.12211630493402481, 0.19378569722175598, 0.10178232192993164, 0.5725256204605103, 0.298846036195755, 0.32599160075187683, 0.0], "max": [0.6839867234230042, 0.4454185664653778, 0.7984078526496887, 2.120781660079956, 1.371164321899414, 1.4118704795837402, 0.0], "min": [0.09922284632921219, -0.5180193781852722, 0.13791072368621826, -2.635117530822754, -1.0734480619430542, -1.9282547235488892, 0.0], "q01": [0.1756722891330719, -0.3077590811252594, 0.235383919775486, -2.0908505964279174, -0.6191593289375306, -0.7488683319091797, 0.0], "q99": [0.6136963081359863, 0.33704194784164443, 0.6681221985816956, 0.7422861719131538, 0.7955395007133507, 0.740464625358582, 0.0], "mask": [true, true, true, true, true, true, false]}, "proprio": {"mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "num_transitions": 325699, "num_trajectories": 1003}, "language_table/0.1.0": {"action": {"mean": [0.00014891766477376223, -0.0005636657006107271, 0.0, 0.0, 0.0, 0.0, 1.0], "std": [0.030162859708070755, 0.04230763390660286, 0.0, 0.0, 0.0, 0.0, 0.0], "max": [0.23357294499874115, 0.24496802687644958, 0.0, 0.0, 0.0, 0.0, 1.0], "min": [-0.21989956498146057, -0.23736150562763214, 0.0, 0.0, 0.0, 0.0, 1.0], "q01": [-0.08179590478539467, -0.11795833334326744, 0.0, 0.0, 0.0, 0.0, 1.0], "q99": [0.08822273463010788, 0.1191693339496851, 0.0, 0.0, 0.0, 0.0, 1.0], "mask": [true, true, true, true, true, true, false]}, "proprio": {"mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "num_transitions": 7045476, "num_trajectories": 442226}, "stanford_hydra_dataset_converted_externally_to_rlds/0.1.0": {"action": {"mean": [0.0007790043600834906, 0.00013707877951674163, -0.000254859565757215, 0.0012903243768960238, -0.004751724191009998, 0.002692892448976636, 0.48855218291282654], "std": [0.008022183552384377, 0.009131456725299358, 0.00957438349723816, 0.04122224077582359, 0.03843001648783684, 0.046067025512456894, 0.49978113174438477], "max": [0.02499854564666748, 0.02499903365969658, 0.024999922141432762, 0.24974457919597626, 0.24997030198574066, 0.24999946355819702, 1.0], "min": [-0.024999044835567474, -0.024999700486660004, -0.02499929815530777, -0.24993225932121277, -0.2499666064977646, -0.2499932497739792, 0.0], "q01": [-0.019992006458342076, -0.02415412735193968, -0.022941758055239916, -0.11085530579090118, -0.12024572037160397, -0.13314770206809043, 0.0], "q99": [0.022886231057345868, 0.022358838934451335, 0.02410089675337076, 0.12370114490389822, 0.11323311634361738, 0.18474749639630164, 1.0], "mask": [true, true, true, true, true, true, false]}, "proprio": {"mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "num_transitions": 358234, "num_trajectories": 570}, "austin_buds_dataset_converted_externally_to_rlds/0.1.0": {"action": {"mean": [-0.07678329944610596, 0.0036849123425781727, 0.05644941329956055, 0.0, 0.0, 0.0, 0.3510494828224182], "std": [0.6367746591567993, 0.3788914680480957, 0.47796377539634705, 0.0, 0.0, 0.0, 0.4772108495235443], "max": [1.0, 1.0, 1.0, 0.0, 0.0, 0.0, 1.0], "min": [-1.0, -1.0, -1.0, 0.0, 0.0, 0.0, 0.0], "q01": [-1.0, -0.9599999785423279, -0.8714285492897034, 0.0, 0.0, 0.0, 0.0], "q99": [1.0, 0.8600000143051147, 1.0, 0.0, 0.0, 0.0, 1.0], "mask": [true, true, true, true, true, true, false]}, "proprio": {"mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "num_transitions": 34112, "num_trajectories": 50}, "nyu_franka_play_dataset_converted_externally_to_rlds/0.1.0": {"action": {"mean": [0.0010219910182058811, -0.00012002632865915075, 0.00032894135802052915, 0.0015034276293590665, -0.002198528265580535, -0.0016632305923849344, 0.7230083346366882], "std": [0.013274150900542736, 0.013215919025242329, 0.01282210648059845, 0.27324533462524414, 0.05702253058552742, 0.03917279839515686, 0.44753193855285645], "max": [0.06424188613891602, 0.07027634978294373, 0.06129661202430725, 6.281067848205566, 0.1967729926109314, 0.26377415657043457, 1.0], "min": [-0.05952230095863342, -0.07232445478439331, -0.06730806827545166, -6.278434753417969, -0.21479034423828125, -0.3627619743347168, 0.0], "q01": [-0.03199600875377655, -0.032861671447753905, -0.03368805110454559, -0.12080862045288086, -0.12175218224525451, -0.11370223641395569, 0.0], "q99": [0.03101520001888276, 0.0373908892273903, 0.03646374464035038, 0.11764093399047852, 0.1258920183777809, 0.09366151213645942, 1.0], "mask": [true, true, true, true, true, true, false]}, "proprio": {"mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "num_transitions": 44875, "num_trajectories": 456}, "furniture_bench_dataset_converted_externally_to_rlds/0.1.0": {"action": {"mean": [0.0001461071806261316, 0.0010830992832779884, 0.0006224963581189513, -0.0033032014034688473, -0.002688060747459531, 0.018242614343762398, 0.48854944109916687], "std": [0.016107233241200447, 0.014891570433974266, 0.014014236629009247, 0.05827433615922928, 0.11417083442211151, 0.33479660749435425, 0.4999157190322876], "max": [0.10000000149011612, 0.10000000149011612, 0.10000000149011612, 0.8651833534240723, 1.0909736156463623, 2.863185405731201, 1.0], "min": [-0.10495579987764359, -0.10939455777406693, -0.10000000149011612, -0.971906840801239, -1.0475432872772217, -3.06000018119812, 0.0], "q01": [-0.053988199681043625, -0.05049169331789017, -0.032499241530895236, -0.1953887003660202, -0.41674559473991396, -0.8886768388748169, 0.0], "q99": [0.05414841488003723, 0.04965164884924884, 0.060055799782276154, 0.18231668293476103, 0.39867786407470646, 0.8772023963928218, 1.0], "mask": [true, true, true, true, true, true, false]}, "proprio": {"mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "num_transitions": 3948057, "num_trajectories": 5100}, "ucsd_kitchen_dataset_converted_externally_to_rlds/0.1.0": {"action": {"mean": [410.375732421875, 116.9518814086914, 192.35031127929688, -121.22441864013672, -33.84892654418945, 50.016136169433594, 0.741813600063324], "std": [122.81488037109375, 108.80094909667969, 130.30345153808594, 116.2820053100586, 27.62191390991211, 41.02091979980469, 0.4376337230205536], "max": [678.0, 400.0, 507.0, 180.00001525878906, 6.000013828277588, 116.99998474121094, 1.0], "min": [172.0, -166.0, -99.99999237060547, -180.00001525878906, -89.0, -96.00010681152344, 0.0], "q01": [200.00001052856445, -102.31004211425781, -94.99993370056153, -180.00001525878906, -88.00001525878906, -38.999977111816406, 0.0], "q99": [637.0, 368.30999999999995, 493.0, 180.00001525878906, 0.999983012676239, 105.00001525878906, 1.0], "mask": [true, true, true, true, true, true, false]}, "proprio": {"mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "num_transitions": 3970, "num_trajectories": 150}, "austin_sailor_dataset_converted_externally_to_rlds/0.1.0": {"action": {"mean": [0.011825386434793472, 0.0064610871486365795, 0.060236409306526184, 0.0, 0.0, 0.0016465834341943264, 0.5260950326919556], "std": [0.46348854899406433, 0.41240164637565613, 0.41186293959617615, 0.0, 0.0, 0.0578608438372612, 0.49893733859062195], "max": [1.0, 1.0, 1.0, 0.0, 0.0, 0.375, 1.0], "min": [-1.0, -1.0, -1.0, 0.0, 0.0, -0.375, 0.0], "q01": [-1.0, -0.9828571677207947, -0.6000000238418579, 0.0, 0.0, -0.17249999940395355, 0.0], "q99": [1.0, 0.9457142949104309, 1.0, 0.0, 0.0, 0.17892856895923615, 1.0], "mask": [true, true, true, true, true, true, false]}, "proprio": {"mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "num_transitions": 353094, "num_trajectories": 240}, "austin_sirius_dataset_converted_externally_to_rlds/0.1.0": {"action": {"mean": [0.077476866543293, 0.031955525279045105, 0.04244735836982727, 0.0, 0.0, -0.01603454165160656, 0.43260180950164795], "std": [0.3906330168247223, 0.2998153865337372, 0.2782270312309265, 0.0, 0.0, 0.08120641857385635, 0.49528202414512634], "max": [1.0002285242080688, 0.960608720779419, 1.105179786682129, 0.0, 0.0, 0.341785728931427, 1.0], "min": [-1.0183025598526, -0.9800000190734863, -0.9774575233459473, 0.0, 0.0, -0.34607142210006714, 0.0], "q01": [-0.780905865430832, -0.5667179036140442, -0.5254343223571777, 0.0, 0.0, -0.28495091378688814, 0.0], "q99": [0.9569637751579284, 0.6971374487876891, 0.8124888157844541, 0.0, 0.0, 0.1971428543329239, 1.0], "mask": [true, true, true, true, true, true, false]}, "proprio": {"mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "num_transitions": 279939, "num_trajectories": 559}, "dlr_edan_shared_control_converted_externally_to_rlds/0.1.0": {"action": {"mean": [0.0066478196531534195, -0.0007657355745323002, 0.006522845011204481, 0.0011679773451760411, -0.006395624950528145, -0.011903021484613419, 0.6985887289047241], "std": [0.021393585950136185, 0.018142299726605415, 0.03374377265572548, 0.01743541844189167, 0.03394372761249542, 0.04641878604888916, 0.45885783433914185], "max": [0.18991442024707794, 0.0739002525806427, 0.18064819276332855, 0.0866486132144928, 0.13464981317520142, 0.16910280287265778, 1.0], "min": [-0.10054297000169754, -0.08427435159683228, -0.13533438742160797, -0.17556548118591309, -0.18485672771930695, -0.2680685818195343, 0.0], "q01": [-0.02987122368067503, -0.06013262912631035, -0.08286409199237824, -0.05924444157630205, -0.15986866518855095, -0.15636983573436739, 0.0], "q99": [0.08832092039287087, 0.042126184627413736, 0.11311905644834042, 0.0643695573508739, 0.03941855944693088, 0.156646853685379, 1.0], "mask": [true, true, true, true, true, true, false]}, "proprio": {"mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "num_transitions": 8928, "num_trajectories": 104}, "iamlab_cmu_pickup_insert_converted_externally_to_rlds/0.1.0": {"action": {"mean": [0.5274373292922974, 0.028582017868757248, 0.18712472915649414, 1.2339569330215454, 0.03226622939109802, -1.4199472665786743, 0.5550631880760193], "std": [0.08108346909284592, 0.1116756722331047, 0.07747555524110794, 2.8737244606018066, 0.02774704433977604, 2.7678685188293457, 0.4969509243965149], "max": [0.6634981632232666, 0.23428471386432648, 0.4308285415172577, 3.1415927410125732, 0.13647015392780304, 3.141592502593994, 1.0], "min": [0.3071657121181488, -0.29754969477653503, 0.06578229367733002, -3.1415927410125732, -0.04584203287959099, -3.141592502593994, 0.0], "q01": [0.3148897051811218, -0.20317550599575043, 0.06785467118024827, -3.140952730178833, -0.029743434861302376, -3.141091251373291, 0.0], "q99": [0.6472805738449097, 0.20846802592277527, 0.36855655312538155, 3.1409926891326903, 0.11424950212240226, 3.1410969257354737, 1.0], "mask": [true, true, true, true, true, true, false]}, "proprio": {"mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "num_transitions": 146241, "num_trajectories": 631}, "utaustin_mutex/0.1.0": {"action": {"mean": [0.06176406517624855, -0.005005490034818649, 0.10216782987117767, -0.03314131125807762, 0.013895022682845592, -0.011317633092403412, 0.5038976669311523], "std": [0.187501460313797, 0.4468473196029663, 0.3792876601219177, 0.14097853004932404, 0.06453699618577957, 0.11765265464782715, 0.501045286655426], "max": [1.0, 1.0, 1.0, 0.375, 0.375, 0.375, 1.0], "min": [-1.0, -1.0, -1.0, -0.375, -0.375, -0.375, 0.0], "q01": [-0.4285714328289032, -0.9800000190734863, -0.5571428537368774, -0.375, -0.15642857551574707, -0.335357129573822, 0.0], "q99": [0.5914285778999329, 0.9714285731315613, 1.0, 0.3278571367263794, 0.207857146859169, 0.25607141852378845, 1.0], "mask": [true, true, true, true, true, true, false]}, "proprio": {"mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "num_transitions": 361883, "num_trajectories": 1500}, "berkeley_fanuc_manipulation/0.1.0": {"action": {"mean": [0.0007744057802483439, -0.00031240080716088414, -0.0015001941937953234, -0.0007515158504247665, -0.00015832878125365824, 0.00014327642566058785, 0.699295699596405], "std": [0.0034070133697241545, 0.00499219074845314, 0.005344326142221689, 0.007599010597914457, 0.004081932827830315, 0.008568963967263699, 0.45868709683418274], "max": [0.009999999776482582, 0.009999999776482582, 0.009999999776482582, 0.03490658476948738, 0.03490658476948738, 0.03490658476948738, 1.0], "min": [-0.009999999776482582, -0.009999999776482582, -0.009999999776482582, -0.03490658476948738, -0.03490658476948738, -0.03490658476948738, 0.0], "q01": [-0.009999999776482582, -0.009999999776482582, -0.009999999776482582, -0.03490658476948738, 0.0, -0.03490658476948738, 0.0], "q99": [0.009999999776482582, 0.009999999776482582, 0.009999999776482582, 0.03490658476948738, 0.0, 0.03490658476948738, 1.0], "mask": [true, true, true, true, true, true, false]}, "proprio": {"mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "num_transitions": 62613, "num_trajectories": 415}, "cmu_stretch/0.1.0": {"action": {"mean": [0.0003630445571616292, 0.0, 0.0016466928645968437, 0.0, 0.0, 0.0, 0.3987048268318176], "std": [0.004081855062395334, 0.0, 0.003774340031668544, 0.0, 0.0, 0.0, 0.489638090133667], "max": [0.02338407188653946, 0.0, 0.023404927924275398, 0.0, 0.0, 0.0, 1.0], "min": [-0.019353797659277916, 0.0, -0.02019215188920498, 0.0, 0.0, 0.0, 0.0], "q01": [-0.011175686959177256, 0.0, -0.0032206363626755773, 0.0, 0.0, 0.0, 0.0], "q99": [0.014501785952597848, 0.0, 0.015056106168776728, 0.0, 0.0, 0.0, 1.0], "mask": [true, true, true, true, true, true, false]}, "proprio": {"mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "num_transitions": 25016, "num_trajectories": 135}, "bc_z/0.1.0": {"action": {"mean": [-0.009958645328879356, 0.0008958434336818755, 0.00499522453173995, 0.000297540333122015, -0.008734511211514473, -0.03068969026207924, 0.8344562649726868], "std": [0.030533093959093094, 0.0231416504830122, 0.020642085000872612, 0.04156165570020676, 0.04643021523952484, 0.07697845250368118, 0.36111101508140564], "max": [0.2165454924106598, 0.1251407265663147, 0.10772687941789627, 0.33544227480888367, 0.28117990493774414, 0.40614867210388184, 1.0], "min": [-0.1677047461271286, -0.14630407094955444, -0.10066790133714676, -0.29421567916870117, -0.32101404666900635, -0.4635624885559082, 0.0], "q01": [-0.09220654994249344, -0.06456145539879798, -0.049121275544166565, -0.11594625547528267, -0.14152548640966414, -0.2251061636209488, 0.0], "q99": [0.07628866866230968, 0.058019736707210584, 0.052540797740221024, 0.11740604028105736, 0.11703975558280955, 0.16729306846857078, 1.0], "mask": [true, true, true, true, true, true, false]}, "proprio": {"mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "num_transitions": 6015535, "num_trajectories": 43264}, "fmb_dataset/1.0.0": {"action": {"mean": [0.05902976542711258, -0.06476633995771408, -0.09787469357252121, 0.004325387068092823, 0.00028963759541511536, -0.04457257315516472, 0.7336440086364746], "std": [0.28809186816215515, 0.2820416986942291, 0.4626740515232086, 0.3266514539718628, 0.10842999070882797, 0.34400978684425354, 0.4435289800167084], "max": [1.399999976158142, 1.0, 1.399999976158142, 1.0, 1.0, 1.0, 1.0], "min": [-1.399999976158142, -1.399999976158142, -1.0, -1.0, -1.0, -1.0, 0.0], "q01": [-0.8257142901420593, -1.399999976158142, -1.0, -1.0, -0.3028571307659149, -1.0, 0.0], "q99": [1.0, 0.5257142782211304, 1.0, 1.0, 0.3400000035762787, 1.0, 1.0], "mask": [true, true, true, true, true, true, false]}, "proprio": {"mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "num_transitions": 1137459, "num_trajectories": 8612}, "dobbe/0.0.1": {"action": {"mean": [-0.00011206958151888102, 0.0011229681549593806, -0.00010193959315074608, -7.37128357286565e-05, -0.0006753374473191798, -5.664441778208129e-05, 0.6318688988685608], "std": [0.042660679668188095, 0.04428431764245033, 0.12224890291690826, 0.005388470832258463, 0.011246936395764351, 0.006288259290158749, 0.3973240256309509], "max": [38.590423583984375, 17.932697296142578, 4.843764305114746, 1.4372116327285767, 0.4340403974056244, 1.2057193517684937, 0.9998947381973267], "min": [-5.700923442840576, -21.605947494506836, -123.72489929199219, -1.7229845523834229, -0.4998578727245331, -0.8867913484573364, 1.4196479014572105e-06], "q01": [-0.01119564864784479, -0.014266146533191203, -0.0071747214533388615, -0.009444301575422287, -0.03990109823644161, -0.017422311007976532, 4.003279136668425e-05], "q99": [0.01015154086053368, 0.017181577533483497, 0.007216989761218411, 0.010380979906767595, 0.03556173853576176, 0.018032474815845446, 0.9982578039169312], "mask": [true, true, true, true, true, true, false]}, "proprio": {"mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "num_transitions": 1139911, "num_trajectories": 5208}, "droid/1.0.0": {"action": {"mean": [0.027425529435276985, -0.0026820411439985037, 0.01595238223671913, 0.0035501928068697453, -0.030532635748386383, -0.006685464642941952, 0.5860344171524048], "std": [0.25387412309646606, 0.18426834046840668, 0.22532416880130768, 0.21757009625434875, 0.22572560608386993, 0.2867794930934906, 0.4287726879119873], "max": [0.9999998211860657, 0.999991774559021, 0.9999973177909851, 0.9999874830245972, 0.9999954104423523, 0.9999998807907104, 1.0], "min": [-0.9999999403953552, -0.9999951124191284, -0.9999960660934448, -0.9999980330467224, -0.9999982118606567, -0.9999998807907104, 0.0], "q01": [-0.7776297926902771, -0.5803514122962952, -0.5795090794563293, -0.6464047729969025, -0.7041108310222626, -0.8895104378461838, 0.0], "q99": [0.7597932070493698, 0.5726242214441299, 0.7351000607013702, 0.6705610305070877, 0.6464948207139969, 0.8897542208433151, 1.0], "mask": [true, true, true, true, true, true, false]}, "proprio": {"mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "num_transitions": 27044326, "num_trajectories": 92233}, "rh20t_rlds/1.0.0": {"action": {"mean": [-5.332157638779582e+28, -1.5128827327837974e+29, -1.832736619079747e+28, 0.5735913515090942, -0.00847744569182396, -0.5566052198410034, 0.3186892569065094], "std": [Infinity, Infinity, Infinity, 2.2581026554107666, 0.1548534482717514, 2.2581026554107666, 0.39917993545532227], "max": [7.582831568163597e+35, 7.557172735451728e+35, 2.2717764477020827e+27, 3.1415927410125732, 1.5116956233978271, 3.1415927410125732, 1.0], "min": [-3.5543094244408723e+36, -8.723098019507117e+36, -9.648338287048974e+35, -3.1415927410125732, -1.5062522888183594, -3.1415927410125732, 0.0], "q01": [0.36028257966041566, -0.272584410905838, 0.005985925104469062, -3.1411514282226562, -0.5925320792198181, -3.1415159702301025, 0.0], "q99": [0.7534684538841248, 0.31738221645355225, 0.33061375379562374, 3.141425132751465, 0.47507260441780086, 3.141479730606079, 1.0], "mask": [true, true, true, true, true, true, false]}, "proprio": {"mean": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "std": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "max": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "min": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q01": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0], "q99": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]}, "num_transitions": 52644433, "num_trajectories": 104392}}}