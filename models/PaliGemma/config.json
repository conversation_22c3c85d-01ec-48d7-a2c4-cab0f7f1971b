{"_vocab_size": 257152, "architectures": ["PaliGemmaForConditionalGeneration"], "bos_token_id": 2, "eos_token_id": 1, "image_token_index": 257152, "model_type": "paligemma", "num_hidden_layers": 26, "pad_token_id": 0, "projection_dim": 2304, "text_config": {"architectures": ["Gemma2ForCausalLM"], "attn_logit_softcapping": 50.0, "cache_implementation": "hybrid", "eos_token_id": [1, 107], "final_logit_softcapping": 30.0, "hidden_act": "gelu_pytorch_tanh", "hidden_activation": "gelu_pytorch_tanh", "hidden_size": 2304, "intermediate_size": 9216, "model_type": "gemma2", "num_attention_heads": 8, "num_hidden_layers": 26, "num_image_tokens": 256, "num_key_value_heads": 4, "query_pre_attn_scalar": 256, "sliding_window": 4096, "torch_dtype": "bfloat16", "vocab_size": 257216}, "torch_dtype": "bfloat16", "transformers_version": "4.47.0.dev0", "vision_config": {"hidden_size": 1152, "intermediate_size": 4304, "model_type": "siglip_vision_model", "num_attention_heads": 16, "num_hidden_layers": 27, "num_image_tokens": 256, "num_positions": 256, "patch_size": 14, "projection_dim": 2304, "torch_dtype": "bfloat16", "vision_use_head": false}}