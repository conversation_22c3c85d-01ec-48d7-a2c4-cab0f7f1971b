{"metadata": {"total_size": 6064484832}, "weight_map": {"language_model.model.embed_tokens.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.0.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.0.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.0.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.0.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.0.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.0.post_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.0.pre_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.0.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.0.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.0.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.0.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.1.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.1.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.1.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.1.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.1.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.1.post_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.1.pre_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.1.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.1.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.1.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.1.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.10.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.10.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.10.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.10.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.10.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.10.post_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.10.pre_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.10.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.10.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.10.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.10.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.11.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.11.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.11.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.11.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.11.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.11.post_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.11.pre_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.11.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.11.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.11.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.11.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.12.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.12.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.12.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.12.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.12.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.12.post_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.12.pre_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.12.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.12.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.12.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.12.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.13.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.13.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.13.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.13.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.13.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.13.post_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.13.pre_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.13.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.13.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.13.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.13.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.14.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.14.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.14.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.14.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.14.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.14.post_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.14.pre_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.14.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.14.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.14.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.14.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.15.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.15.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.15.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.15.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.15.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.15.post_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.15.pre_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.15.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.15.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.15.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.15.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.16.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.16.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.16.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.16.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.16.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.16.post_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.16.pre_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.16.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.16.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.16.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.16.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.17.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.17.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.17.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.17.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.17.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.17.post_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.17.pre_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.17.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.17.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.17.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.17.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.18.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.18.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.18.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.18.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.18.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.18.post_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.18.pre_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.18.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.18.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.18.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.18.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.19.input_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.19.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.19.mlp.gate_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.19.mlp.up_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.19.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.19.post_feedforward_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.19.pre_feedforward_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.19.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.19.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.19.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.19.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.2.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.2.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.2.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.2.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.2.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.2.post_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.2.pre_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.2.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.2.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.2.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.2.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.20.input_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.20.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.20.mlp.gate_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.20.mlp.up_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.20.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.20.post_feedforward_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.20.pre_feedforward_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.20.self_attn.k_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.20.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.20.self_attn.q_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.20.self_attn.v_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.21.input_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.21.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.21.mlp.gate_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.21.mlp.up_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.21.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.21.post_feedforward_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.21.pre_feedforward_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.21.self_attn.k_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.21.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.21.self_attn.q_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.21.self_attn.v_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.22.input_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.22.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.22.mlp.gate_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.22.mlp.up_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.22.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.22.post_feedforward_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.22.pre_feedforward_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.22.self_attn.k_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.22.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.22.self_attn.q_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.22.self_attn.v_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.23.input_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.23.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.23.mlp.gate_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.23.mlp.up_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.23.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.23.post_feedforward_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.23.pre_feedforward_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.23.self_attn.k_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.23.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.23.self_attn.q_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.23.self_attn.v_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.24.input_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.24.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.24.mlp.gate_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.24.mlp.up_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.24.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.24.post_feedforward_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.24.pre_feedforward_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.24.self_attn.k_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.24.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.24.self_attn.q_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.24.self_attn.v_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.25.input_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.25.mlp.down_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.25.mlp.gate_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.25.mlp.up_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.25.post_attention_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.25.post_feedforward_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.25.pre_feedforward_layernorm.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.25.self_attn.k_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.25.self_attn.o_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.25.self_attn.q_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.25.self_attn.v_proj.weight": "model-00002-of-00002.safetensors", "language_model.model.layers.3.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.3.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.3.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.3.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.3.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.3.post_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.3.pre_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.3.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.3.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.3.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.3.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.4.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.4.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.4.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.4.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.4.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.4.post_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.4.pre_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.4.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.4.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.4.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.4.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.5.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.5.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.5.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.5.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.5.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.5.post_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.5.pre_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.5.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.5.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.5.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.5.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.6.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.6.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.6.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.6.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.6.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.6.post_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.6.pre_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.6.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.6.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.6.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.6.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.7.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.7.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.7.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.7.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.7.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.7.post_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.7.pre_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.7.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.7.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.7.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.7.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.8.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.8.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.8.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.8.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.8.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.8.post_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.8.pre_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.8.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.8.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.8.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.8.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.9.input_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.9.mlp.down_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.9.mlp.gate_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.9.mlp.up_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.9.post_attention_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.9.post_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.9.pre_feedforward_layernorm.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.9.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.9.self_attn.o_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.9.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.layers.9.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "language_model.model.norm.weight": "model-00002-of-00002.safetensors", "multi_modal_projector.linear.bias": "model-00001-of-00002.safetensors", "multi_modal_projector.linear.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.embeddings.patch_embedding.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.embeddings.patch_embedding.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.embeddings.position_embedding.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.0.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.0.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.0.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.0.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.0.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.0.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.0.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.0.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.1.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.1.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.1.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.1.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.1.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.1.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.1.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.1.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.10.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.10.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.10.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.10.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.10.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.10.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.10.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.10.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.11.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.11.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.11.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.11.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.11.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.11.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.11.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.11.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.12.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.12.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.12.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.12.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.12.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.12.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.12.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.12.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.12.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.12.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.12.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.12.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.12.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.12.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.12.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.12.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.13.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.13.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.13.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.13.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.13.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.13.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.13.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.13.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.13.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.13.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.13.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.13.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.13.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.13.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.13.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.13.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.14.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.14.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.14.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.14.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.14.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.14.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.14.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.14.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.14.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.14.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.14.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.14.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.14.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.14.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.14.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.14.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.15.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.15.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.15.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.15.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.15.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.15.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.15.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.15.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.15.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.15.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.15.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.15.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.15.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.15.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.15.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.15.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.16.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.16.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.16.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.16.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.16.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.16.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.16.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.16.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.16.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.16.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.16.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.16.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.16.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.16.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.16.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.16.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.17.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.17.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.17.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.17.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.17.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.17.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.17.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.17.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.17.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.17.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.17.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.17.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.17.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.17.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.17.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.17.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.18.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.18.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.18.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.18.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.18.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.18.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.18.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.18.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.18.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.18.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.18.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.18.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.18.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.18.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.18.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.18.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.19.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.19.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.19.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.19.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.19.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.19.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.19.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.19.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.19.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.19.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.19.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.19.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.19.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.19.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.19.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.19.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.2.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.2.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.2.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.2.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.2.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.2.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.2.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.2.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.20.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.20.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.20.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.20.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.20.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.20.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.20.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.20.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.20.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.20.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.20.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.20.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.20.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.20.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.20.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.20.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.21.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.21.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.21.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.21.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.21.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.21.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.21.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.21.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.21.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.21.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.21.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.21.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.21.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.21.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.21.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.21.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.22.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.22.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.22.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.22.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.22.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.22.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.22.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.22.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.22.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.22.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.22.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.22.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.22.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.22.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.22.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.22.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.23.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.23.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.23.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.23.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.23.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.23.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.23.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.23.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.23.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.23.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.23.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.23.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.23.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.23.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.23.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.23.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.24.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.24.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.24.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.24.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.24.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.24.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.24.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.24.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.24.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.24.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.24.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.24.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.24.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.24.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.24.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.24.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.25.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.25.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.25.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.25.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.25.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.25.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.25.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.25.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.25.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.25.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.25.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.25.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.25.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.25.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.25.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.25.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.26.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.26.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.26.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.26.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.26.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.26.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.26.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.26.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.26.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.26.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.26.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.26.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.26.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.26.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.26.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.26.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.3.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.3.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.3.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.3.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.3.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.3.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.3.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.3.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.4.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.4.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.4.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.4.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.4.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.4.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.4.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.4.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.5.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.5.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.5.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.5.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.5.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.5.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.5.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.5.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.6.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.6.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.6.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.6.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.6.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.6.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.6.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.6.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.7.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.7.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.7.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.7.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.7.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.7.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.7.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.7.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.8.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.8.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.8.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.8.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.8.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.8.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.8.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.8.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.9.layer_norm1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.9.layer_norm1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.9.layer_norm2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.9.layer_norm2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.9.mlp.fc1.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.9.mlp.fc1.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.9.mlp.fc2.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.9.mlp.fc2.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.weight": "model-00001-of-00002.safetensors", "vision_tower.vision_model.post_layernorm.bias": "model-00001-of-00002.safetensors", "vision_tower.vision_model.post_layernorm.weight": "model-00001-of-00002.safetensors"}}