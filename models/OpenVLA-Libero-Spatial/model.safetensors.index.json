{"metadata": {"total_size": 15082474368}, "weight_map": {"language_model.lm_head.weight": "model-00004-of-00004.safetensors", "language_model.model.embed_tokens.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.0.input_layernorm.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.0.mlp.down_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.0.mlp.gate_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.0.mlp.up_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.0.post_attention_layernorm.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.0.self_attn.k_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.0.self_attn.o_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.0.self_attn.q_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.0.self_attn.v_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.1.input_layernorm.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.1.mlp.down_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.1.mlp.gate_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.1.mlp.up_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.1.post_attention_layernorm.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.1.self_attn.k_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.1.self_attn.o_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.1.self_attn.q_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.1.self_attn.v_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.10.input_layernorm.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.10.mlp.down_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.10.mlp.gate_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.10.mlp.up_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.10.post_attention_layernorm.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.10.self_attn.k_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.10.self_attn.o_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.10.self_attn.q_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.10.self_attn.v_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.11.input_layernorm.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.11.mlp.down_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.11.mlp.gate_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.11.mlp.up_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.11.post_attention_layernorm.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.11.self_attn.k_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.11.self_attn.o_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.11.self_attn.q_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.11.self_attn.v_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.12.input_layernorm.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.12.mlp.down_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.12.mlp.gate_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.12.mlp.up_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.12.post_attention_layernorm.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.12.self_attn.k_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.12.self_attn.o_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.12.self_attn.q_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.12.self_attn.v_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.13.input_layernorm.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.13.mlp.down_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.13.mlp.gate_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.13.mlp.up_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.13.post_attention_layernorm.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.13.self_attn.k_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.13.self_attn.o_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.13.self_attn.q_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.13.self_attn.v_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.14.input_layernorm.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.14.mlp.down_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.14.mlp.gate_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.14.mlp.up_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.14.post_attention_layernorm.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.14.self_attn.k_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.14.self_attn.o_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.14.self_attn.q_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.14.self_attn.v_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.15.input_layernorm.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.15.mlp.down_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.15.mlp.gate_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.15.mlp.up_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.15.post_attention_layernorm.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.15.self_attn.k_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.15.self_attn.o_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.15.self_attn.q_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.15.self_attn.v_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.16.input_layernorm.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.16.mlp.down_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.16.mlp.gate_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.16.mlp.up_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.16.post_attention_layernorm.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.16.self_attn.k_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.16.self_attn.o_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.16.self_attn.q_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.16.self_attn.v_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.17.input_layernorm.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.17.mlp.down_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.17.mlp.gate_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.17.mlp.up_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.17.post_attention_layernorm.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.17.self_attn.k_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.17.self_attn.o_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.17.self_attn.q_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.17.self_attn.v_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.18.input_layernorm.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.18.mlp.down_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.18.mlp.gate_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.18.mlp.up_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.18.post_attention_layernorm.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.18.self_attn.k_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.18.self_attn.o_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.18.self_attn.q_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.18.self_attn.v_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.19.input_layernorm.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.19.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.19.mlp.gate_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.19.mlp.up_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.19.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.19.self_attn.k_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.19.self_attn.o_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.19.self_attn.q_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.19.self_attn.v_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.2.input_layernorm.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.2.mlp.down_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.2.mlp.gate_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.2.mlp.up_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.2.post_attention_layernorm.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.2.self_attn.k_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.2.self_attn.o_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.2.self_attn.q_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.2.self_attn.v_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.20.input_layernorm.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.20.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.20.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.20.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.20.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.20.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.20.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.20.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.20.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.21.input_layernorm.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.21.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.21.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.21.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.21.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.21.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.21.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.21.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.21.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.22.input_layernorm.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.22.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.22.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.22.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.22.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.22.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.22.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.22.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.22.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.23.input_layernorm.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.23.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.23.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.23.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.23.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.23.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.23.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.23.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.23.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.24.input_layernorm.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.24.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.24.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.24.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.24.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.24.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.24.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.24.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.24.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.25.input_layernorm.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.25.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.25.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.25.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.25.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.25.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.25.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.25.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.25.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.26.input_layernorm.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.26.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.26.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.26.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.26.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.26.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.26.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.26.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.26.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.27.input_layernorm.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.27.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.27.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.27.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.27.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.27.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.27.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.27.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.27.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.28.input_layernorm.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.28.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.28.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.28.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.28.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.28.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.28.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.28.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.28.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.29.input_layernorm.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.29.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.29.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.29.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.29.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.29.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.29.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.29.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.29.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.3.input_layernorm.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.3.mlp.down_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.3.mlp.gate_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.3.mlp.up_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.3.post_attention_layernorm.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.3.self_attn.k_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.3.self_attn.o_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.3.self_attn.q_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.3.self_attn.v_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.30.input_layernorm.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.30.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.30.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.30.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.30.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.30.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.30.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.30.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.30.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.31.input_layernorm.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.31.mlp.down_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.31.mlp.gate_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.31.mlp.up_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.31.post_attention_layernorm.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.31.self_attn.k_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.31.self_attn.o_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.31.self_attn.q_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.31.self_attn.v_proj.weight": "model-00003-of-00004.safetensors", "language_model.model.layers.4.input_layernorm.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.4.mlp.down_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.4.mlp.gate_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.4.mlp.up_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.4.post_attention_layernorm.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.4.self_attn.k_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.4.self_attn.o_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.4.self_attn.q_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.4.self_attn.v_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.5.input_layernorm.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.5.mlp.down_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.5.mlp.gate_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.5.mlp.up_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.5.post_attention_layernorm.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.5.self_attn.k_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.5.self_attn.o_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.5.self_attn.q_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.5.self_attn.v_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.6.input_layernorm.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.6.mlp.down_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.6.mlp.gate_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.6.mlp.up_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.6.post_attention_layernorm.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.6.self_attn.k_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.6.self_attn.o_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.6.self_attn.q_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.6.self_attn.v_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.7.input_layernorm.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.7.mlp.down_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.7.mlp.gate_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.7.mlp.up_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.7.post_attention_layernorm.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.7.self_attn.k_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.7.self_attn.o_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.7.self_attn.q_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.7.self_attn.v_proj.weight": "model-00001-of-00004.safetensors", "language_model.model.layers.8.input_layernorm.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.8.mlp.down_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.8.mlp.gate_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.8.mlp.up_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.8.post_attention_layernorm.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.8.self_attn.k_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.8.self_attn.o_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.8.self_attn.q_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.8.self_attn.v_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.9.input_layernorm.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.9.mlp.down_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.9.mlp.gate_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.9.mlp.up_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.9.post_attention_layernorm.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.9.self_attn.k_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.9.self_attn.o_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.9.self_attn.q_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.layers.9.self_attn.v_proj.weight": "model-00002-of-00004.safetensors", "language_model.model.norm.weight": "model-00003-of-00004.safetensors", "projector.fc1.bias": "model-00001-of-00004.safetensors", "projector.fc1.weight": "model-00001-of-00004.safetensors", "projector.fc2.bias": "model-00001-of-00004.safetensors", "projector.fc2.weight": "model-00001-of-00004.safetensors", "projector.fc3.bias": "model-00001-of-00004.safetensors", "projector.fc3.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.0.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.0.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.0.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.0.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.0.ls1.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.0.ls2.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.0.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.0.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.0.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.0.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.0.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.0.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.0.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.0.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.1.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.1.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.1.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.1.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.1.ls1.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.1.ls2.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.1.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.1.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.1.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.1.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.1.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.1.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.1.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.1.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.10.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.10.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.10.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.10.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.10.ls1.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.10.ls2.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.10.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.10.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.10.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.10.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.10.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.10.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.10.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.10.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.11.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.11.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.11.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.11.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.11.ls1.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.11.ls2.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.11.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.11.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.11.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.11.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.11.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.11.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.11.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.11.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.12.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.12.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.12.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.12.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.12.ls1.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.12.ls2.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.12.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.12.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.12.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.12.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.12.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.12.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.12.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.12.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.13.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.13.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.13.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.13.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.13.ls1.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.13.ls2.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.13.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.13.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.13.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.13.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.13.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.13.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.13.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.13.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.14.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.14.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.14.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.14.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.14.ls1.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.14.ls2.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.14.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.14.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.14.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.14.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.14.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.14.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.14.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.14.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.15.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.15.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.15.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.15.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.15.ls1.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.15.ls2.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.15.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.15.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.15.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.15.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.15.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.15.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.15.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.15.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.16.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.16.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.16.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.16.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.16.ls1.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.16.ls2.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.16.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.16.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.16.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.16.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.16.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.16.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.16.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.16.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.17.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.17.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.17.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.17.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.17.ls1.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.17.ls2.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.17.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.17.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.17.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.17.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.17.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.17.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.17.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.17.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.18.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.18.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.18.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.18.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.18.ls1.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.18.ls2.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.18.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.18.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.18.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.18.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.18.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.18.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.18.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.18.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.19.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.19.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.19.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.19.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.19.ls1.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.19.ls2.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.19.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.19.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.19.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.19.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.19.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.19.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.19.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.19.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.2.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.2.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.2.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.2.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.2.ls1.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.2.ls2.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.2.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.2.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.2.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.2.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.2.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.2.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.2.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.2.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.20.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.20.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.20.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.20.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.20.ls1.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.20.ls2.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.20.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.20.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.20.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.20.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.20.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.20.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.20.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.20.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.21.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.21.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.21.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.21.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.21.ls1.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.21.ls2.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.21.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.21.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.21.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.21.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.21.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.21.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.21.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.21.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.22.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.22.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.22.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.22.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.22.ls1.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.22.ls2.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.22.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.22.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.22.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.22.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.22.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.22.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.22.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.22.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.23.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.23.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.23.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.23.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.23.ls1.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.23.ls2.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.23.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.23.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.23.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.23.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.23.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.23.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.23.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.23.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.3.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.3.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.3.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.3.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.3.ls1.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.3.ls2.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.3.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.3.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.3.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.3.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.3.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.3.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.3.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.3.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.4.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.4.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.4.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.4.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.4.ls1.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.4.ls2.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.4.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.4.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.4.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.4.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.4.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.4.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.4.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.4.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.5.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.5.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.5.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.5.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.5.ls1.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.5.ls2.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.5.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.5.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.5.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.5.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.5.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.5.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.5.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.5.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.6.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.6.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.6.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.6.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.6.ls1.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.6.ls2.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.6.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.6.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.6.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.6.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.6.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.6.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.6.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.6.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.7.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.7.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.7.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.7.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.7.ls1.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.7.ls2.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.7.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.7.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.7.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.7.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.7.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.7.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.7.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.7.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.8.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.8.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.8.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.8.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.8.ls1.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.8.ls2.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.8.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.8.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.8.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.8.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.8.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.8.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.8.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.8.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.9.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.9.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.9.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.9.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.9.ls1.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.9.ls2.scale_factor": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.9.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.9.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.9.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.9.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.9.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.9.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.9.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.blocks.9.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.cls_token": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.norm.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.norm.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.patch_embed.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.patch_embed.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.pos_embed": "model-00001-of-00004.safetensors", "vision_backbone.featurizer.reg_token": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.attn_pool.kv.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.attn_pool.kv.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.attn_pool.latent": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.attn_pool.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.attn_pool.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.attn_pool.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.attn_pool.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.attn_pool.norm.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.attn_pool.norm.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.attn_pool.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.attn_pool.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.attn_pool.q.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.attn_pool.q.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.0.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.0.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.0.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.0.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.0.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.0.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.0.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.0.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.0.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.0.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.0.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.0.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.1.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.1.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.1.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.1.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.1.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.1.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.1.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.1.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.1.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.1.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.1.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.1.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.10.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.10.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.10.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.10.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.10.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.10.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.10.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.10.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.10.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.10.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.10.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.10.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.11.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.11.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.11.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.11.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.11.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.11.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.11.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.11.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.11.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.11.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.11.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.11.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.12.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.12.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.12.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.12.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.12.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.12.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.12.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.12.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.12.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.12.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.12.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.12.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.13.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.13.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.13.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.13.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.13.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.13.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.13.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.13.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.13.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.13.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.13.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.13.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.14.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.14.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.14.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.14.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.14.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.14.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.14.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.14.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.14.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.14.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.14.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.14.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.15.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.15.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.15.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.15.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.15.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.15.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.15.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.15.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.15.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.15.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.15.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.15.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.16.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.16.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.16.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.16.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.16.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.16.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.16.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.16.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.16.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.16.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.16.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.16.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.17.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.17.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.17.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.17.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.17.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.17.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.17.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.17.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.17.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.17.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.17.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.17.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.18.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.18.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.18.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.18.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.18.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.18.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.18.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.18.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.18.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.18.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.18.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.18.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.19.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.19.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.19.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.19.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.19.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.19.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.19.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.19.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.19.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.19.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.19.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.19.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.2.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.2.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.2.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.2.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.2.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.2.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.2.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.2.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.2.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.2.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.2.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.2.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.20.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.20.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.20.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.20.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.20.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.20.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.20.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.20.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.20.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.20.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.20.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.20.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.21.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.21.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.21.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.21.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.21.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.21.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.21.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.21.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.21.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.21.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.21.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.21.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.22.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.22.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.22.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.22.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.22.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.22.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.22.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.22.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.22.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.22.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.22.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.22.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.23.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.23.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.23.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.23.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.23.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.23.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.23.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.23.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.23.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.23.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.23.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.23.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.24.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.24.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.24.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.24.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.24.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.24.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.24.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.24.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.24.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.24.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.24.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.24.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.25.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.25.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.25.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.25.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.25.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.25.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.25.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.25.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.25.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.25.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.25.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.25.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.26.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.26.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.26.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.26.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.26.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.26.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.26.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.26.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.26.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.26.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.26.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.26.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.3.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.3.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.3.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.3.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.3.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.3.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.3.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.3.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.3.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.3.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.3.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.3.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.4.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.4.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.4.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.4.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.4.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.4.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.4.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.4.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.4.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.4.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.4.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.4.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.5.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.5.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.5.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.5.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.5.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.5.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.5.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.5.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.5.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.5.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.5.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.5.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.6.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.6.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.6.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.6.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.6.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.6.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.6.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.6.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.6.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.6.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.6.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.6.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.7.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.7.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.7.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.7.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.7.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.7.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.7.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.7.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.7.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.7.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.7.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.7.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.8.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.8.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.8.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.8.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.8.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.8.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.8.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.8.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.8.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.8.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.8.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.8.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.9.attn.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.9.attn.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.9.attn.qkv.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.9.attn.qkv.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.9.mlp.fc1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.9.mlp.fc1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.9.mlp.fc2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.9.mlp.fc2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.9.norm1.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.9.norm1.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.9.norm2.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.blocks.9.norm2.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.norm.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.norm.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.patch_embed.proj.bias": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.patch_embed.proj.weight": "model-00001-of-00004.safetensors", "vision_backbone.fused_featurizer.pos_embed": "model-00001-of-00004.safetensors"}}