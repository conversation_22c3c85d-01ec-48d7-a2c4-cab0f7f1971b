{"auto_map": {"AutoImageProcessor": "openvla/openvla-7b--processing_prismatic.PrismaticImageProcessor", "AutoProcessor": "openvla/openvla-7b--processing_prismatic.PrismaticProcessor"}, "image_processor_type": "PrismaticImageProcessor", "image_resize_strategy": "resize-naive", "input_sizes": [[3, 224, 224], [3, 224, 224]], "interpolations": ["bicubic", "bicubic"], "means": [[0.485, 0.456, 0.406], [0.5, 0.5, 0.5]], "processor_class": "PrismaticProcessor", "stds": [[0.229, 0.224, 0.225], [0.5, 0.5, 0.5]], "tvf_crop_params": [{"output_size": [224, 224]}, {"output_size": [224, 224]}], "tvf_do_letterbox": false, "tvf_letterbox_fill": null, "tvf_normalize_params": [{"inplace": false, "mean": [0.484375, 0.455078125, 0.40625], "std": [0.228515625, 0.2236328125, 0.224609375]}, {"inplace": false, "mean": [0.5, 0.5, 0.5], "std": [0.5, 0.5, 0.5]}], "tvf_resize_params": [{"antialias": true, "interpolation": 3, "max_size": null, "size": [224, 224]}, {"antialias": true, "interpolation": 3, "max_size": null, "size": [224, 224]}], "use_fused_vision_backbone": true}