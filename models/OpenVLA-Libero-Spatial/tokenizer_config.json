{"add_bos_token": true, "add_eos_token": false, "added_tokens_decoder": {"0": {"content": "<unk>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "1": {"content": "<s>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "2": {"content": "</s>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "32000": {"content": "<PAD>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}}, "auto_map": {"AutoProcessor": "openvla/openvla-7b--processing_prismatic.PrismaticProcessor"}, "bos_token": "<s>", "clean_up_tokenization_spaces": false, "eos_token": "</s>", "legacy": false, "model_max_length": 2048, "pad_token": "<PAD>", "padding_side": "right", "processor_class": "PrismaticProcessor", "sp_model_kwargs": {}, "tokenizer_class": "LlamaTokenizer", "unk_token": "<unk>", "use_default_system_prompt": false}