#!/usr/bin/env python3
"""
PaliGemma 完整架构可视化主程序
运行深度分析并生成交互式HTML可视化
"""

import os
import sys
import argparse
import time
import json
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from deep_architecture_analyzer import DeepArchitectureAnalyzer
from interactive_html_generator import InteractiveHTMLGenerator

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='PaliGemma 深度架构可视化')
    parser.add_argument('--model_path', type=str, 
                       default='/home/<USER>/dataset/X/models/PaliGemma',
                       help='PaliGemma模型路径')
    parser.add_argument('--image_path', type=str,
                       default='/home/<USER>/dataset/X/coke.png',
                       help='输入图像路径')
    parser.add_argument('--text_prompt', type=str,
                       default='pick coke can',
                       help='文本提示')
    parser.add_argument('--output_dir', type=str,
                       default='/home/<USER>/dataset/X/X00_Architecture',
                       help='输出目录')
    parser.add_argument('--device', type=str,
                       default='cuda:0',
                       help='计算设备')
    parser.add_argument('--skip_analysis', action='store_true',
                       help='跳过分析，直接使用现有结果生成HTML')
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("PaliGemma 深度架构可视化系统")
    print("=" * 60)
    print(f"模型路径: {args.model_path}")
    print(f"图像路径: {args.image_path}")
    print(f"文本提示: {args.text_prompt}")
    print(f"输出目录: {args.output_dir}")
    print(f"计算设备: {args.device}")
    print("=" * 60)
    
    # 确保输出目录存在
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 定义文件路径
    analysis_file = os.path.join(args.output_dir, 'architecture_analysis.json')
    html_file = os.path.join(args.output_dir, 'paligemma_architecture_visualization.html')
    
    analysis_result = None
    
    # 第一步：运行架构分析（如果需要）
    if not args.skip_analysis or not os.path.exists(analysis_file):
        print("\n🔍 第一步：运行深度架构分析...")
        print("-" * 40)
        
        try:
            # 检查输入文件
            if not os.path.exists(args.model_path):
                raise FileNotFoundError(f"模型路径不存在: {args.model_path}")
            if not os.path.exists(args.image_path):
                raise FileNotFoundError(f"图像路径不存在: {args.image_path}")
            
            # 创建分析器
            analyzer = DeepArchitectureAnalyzer(args.model_path, args.device)
            
            # 运行分析
            start_time = time.time()
            analysis_result = analyzer.analyze_architecture(args.image_path, args.text_prompt)
            analysis_time = time.time() - start_time
            
            print(f"✅ 分析完成，耗时: {analysis_time:.2f}秒")
            print(f"   - 发现节点: {len(analysis_result['nodes'])}")
            print(f"   - 发现边: {len(analysis_result['edges'])}")
            
            # 保存分析结果
            with open(analysis_file, 'w', encoding='utf-8') as f:
                json.dump(analysis_result, f, indent=2, ensure_ascii=False)
            print(f"📁 分析结果已保存: {analysis_file}")
            
        except Exception as e:
            print(f"❌ 分析失败: {e}")
            import traceback
            traceback.print_exc()
            return 1
    else:
        print("\n📂 跳过分析，加载现有结果...")
        try:
            with open(analysis_file, 'r', encoding='utf-8') as f:
                analysis_result = json.load(f)
            print(f"✅ 已加载分析结果: {analysis_file}")
        except Exception as e:
            print(f"❌ 加载分析结果失败: {e}")
            return 1
    
    # 第二步：生成交互式HTML
    print("\n🎨 第二步：生成交互式HTML可视化...")
    print("-" * 40)
    
    try:
        generator = InteractiveHTMLGenerator()
        
        start_time = time.time()
        generator.generate_html(analysis_result, html_file)
        generation_time = time.time() - start_time
        
        print(f"✅ HTML生成完成，耗时: {generation_time:.2f}秒")
        print(f"📁 HTML文件已保存: {html_file}")
        
        # 获取文件大小
        file_size = os.path.getsize(html_file) / (1024 * 1024)  # MB
        print(f"📊 文件大小: {file_size:.2f} MB")
        
    except Exception as e:
        print(f"❌ HTML生成失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    # 第三步：显示总结信息
    print("\n📋 分析总结:")
    print("-" * 40)
    
    if analysis_result:
        metadata = analysis_result.get('metadata', {})
        component_stats = analysis_result.get('component_stats', {})
        
        print(f"模型: {metadata.get('model_path', 'N/A')}")
        print(f"输入图像: {metadata.get('image_path', 'N/A')}")
        print(f"文本提示: {metadata.get('text_prompt', 'N/A')}")
        
        if 'inference_result' in analysis_result:
            inference = analysis_result['inference_result']
            if inference.get('success', False):
                output_text = inference.get('output', {}).get('generated_text', 'N/A')
                print(f"生成结果: {output_text}")
        
        print(f"\n组件统计:")
        for component, stats in component_stats.items():
            print(f"  {component}: {stats['node_count']}个节点, {stats['total_params']:,}个参数")
    
    print("\n🎉 可视化完成！")
    print(f"请在浏览器中打开: {html_file}")
    print("\n💡 使用提示:")
    print("  - 点击节点查看详细信息")
    print("  - 拖拽可以移动视图")
    print("  - 使用鼠标滚轮缩放")
    print("  - 点击'播放数据流'查看执行动画")
    print("  - 切换不同的视图模式")
    
    return 0

def check_environment():
    """检查运行环境"""
    print("🔧 检查运行环境...")
    
    # 检查Python版本
    import sys
    if sys.version_info < (3, 7):
        print("❌ 需要Python 3.7或更高版本")
        return False
    
    # 检查必要的包
    required_packages = ['torch', 'transformers', 'PIL', 'numpy']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少必要的包: {', '.join(missing_packages)}")
        print("请运行: pip install torch transformers pillow numpy")
        return False
    
    print("✅ 环境检查通过")
    return True

if __name__ == "__main__":
    # 检查环境
    if not check_environment():
        sys.exit(1)
    
    # 运行主程序
    exit_code = main()
    sys.exit(exit_code)