#!/usr/bin/env python3
"""
PaliGemma 深度架构分析器
深入分析模型的每个计算单元，捕获详细的参数信息和数据流
"""

import os
import torch
import torch.nn as nn
import numpy as np
from collections import defaultdict, OrderedDict
import json
from typing import Dict, List, Any, Tuple
from transformers import AutoProcessor, PaliGemmaForConditionalGeneration
from PIL import Image
import time
import traceback

class DeepArchitectureAnalyzer:
    """深度架构分析器 - 分析PaliGemma模型的每个计算单元"""
    
    def __init__(self, model_path: str, device: str = "cuda:0"):
        """初始化分析器"""
        self.model_path = model_path
        self.device = device
        self.model = None
        self.processor = None
        
        # 数据存储
        self.nodes = []  # 所有计算节点
        self.edges = []  # 数据流边
        self.module_info = {}  # 模块详细信息
        self.data_flow = {}  # 数据流信息
        self.hooks = []  # 注册的hooks
        
        # 计数器
        self.node_counter = 0
        self.execution_order = 0
        
        print("正在初始化深度架构分析器...")
        self._load_model()
        
    def _load_model(self):
        """加载模型和处理器"""
        try:
            print(f"正在加载模型: {self.model_path}")
            self.model = PaliGemmaForConditionalGeneration.from_pretrained(
                self.model_path,
                torch_dtype=torch.bfloat16,
                device_map=self.device,
                attn_implementation="eager"  # 确保兼容性
            )
            self.processor = AutoProcessor.from_pretrained(self.model_path)
            print("模型加载完成")
        except Exception as e:
            print(f"模型加载失败: {e}")
            raise
    
    def analyze_architecture(self, image_path: str, text_prompt: str) -> Dict[str, Any]:
        """分析模型架构和数据流"""
        print(f"开始分析架构: 图像={image_path}, 文本='{text_prompt}'")
        
        # 第一步：分析静态架构
        print("1. 分析静态架构...")
        self._analyze_static_architecture()
        
        # 第二步：运行推理并捕获数据流
        print("2. 运行推理并捕获数据流...")
        inference_result = self._run_inference_with_hooks(image_path, text_prompt)
        
        # 第三步：生成最终数据结构
        print("3. 生成分析结果...")
        result = self._generate_analysis_result(image_path, text_prompt, inference_result)
        
        print(f"分析完成: {len(self.nodes)}个节点, {len(self.edges)}条边")
        return result
    
    def _analyze_static_architecture(self):
        """分析静态架构结构"""
        print("正在分析模型静态结构...")
        
        # 遍历所有模块
        for name, module in self.model.named_modules():
            if self._is_leaf_module(module):
                node_info = self._create_node_info(name, module)
                self.nodes.append(node_info)
                self.module_info[name] = node_info
        
        # 分析模块间的连接关系
        self._analyze_module_connections()
        
        print(f"发现 {len(self.nodes)} 个计算节点")
    
    def _is_leaf_module(self, module: nn.Module) -> bool:
        """判断是否为叶子模块（最小计算单元）"""
        leaf_types = (
            nn.Linear, nn.Conv2d, nn.LayerNorm, nn.Embedding,
            nn.GELU, nn.ReLU, nn.Tanh, nn.Sigmoid, nn.Dropout,
            nn.MultiheadAttention
        )
        
        # 检查是否为基础计算层
        if isinstance(module, leaf_types):
            return True
            
        # 检查是否为自定义的RMSNorm等
        module_name = module.__class__.__name__
        if any(norm_type in module_name.lower() for norm_type in ['norm', 'rmsnorm']):
            return True
            
        # 检查是否为激活函数
        if any(act_type in module_name.lower() for act_type in ['gelu', 'relu', 'tanh', 'sigmoid']):
            return True
            
        # 检查是否没有子模块（叶子节点）
        if len(list(module.children())) == 0 and len(list(module.parameters())) > 0:
            return True
            
        return False
    
    def _create_node_info(self, name: str, module: nn.Module) -> Dict[str, Any]:
        """创建节点信息"""
        node_id = f"node_{self.node_counter}"
        self.node_counter += 1
        
        # 基本信息
        node_info = {
            'id': node_id,
            'name': name,
            'module_type': module.__class__.__name__,
            'full_name': name,
            'component': self._get_component_type(name),
            'layer_index': self._get_layer_index(name),
            'parameters': self._get_parameter_info(module),
            'position': self._calculate_position(name),
            'execution_order': -1,  # 将在推理时设置
            'input_shape': None,    # 将在推理时设置
            'output_shape': None,   # 将在推理时设置
            'data_stats': None      # 将在推理时设置
        }
        
        return node_info
    
    def _get_component_type(self, name: str) -> str:
        """获取组件类型"""
        if 'vision_tower' in name or 'vision' in name:
            return 'vision'
        elif 'multi_modal_projector' in name or 'projector' in name:
            return 'projection'
        elif 'language_model' in name or 'model.layers' in name or 'embed_tokens' in name or 'norm' in name:
            return 'language'
        else:
            return 'other'
    
    def _get_layer_index(self, name: str) -> int:
        """从模块名称中提取层索引"""
        import re
        # 查找类似 "layers.0" 或 "layer.5" 的模式
        match = re.search(r'layers?\.(\d+)', name)
        if match:
            return int(match.group(1))
        return -1
    
    def _get_parameter_info(self, module: nn.Module) -> Dict[str, Any]:
        """获取模块参数信息"""
        param_info = {
            'total_params': 0,
            'trainable_params': 0,
            'param_shapes': {},
            'param_stats': {}
        }
        
        for param_name, param in module.named_parameters(recurse=False):
            param_info['total_params'] += param.numel()
            if param.requires_grad:
                param_info['trainable_params'] += param.numel()
            
            param_info['param_shapes'][param_name] = list(param.shape)
            
            # 参数统计
            if param.numel() > 0:
                param_info['param_stats'][param_name] = {
                    'mean': float(param.data.float().mean().cpu()),
                    'std': float(param.data.float().std().cpu()),
                    'min': float(param.data.float().min().cpu()),
                    'max': float(param.data.float().max().cpu())
                }
        
        return param_info
    
    def _calculate_position(self, name: str) -> Dict[str, float]:
        """计算节点在可视化中的位置"""
        component = self._get_component_type(name)
        layer_idx = self._get_layer_index(name)
        
        # 基础X坐标（按组件类型）
        x_base = {
            'vision': 100,
            'projection': 500,
            'language': 900,
            'other': 1300
        }.get(component, 1300)
        
        # Y坐标（按层索引和模块类型）
        y_offset = 0
        if layer_idx >= 0:
            y_offset = layer_idx * 100
        
        # 根据模块类型微调位置
        if 'attn' in name.lower():
            x_offset = 0
        elif 'mlp' in name.lower() or 'feed' in name.lower():
            x_offset = 150
        elif 'norm' in name.lower():
            x_offset = 75
        else:
            x_offset = 225
        
        return {
            'x': x_base + x_offset,
            'y': 100 + y_offset
        }
    
    def _analyze_module_connections(self):
        """分析模块间的连接关系（基于命名规律推断）"""
        # 这里先创建基础的连接关系，实际的数据流将在推理时确定
        sorted_nodes = sorted(self.nodes, key=lambda x: (x['component'], x['layer_index'], x['name']))
        
        for i in range(len(sorted_nodes) - 1):
            current = sorted_nodes[i]
            next_node = sorted_nodes[i + 1]
            
            # 简单的连接规则：同一层内的模块按顺序连接
            if (current['component'] == next_node['component'] and 
                current['layer_index'] == next_node['layer_index']):
                
                edge = {
                    'id': f"edge_{len(self.edges)}",
                    'source': current['id'],
                    'target': next_node['id'],
                    'type': 'sequential',
                    'data_flow': None  # 将在推理时填充
                }
                self.edges.append(edge)    
    def _run_inference_with_hooks(self, image_path: str, text_prompt: str) -> Dict[str, Any]:
        """运行推理并通过hooks捕获数据流"""
        print("正在注册hooks...")
        self._register_data_flow_hooks()
        
        try:
            # 准备输入
            image = Image.open(image_path).convert("RGB")
            inputs = self.processor(images=image, text=text_prompt, return_tensors="pt").to(self.device)
            
            # 记录输入信息
            input_info = {
                'image_path': image_path,
                'image_size': image.size,
                'text_prompt': text_prompt,
                'input_ids_shape': list(inputs["input_ids"].shape),
                'pixel_values_shape': list(inputs["pixel_values"].shape),
                'input_tokens': inputs["input_ids"].shape[1]
            }
            
            # 运行推理
            print("正在运行推理...")
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=20,
                    do_sample=False,
                    return_dict_in_generate=True,
                    output_scores=True
                )
            
            # 解码输出
            generated_ids = outputs.sequences[0][inputs["input_ids"].shape[1]:]
            generated_text = self.processor.decode(generated_ids, skip_special_tokens=True)
            
            output_info = {
                'generated_text': generated_text,
                'output_tokens': len(generated_ids),
                'sequences_shape': list(outputs.sequences.shape)
            }
            
            return {
                'input': input_info,
                'output': output_info,
                'success': True
            }
            
        except Exception as e:
            print(f"推理过程中出错: {e}")
            traceback.print_exc()
            return {
                'input': {},
                'output': {},
                'success': False,
                'error': str(e)
            }
        finally:
            self._remove_hooks()
    
    def _register_data_flow_hooks(self):
        """注册数据流捕获hooks"""
        self.execution_order = 0
        
        for name, module in self.model.named_modules():
            if name in self.module_info:
                hook = self._create_data_flow_hook(name)
                handle = module.register_forward_hook(hook)
                self.hooks.append(handle)
    
    def _create_data_flow_hook(self, module_name: str):
        """创建数据流捕获hook"""
        def hook_fn(module, input, output):
            try:
                # 更新执行顺序
                if module_name in self.module_info:
                    self.module_info[module_name]['execution_order'] = self.execution_order
                    self.execution_order += 1
                
                # 捕获输入输出形状和统计信息
                input_info = self._extract_tensor_info(input)
                output_info = self._extract_tensor_info(output)
                
                # 更新节点信息
                if module_name in self.module_info:
                    self.module_info[module_name]['input_shape'] = input_info['shapes']
                    self.module_info[module_name]['output_shape'] = output_info['shapes']
                    self.module_info[module_name]['data_stats'] = {
                        'input_stats': input_info['stats'],
                        'output_stats': output_info['stats']
                    }
                
                # 记录数据流
                self.data_flow[module_name] = {
                    'input': input_info,
                    'output': output_info,
                    'execution_order': self.execution_order - 1
                }
                
            except Exception as e:
                print(f"Hook错误 {module_name}: {e}")
        
        return hook_fn
    
    def _extract_tensor_info(self, tensor_data) -> Dict[str, Any]:
        """提取张量信息"""
        if tensor_data is None:
            return {'shapes': [], 'stats': {}}
        
        shapes = []
        stats = {}
        
        if isinstance(tensor_data, torch.Tensor):
            tensor_data = [tensor_data]
        elif isinstance(tensor_data, (tuple, list)):
            tensor_data = list(tensor_data)
        else:
            return {'shapes': [], 'stats': {}}
        
        for i, tensor in enumerate(tensor_data):
            if isinstance(tensor, torch.Tensor) and tensor.numel() > 0:
                shapes.append(list(tensor.shape))
                
                # 计算统计信息（采样以提高性能）
                if tensor.numel() > 10000:
                    # 对大张量进行采样
                    flat_tensor = tensor.flatten()
                    indices = torch.randperm(flat_tensor.size(0))[:1000]
                    sample = flat_tensor[indices]
                else:
                    sample = tensor
                
                try:
                    stats[f'tensor_{i}'] = {
                        'mean': float(sample.float().mean().cpu()),
                        'std': float(sample.float().std().cpu()),
                        'min': float(sample.float().min().cpu()),
                        'max': float(sample.float().max().cpu()),
                        'dtype': str(tensor.dtype),
                        'device': str(tensor.device)
                    }
                except:
                    stats[f'tensor_{i}'] = {'error': 'Failed to compute stats'}
        
        return {'shapes': shapes, 'stats': stats}
    
    def _remove_hooks(self):
        """移除所有hooks"""
        for hook in self.hooks:
            hook.remove()
        self.hooks.clear()
    
    def _generate_analysis_result(self, image_path: str, text_prompt: str, inference_result: Dict) -> Dict[str, Any]:
        """生成最终的分析结果"""
        
        # 更新节点信息
        for node in self.nodes:
            name = node['name']
            if name in self.module_info:
                node.update(self.module_info[name])
        
        # 更新边信息（基于实际数据流）
        self._update_edges_with_data_flow()
        
        # 生成组件统计
        component_stats = self._generate_component_stats()
        
        # 生成层级结构
        hierarchy = self._generate_hierarchy()
        
        result = {
            'metadata': {
                'model_path': self.model_path,
                'image_path': image_path,
                'text_prompt': text_prompt,
                'analysis_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                'total_nodes': len(self.nodes),
                'total_edges': len(self.edges)
            },
            'inference_result': inference_result,
            'nodes': self.nodes,
            'edges': self.edges,
            'component_stats': component_stats,
            'hierarchy': hierarchy,
            'data_flow': self.data_flow
        }
        
        return result
    
    def _update_edges_with_data_flow(self):
        """基于实际数据流更新边信息"""
        # 根据执行顺序创建实际的数据流边
        executed_nodes = [node for node in self.nodes if node.get('execution_order', -1) >= 0]
        executed_nodes.sort(key=lambda x: x['execution_order'])
        
        # 清除旧的边
        self.edges.clear()
        
        # 创建新的边（基于执行顺序）
        for i in range(len(executed_nodes) - 1):
            current = executed_nodes[i]
            next_node = executed_nodes[i + 1]
            
            # 检查是否应该连接（基于组件和层级）
            if self._should_connect(current, next_node):
                edge = {
                    'id': f"edge_{len(self.edges)}",
                    'source': current['id'],
                    'target': next_node['id'],
                    'type': 'data_flow',
                    'execution_order': current['execution_order'],
                    'data_info': self._get_edge_data_info(current, next_node)
                }
                self.edges.append(edge)
    
    def _should_connect(self, node1: Dict, node2: Dict) -> bool:
        """判断两个节点是否应该连接"""
        # 同一组件内的连续节点
        if node1['component'] == node2['component']:
            return True
        
        # 跨组件的连接（vision -> projection -> language）
        component_order = ['vision', 'projection', 'language']
        try:
            idx1 = component_order.index(node1['component'])
            idx2 = component_order.index(node2['component'])
            return idx2 == idx1 + 1
        except ValueError:
            return False
    
    def _get_edge_data_info(self, source_node: Dict, target_node: Dict) -> Dict[str, Any]:
        """获取边的数据流信息"""
        source_name = source_node['name']
        target_name = target_node['name']
        
        edge_info = {
            'source_output': None,
            'target_input': None,
            'shape_change': False
        }
        
        if source_name in self.data_flow:
            edge_info['source_output'] = self.data_flow[source_name]['output']
        
        if target_name in self.data_flow:
            edge_info['target_input'] = self.data_flow[target_name]['input']
        
        # 检查形状变化
        if (edge_info['source_output'] and edge_info['target_input'] and
            edge_info['source_output']['shapes'] and edge_info['target_input']['shapes']):
            source_shape = edge_info['source_output']['shapes'][0] if edge_info['source_output']['shapes'] else []
            target_shape = edge_info['target_input']['shapes'][0] if edge_info['target_input']['shapes'] else []
            edge_info['shape_change'] = source_shape != target_shape
        
        return edge_info
    
    def _generate_component_stats(self) -> Dict[str, Any]:
        """生成组件统计信息"""
        stats = defaultdict(lambda: {
            'node_count': 0,
            'total_params': 0,
            'layer_count': 0,
            'module_types': defaultdict(int)
        })
        
        for node in self.nodes:
            component = node['component']
            stats[component]['node_count'] += 1
            stats[component]['total_params'] += node['parameters']['total_params']
            stats[component]['module_types'][node['module_type']] += 1
            
            if node['layer_index'] >= 0:
                stats[component]['layer_count'] = max(stats[component]['layer_count'], node['layer_index'] + 1)
        
        return dict(stats)
    
    def _generate_hierarchy(self) -> Dict[str, Any]:
        """生成层级结构"""
        hierarchy = {
            'vision': defaultdict(list),
            'projection': [],
            'language': defaultdict(list),
            'other': []
        }
        
        for node in self.nodes:
            component = node['component']
            if component in ['vision', 'language']:
                layer_idx = node['layer_index']
                if layer_idx >= 0:
                    hierarchy[component][layer_idx].append(node)
                else:
                    hierarchy[component][-1].append(node)
            else:
                hierarchy[component].append(node)
        
        return hierarchy

def main():
    """主函数 - 用于测试"""
    model_path = "/home/<USER>/dataset/X/models/PaliGemma"
    image_path = "/home/<USER>/dataset/X/avatar.png"
    text_prompt = "7+2="
    
    analyzer = DeepArchitectureAnalyzer(model_path)
    result = analyzer.analyze_architecture(image_path, text_prompt)
    
    # 保存结果
    output_path = "/home/<USER>/dataset/X/X00_Architecture/architecture_analysis.json"
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2, ensure_ascii=False)
    
    print(f"分析结果已保存到: {output_path}")

if __name__ == "__main__":
    main()