#!/usr/bin/env python3
"""
测试Prismatic VLM模型的脚本
使用本地模型和图像进行推理
"""

import os
import sys
import torch
from PIL import Image
from pathlib import Path

# 添加prismatic-vlms到Python路径
sys.path.insert(0, '/home/<USER>/dataset/X/prismatic-vlms')

from prismatic import load
from prismatic.overwatch import initialize_overwatch

# 初始化日志
overwatch = initialize_overwatch(__name__)

def test_prismatic_vlm():
    """测试Prismatic VLM模型"""
    
    # 配置路径
    model_path = "/home/<USER>/dataset/X/models/prism-dinosiglip-224px+7b/prism-dinosiglip-224px+7b"
    image_path = "/home/<USER>/dataset/X/coke.png"
    
    # 检查文件是否存在
    if not os.path.exists(model_path):
        print(f"错误：模型路径不存在: {model_path}")
        return
    
    if not os.path.exists(image_path):
        print(f"错误：图像路径不存在: {image_path}")
        return
    
    print(f"正在加载模型: {model_path}")
    print(f"正在加载图像: {image_path}")
    
    # 设置设备
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    try:
        # 加载模型
        print("正在加载Prismatic VLM模型...")
        vlm = load(model_path)
        vlm.to(device, dtype=torch.bfloat16)
        print("模型加载成功！")
        
        # 加载图像
        print("正在加载图像...")
        image = Image.open(image_path).convert("RGB")
        print(f"图像尺寸: {image.size}")
        
        # 设置提示
        user_prompt = "where is coke can"
        print(f"用户提示: {user_prompt}")
        
        # 构建提示
        prompt_builder = vlm.get_prompt_builder()
        prompt_builder.add_turn(role="human", message=user_prompt)
        prompt_text = prompt_builder.get_prompt()
        print(f"完整提示: {prompt_text}")
        
        # 生成回答
        print("正在生成回答...")
        generated_text = vlm.generate(
            image,
            prompt_text,
            do_sample=True,
            temperature=0.4,
            max_new_tokens=512,
            min_length=1,
        )
        
        print("\n" + "="*50)
        print("VLM 回答:")
        print("="*50)
        print(generated_text)
        print("="*50)
        
        return generated_text
        
    except Exception as e:
        print(f"运行时错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    print("开始测试Prismatic VLM模型...")
    result = test_prismatic_vlm()
    if result:
        print("\n测试完成！")
    else:
        print("\n测试失败！")