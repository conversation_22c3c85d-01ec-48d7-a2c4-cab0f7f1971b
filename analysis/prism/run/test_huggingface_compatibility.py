#!/usr/bin/env python3
"""
测试Prismatic VLM模型与Hugging Face Transformers的兼容性
"""

import os
import sys
import torch
from PIL import Image
from pathlib import Path

# 添加prismatic-vlms到Python路径
sys.path.insert(0, '/home/<USER>/dataset/X/prismatic-vlms')

def test_huggingface_compatibility():
    """测试与Hugging Face Transformers的兼容性"""
    
    model_path = "/home/<USER>/dataset/X/models/prism-dinosiglip-224px+7b/prism-dinosiglip-224px+7b"
    image_path = "/home/<USER>/dataset/X/coke.png"
    
    print("测试1: 检查模型文件结构")
    print("-" * 40)
    
    # 检查模型文件结构
    model_files = list(Path(model_path).glob("*"))
    print(f"模型目录内容:")
    for file in model_files:
        print(f"  - {file.name}")
    
    # 检查是否有Hugging Face标准文件
    hf_files = ["config.json", "pytorch_model.bin", "model.safetensors", "tokenizer.json", "tokenizer_config.json"]
    found_hf_files = []
    for hf_file in hf_files:
        if (Path(model_path) / hf_file).exists():
            found_hf_files.append(hf_file)
    
    print(f"\n找到的Hugging Face标准文件:")
    for file in found_hf_files:
        print(f"  ✓ {file}")
    
    missing_files = set(hf_files) - set(found_hf_files)
    if missing_files:
        print(f"\n缺失的Hugging Face标准文件:")
        for file in missing_files:
            print(f"  ✗ {file}")
    
    print("\n测试2: 尝试使用Hugging Face Transformers加载")
    print("-" * 40)
    
    try:
        from transformers import AutoModel, AutoTokenizer, AutoConfig
        
        # 尝试加载配置
        try:
            config = AutoConfig.from_pretrained(model_path)
            print("✓ 成功加载配置")
            print(f"  模型类型: {config.model_type if hasattr(config, 'model_type') else '未知'}")
        except Exception as e:
            print(f"✗ 加载配置失败: {str(e)}")
        
        # 尝试加载tokenizer
        try:
            tokenizer = AutoTokenizer.from_pretrained(model_path)
            print("✓ 成功加载tokenizer")
        except Exception as e:
            print(f"✗ 加载tokenizer失败: {str(e)}")
        
        # 尝试加载模型
        try:
            model = AutoModel.from_pretrained(model_path)
            print("✓ 成功使用AutoModel加载模型")
        except Exception as e:
            print(f"✗ 使用AutoModel加载模型失败: {str(e)}")
            
    except ImportError as e:
        print(f"✗ 导入transformers失败: {str(e)}")
    
    print("\n测试3: 检查Prismatic特定文件")
    print("-" * 40)
    
    # 检查checkpoints目录
    checkpoints_dir = Path(model_path) / "checkpoints"
    if checkpoints_dir.exists():
        print("✓ 找到checkpoints目录")
        checkpoint_files = list(checkpoints_dir.glob("*"))
        for file in checkpoint_files:
            print(f"  - {file.name}")
    else:
        print("✗ 未找到checkpoints目录")
    
    # 检查配置文件
    config_files = ["config.json", "config.yaml"]
    for config_file in config_files:
        config_path = Path(model_path) / config_file
        if config_path.exists():
            print(f"✓ 找到配置文件: {config_file}")
            # 读取并显示部分配置
            try:
                if config_file.endswith('.json'):
                    import json
                    with open(config_path, 'r') as f:
                        config_data = json.load(f)
                    print(f"  模型ID: {config_data.get('model', {}).get('model_id', '未知')}")
                    print(f"  视觉骨干: {config_data.get('model', {}).get('vision_backbone_id', '未知')}")
                    print(f"  语言模型: {config_data.get('model', {}).get('llm_backbone_id', '未知')}")
            except Exception as e:
                print(f"  读取配置文件失败: {str(e)}")
    
    print("\n测试4: 分析模型架构兼容性")
    print("-" * 40)
    
    # 分析模型是否可以转换为Hugging Face格式
    print("分析结果:")
    if found_hf_files:
        print("✓ 模型包含部分Hugging Face标准文件")
        if "config.json" in found_hf_files:
            print("✓ 有配置文件，可能支持基本的Hugging Face接口")
        if any(f in found_hf_files for f in ["pytorch_model.bin", "model.safetensors"]):
            print("✓ 有模型权重文件")
        else:
            print("✗ 缺少标准的模型权重文件")
    else:
        print("✗ 模型不符合Hugging Face标准格式")
        print("  建议: 需要进行格式转换才能使用Hugging Face Transformers")
    
    return found_hf_files

if __name__ == "__main__":
    print("开始测试Hugging Face兼容性...")
    result = test_huggingface_compatibility()
    print(f"\n测试完成！找到 {len(result)} 个Hugging Face标准文件。")