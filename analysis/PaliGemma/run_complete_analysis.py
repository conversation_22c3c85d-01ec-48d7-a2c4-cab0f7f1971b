#!/usr/bin/env python3
"""
PaliGemma 完整分析工具
整合所有可视化和分析功能的统一入口
"""

import os
import sys
import argparse
import time
from datetime import datetime

def run_interpretability_analysis(args):
    """运行可解释性分析"""
    print("=== 1. 运行可解释性分析 ===")
    try:
        from interpretability_analysis import PaliGemmaInterpreter
        
        interpreter = PaliGemmaInterpreter(args.model_path, args.device)
        results = interpreter.analyze_image_text(
            args.image_path, 
            args.text_prompt, 
            args.output_dir
        )
        interpreter.generate_detailed_report(results, args.output_dir, results['timestamp'])
        
        print("✓ 可解释性分析完成")
        return True
    except Exception as e:
        print(f"✗ 可解释性分析失败: {e}")
        return False

def run_end_to_end_flow(args):
    """运行端到端数据流分析"""
    print("\n=== 2. 运行端到端数据流分析 ===")
    try:
        from end_to_end_flow_visualizer import EndToEndFlowVisualizer
        
        visualizer = EndToEndFlowVisualizer(args.model_path, args.device)
        flow_data = visualizer.capture_data_flow(args.image_path, args.text_prompt)
        
        # 生成流程图
        flow_diagram_path = os.path.join(args.output_dir, "paligemma_end_to_end_flow.png")
        visualizer.create_flow_diagram(flow_diagram_path, flow_data)
        
        # 保存原始数据
        import json
        flow_data_path = os.path.join(args.output_dir, "flow_data.json")
        with open(flow_data_path, 'w', encoding='utf-8') as f:
            json.dump(flow_data, f, indent=2, ensure_ascii=False)

        # 生成详细报告
        report_path = os.path.join(args.output_dir, "end_to_end_flow_report.md")
        visualizer.create_detailed_flow_report(report_path, flow_data)

        # 生成交互式HTML
        try:
            from interactive_flow_visualizer import InteractiveFlowVisualizer
            interactive_visualizer = InteractiveFlowVisualizer()
            interactive_html_path = os.path.join(args.output_dir, "interactive_flow.html")
            interactive_visualizer.create_interactive_html(flow_data, args.image_path, interactive_html_path)
        except Exception as e:
            print(f"交互式可视化生成失败: {e}")
        
        print("✓ 端到端数据流分析完成")
        return True
    except Exception as e:
        print(f"✗ 端到端数据流分析失败: {e}")
        return False

def run_computation_graph(args):
    """运行计算图可视化"""
    print("\n=== 3. 运行计算图可视化 ===")
    
    # 尝试交互式计算图
    try:
        from visualize_computation_graph import PaliGemmaGraphVisualizer
        
        visualizer = PaliGemmaGraphVisualizer(args.model_path, args.device)
        visualizer.analyze_model_structure()
        visualizer.build_computation_graph(max_depth=2, include_params=True)
        
        html_path = os.path.join(args.output_dir, "paligemma_computation_graph.html")
        visualizer.create_interactive_visualization(html_path)
        
        stats = visualizer.generate_summary_report(args.output_dir)
        print("✓ 交互式计算图生成完成")
        return True
    except Exception as e:
        print(f"交互式计算图失败: {e}")
        
        # 回退到简化版本
        try:
            from simple_graph_visualizer import SimplePaliGemmaVisualizer
            
            visualizer = SimplePaliGemmaVisualizer(args.model_path, args.device)
            visualizer.analyze_model_structure()
            
            visualizer.create_architecture_diagram(
                os.path.join(args.output_dir, "paligemma_architecture.png")
            )
            visualizer.create_parameter_distribution_chart(
                os.path.join(args.output_dir, "parameter_distribution.png")
            )
            visualizer.create_layer_depth_analysis(
                os.path.join(args.output_dir, "layer_depth_analysis.png")
            )
            
            print("✓ 简化计算图生成完成")
            return True
        except Exception as e:
            print(f"✗ 计算图可视化失败: {e}")
            return False

def run_result_visualization(args):
    """运行结果可视化"""
    print("\n=== 4. 运行结果可视化 ===")
    try:
        from load_and_visualize import ResultsVisualizer
        
        visualizer = ResultsVisualizer(args.output_dir)
        available_timestamps = visualizer.list_available_results()
        
        if available_timestamps:
            latest_timestamp = available_timestamps[-1]
            results = visualizer.load_results(latest_timestamp)
            
            if 'attention_maps' in results:
                visualizer.analyze_attention_patterns(results['attention_maps'], args.output_dir)
            
            if 'taps' in results:
                visualizer.analyze_tensor_flow(results['taps'], args.output_dir)
            
            print("✓ 结果可视化完成")
            return True
        else:
            print("✗ 没有找到可视化的结果")
            return False
    except Exception as e:
        print(f"✗ 结果可视化失败: {e}")
        return False

def generate_summary_report(args, results):
    """生成总结报告"""
    print("\n=== 5. 生成总结报告 ===")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    summary_path = os.path.join(args.output_dir, f"complete_analysis_summary_{timestamp}.md")
    
    with open(summary_path, 'w', encoding='utf-8') as f:
        f.write(f"""# PaliGemma 完整分析报告

## 分析概览
- **分析时间**: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
- **模型路径**: {args.model_path}
- **输入图像**: {args.image_path}
- **文本提示**: "{args.text_prompt}"
- **输出目录**: {args.output_dir}

## 分析结果

### 1. 可解释性分析
""")
        
        if results['interpretability']:
            f.write("✅ **完成** - 生成了注意力热力图、梯度流分析、残差流分析等\n")
        else:
            f.write("❌ **失败** - 可解释性分析未能完成\n")
        
        f.write("\n### 2. 端到端数据流分析\n")
        if results['end_to_end']:
            f.write("✅ **完成** - 生成了数据流图、详细报告和交互式可视化\n")
        else:
            f.write("❌ **失败** - 端到端分析未能完成\n")
        
        f.write("\n### 3. 计算图可视化\n")
        if results['computation_graph']:
            f.write("✅ **完成** - 生成了模型结构图和参数分布图\n")
        else:
            f.write("❌ **失败** - 计算图可视化未能完成\n")
        
        f.write("\n### 4. 结果可视化\n")
        if results['result_viz']:
            f.write("✅ **完成** - 生成了注意力模式分析和张量流分析\n")
        else:
            f.write("❌ **失败** - 结果可视化未能完成\n")
        
        f.write(f"""
## 生成的文件

### 核心分析文件
- `paligemma_end_to_end_flow.png` - 端到端数据流图
- `interactive_flow.html` - 交互式数据流可视化
- `paligemma_computation_graph.html` - 交互式计算图
- `end_to_end_flow_report.md` - 详细数据流报告

### 可解释性分析文件
- `attention_heatmaps_*.png` - 注意力热力图
- `hidden_states_analysis_*.png` - 隐藏状态分析
- `gradient_flow_*.png` - 梯度流分析
- `residual_flow_*.png` - 残差流分析
- `qkv_analysis_*.png` - QKV矩阵分析

### 模型结构文件
- `paligemma_architecture.png` - 模型架构图
- `parameter_distribution.png` - 参数分布图
- `layer_depth_analysis.png` - 层深度分析

### 原始数据文件
- `taps_*.pkl` - 中间张量数据
- `attention_maps_*.npy` - 注意力权重
- `hidden_states_*.npy` - 隐藏状态
- `flow_data.json` - 数据流原始数据

## 使用建议

1. **查看交互式可视化**: 打开 `interactive_flow.html` 了解数据流过程
2. **分析注意力模式**: 查看注意力热力图了解跨模态注意力
3. **理解模型结构**: 查看计算图和架构图
4. **深入分析**: 使用pickle文件进行自定义分析

## 技术说明

本分析使用了PyTorch hooks机制捕获模型内部状态，包括：
- 前向传播中间结果
- 反向传播梯度信息
- 注意力权重和隐藏状态
- QKV矩阵和残差连接

分析结果有助于理解PaliGemma模型的工作机制和跨模态信息融合过程。
""")
    
    print(f"✓ 总结报告已生成: {summary_path}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='PaliGemma完整分析工具')
    parser.add_argument('--model_path', type=str,
                       default='/home/<USER>/dataset/X/models/PaliGemma',
                       help='模型路径')
    parser.add_argument('--image_path', type=str,
                       default='/home/<USER>/dataset/X/coke.png',
                       help='输入图像路径')
    parser.add_argument('--text_prompt', type=str,
                       default='pick coke can',
                       help='文本提示')
    parser.add_argument('--output_dir', type=str,
                       default='/home/<USER>/dataset/X/analysis/PaliGemma/results',
                       help='输出目录')
    parser.add_argument('--device', type=str, default='cuda:0',
                       help='设备')
    parser.add_argument('--skip-interpretability', action='store_true',
                       help='跳过可解释性分析')
    parser.add_argument('--skip-flow', action='store_true',
                       help='跳过数据流分析')
    parser.add_argument('--skip-graph', action='store_true',
                       help='跳过计算图可视化')
    parser.add_argument('--skip-viz', action='store_true',
                       help='跳过结果可视化')
    
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    print("🚀 PaliGemma 完整分析工具")
    print("=" * 50)
    print(f"模型路径: {args.model_path}")
    print(f"图像路径: {args.image_path}")
    print(f"文本提示: '{args.text_prompt}'")
    print(f"输出目录: {args.output_dir}")
    print(f"设备: {args.device}")
    print("=" * 50)
    
    # 记录结果
    results = {
        'interpretability': False,
        'end_to_end': False,
        'computation_graph': False,
        'result_viz': False
    }
    
    start_time = time.time()
    
    # 运行各个分析
    if not args.skip_interpretability:
        results['interpretability'] = run_interpretability_analysis(args)
    
    if not args.skip_flow:
        results['end_to_end'] = run_end_to_end_flow(args)
    
    if not args.skip_graph:
        results['computation_graph'] = run_computation_graph(args)
    
    if not args.skip_viz:
        results['result_viz'] = run_result_visualization(args)
    
    # 生成总结报告
    generate_summary_report(args, results)
    
    # 显示结果
    end_time = time.time()
    duration = end_time - start_time
    
    print("\n" + "=" * 50)
    print("🎉 分析完成!")
    print(f"⏱️  总耗时: {duration:.1f} 秒")
    print(f"📁 结果目录: {args.output_dir}")
    
    success_count = sum(results.values())
    total_count = len(results)
    print(f"✅ 成功: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("\n🌟 所有分析都已成功完成!")
        print("📖 请查看生成的HTML文件获得最佳体验:")
        print(f"   - {os.path.join(args.output_dir, 'interactive_flow.html')}")
        print(f"   - {os.path.join(args.output_dir, 'paligemma_computation_graph.html')}")
    else:
        print(f"\n⚠️  部分分析失败，请检查错误信息")

if __name__ == "__main__":
    main()
