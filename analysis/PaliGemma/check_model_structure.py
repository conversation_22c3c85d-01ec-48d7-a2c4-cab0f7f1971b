#!/usr/bin/env python3
"""
检查PaliGemma模型结构，找到正确的模块路径
"""

import torch
from transformers import PaliGemmaForConditionalGeneration

def check_model_structure():
    """检查模型结构"""
    model_path = "/home/<USER>/dataset/X/models/PaliGemma"
    
    print("加载模型...")
    model = PaliGemmaForConditionalGeneration.from_pretrained(
        model_path,
        torch_dtype=torch.bfloat16,
        device_map="cpu",  # 使用CPU避免GPU内存问题
    )
    
    print("\n=== 顶级模块 ===")
    for name, module in model.named_children():
        print(f"{name}: {type(module).__name__}")
    
    print("\n=== 查找视觉相关模块 ===")
    vision_modules = []
    for name, module in model.named_modules():
        if any(keyword in name.lower() for keyword in ['vision', 'image', 'visual', 'siglip']):
            vision_modules.append((name, type(module).__name__))
    
    for name, module_type in vision_modules[:10]:  # 只显示前10个
        print(f"{name}: {module_type}")
    
    print("\n=== 查找投影相关模块 ===")
    projection_modules = []
    for name, module in model.named_modules():
        if any(keyword in name.lower() for keyword in ['project', 'proj', 'multi_modal']):
            projection_modules.append((name, type(module).__name__))
    
    for name, module_type in projection_modules:
        print(f"{name}: {module_type}")
    
    print("\n=== 查找语言模型相关模块 ===")
    language_modules = []
    for name, module in model.named_modules():
        if any(keyword in name.lower() for keyword in ['language', 'text', 'embed', 'lm_head']):
            language_modules.append((name, type(module).__name__))
    
    for name, module_type in language_modules[:10]:  # 只显示前10个
        print(f"{name}: {module_type}")

if __name__ == "__main__":
    check_model_structure()
