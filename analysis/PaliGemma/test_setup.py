#!/usr/bin/env python3
"""
测试PaliGemma可解释性分析工具的设置
"""

import os
import sys
import torch
from PIL import Image

def test_environment():
    """测试环境设置"""
    print("=== 环境测试 ===")
    
    # 检查Python版本
    print(f"Python版本: {sys.version}")
    
    # 检查PyTorch
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA设备数量: {torch.cuda.device_count()}")
        print(f"当前CUDA设备: {torch.cuda.current_device()}")
        print(f"设备名称: {torch.cuda.get_device_name()}")
    
    # 检查必要的包
    required_packages = ['transformers', 'PIL', 'matplotlib', 'seaborn', 'numpy']
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} 已安装")
        except ImportError:
            print(f"✗ {package} 未安装")
    
    print()

def test_paths():
    """测试路径设置"""
    print("=== 路径测试 ===")
    
    paths = {
        "模型路径": "/home/<USER>/dataset/X/models/PaliGemma",
        "图像路径": "/home/<USER>/dataset/X/coke.png",
        "输出目录": "/home/<USER>/dataset/X/analysis/PaliGemma/results"
    }
    
    for name, path in paths.items():
        if os.path.exists(path):
            print(f"✓ {name}: {path}")
        else:
            print(f"✗ {name}: {path} (不存在)")
    
    print()

def test_image_loading():
    """测试图像加载"""
    print("=== 图像加载测试 ===")
    
    image_path = "/home/<USER>/dataset/X/coke.png"
    
    try:
        image = Image.open(image_path)
        print(f"✓ 图像加载成功")
        print(f"  尺寸: {image.size}")
        print(f"  模式: {image.mode}")
        print(f"  格式: {image.format}")
    except Exception as e:
        print(f"✗ 图像加载失败: {e}")
    
    print()

def test_model_loading():
    """测试模型加载（简化版）"""
    print("=== 模型加载测试 ===")
    
    model_path = "/home/<USER>/dataset/X/models/PaliGemma"
    
    try:
        from transformers import AutoProcessor
        
        # 只测试processor加载，避免加载完整模型
        processor = AutoProcessor.from_pretrained(model_path)
        print(f"✓ Processor加载成功")
        print(f"  类型: {type(processor)}")
        
    except Exception as e:
        print(f"✗ Processor加载失败: {e}")
        print("  请检查模型路径和网络连接")
    
    print()

def test_output_directory():
    """测试输出目录创建"""
    print("=== 输出目录测试 ===")
    
    output_dir = "/home/<USER>/dataset/X/analysis/PaliGemma/results"
    
    try:
        os.makedirs(output_dir, exist_ok=True)
        
        # 测试写入权限
        test_file = os.path.join(output_dir, "test_write.txt")
        with open(test_file, 'w') as f:
            f.write("test")
        
        os.remove(test_file)
        print(f"✓ 输出目录可写: {output_dir}")
        
    except Exception as e:
        print(f"✗ 输出目录测试失败: {e}")
    
    print()

def test_imports():
    """测试关键模块导入"""
    print("=== 模块导入测试 ===")
    
    try:
        from interpretability_analysis import PaliGemmaInterpreter
        print("✓ PaliGemmaInterpreter 导入成功")
    except Exception as e:
        print(f"✗ PaliGemmaInterpreter 导入失败: {e}")
    
    try:
        from load_and_visualize import ResultsVisualizer
        print("✓ ResultsVisualizer 导入成功")
    except Exception as e:
        print(f"✗ ResultsVisualizer 导入失败: {e}")
    
    print()

def main():
    """运行所有测试"""
    print("PaliGemma 可解释性分析工具 - 环境测试")
    print("=" * 50)
    
    test_environment()
    test_paths()
    test_image_loading()
    test_model_loading()
    test_output_directory()
    test_imports()
    
    print("=" * 50)
    print("测试完成！")
    print("\n如果所有测试都通过(✓)，您可以运行:")
    print("python run_interpretability.py")
    print("\n如果有测试失败(✗)，请根据错误信息进行修复。")

if __name__ == "__main__":
    main()
