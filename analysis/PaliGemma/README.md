# PaliGemma 可解释性分析工具

这个工具集用于深入分析PaliGemma模型的内部数据流动，包括注意力机制、梯度流、残差流、归一化结果以及QKV矩阵等。

## 功能特性

- **注意力分析**: 捕获和可视化每层的注意力权重，特别是跨模态注意力
- **梯度流追踪**: 观察反向传播过程中的梯度流动
- **残差流分析**: 监控残差连接中的信息流
- **QKV矩阵分析**: 详细分析Query、Key、Value矩阵的统计特性
- **隐藏状态监控**: 跟踪每层隐藏状态的变化
- **归一化效果**: 观察RMSNorm等归一化层的输出

## 文件结构

```
analysis/PaliGemma/
├── README.md                        # 本文件
├── interpretability_analysis.py     # 主要分析工具
├── run_interpretability.py          # 简化运行脚本
├── load_and_visualize.py            # 结果加载和可视化工具
├── visualize_computation_graph.py   # 交互式计算图可视化
├── simple_graph_visualizer.py       # 简化计算图可视化
├── install_dependencies.py          # 依赖安装脚本
├── test_setup.py                    # 环境测试脚本
├── run.py                           # 原始推理脚本
└── results/                         # 分析结果输出目录
    ├── analysis_summary_*.json      # 分析摘要
    ├── attention_maps_*.npy         # 注意力权重
    ├── hidden_states_*.npy          # 隐藏状态
    ├── taps_*.pkl                   # 中间张量数据
    ├── *.png                        # 可视化图表
    ├── *.html                       # 交互式可视化
    └── detailed_report_*.md         # 详细报告
```

## 环境要求

```bash
# 激活conda环境
source /home/<USER>/software/anaconda3/bin/activate spatialvla

# 确保安装了必要的包
pip install torch transformers pillow matplotlib seaborn numpy
```

## 使用方法

### 1. 环境检查和依赖安装

首先检查环境并安装必要的依赖：

```bash
cd /home/<USER>/dataset/X/analysis/PaliGemma

# 检查环境
python test_setup.py

# 安装依赖（如果需要）
python install_dependencies.py
```

### 2. 基本分析

使用默认参数运行分析：

```bash
python run_interpretability.py
```

### 3. 计算图可视化

生成交互式HTML计算图：

```bash
# 交互式可视化（需要pyvis）
python visualize_computation_graph.py

# 简化静态图表（仅需matplotlib）
python simple_graph_visualizer.py
```

### 4. 自定义参数

```bash
python run_interpretability.py \
    --model_path /path/to/your/model \
    --image_path /path/to/your/image.png \
    --text_prompt "your text prompt" \
    --output_dir ./custom_results \
    --device cuda:0
```

### 5. 加载和可视化已有结果

```bash
# 查看最新结果
python load_and_visualize.py

# 指定时间戳和层/头
python load_and_visualize.py \
    --timestamp 20241227_143022 \
    --layer 5 \
    --head 2 \
    --results_dir ./results
```

### 4. 直接使用分析类

```python
from interpretability_analysis import PaliGemmaInterpreter

# 创建分析器
interpreter = PaliGemmaInterpreter("/path/to/model")

# 执行分析
results = interpreter.analyze_image_text(
    image_path="/path/to/image.png",
    text_prompt="pick coke can",
    output_dir="./results"
)

# 访问结果
print(f"损失值: {results['loss']}")
print(f"注意力层数: {len(results['attention_maps'])}")
```

## 输出文件说明

### 1. 分析摘要 (analysis_summary_*.json)
包含基本的分析信息：
- 时间戳、输入路径、文本提示
- 损失值、模型结构信息
- 捕获的张量统计

### 2. 注意力权重 (attention_maps_*.npy)
每层的注意力权重矩阵，形状为 `[batch, heads, seq_len, seq_len]`

### 3. 隐藏状态 (hidden_states_*.npy)
每层的隐藏状态，形状为 `[batch, seq_len, hidden_dim]`

### 4. 中间张量 (taps_*.pkl)
使用pickle保存的所有中间张量，包括：
- 残差流: `layers.*/resid_in`, `layers.*/resid_out`
- 注意力输出: `*.self_attn/attn_out`
- MLP输出: `*.mlp/mlp_out`
- 归一化输出: `*.norm_out`
- QKV投影: `*.q_proj`, `*.k_proj`, `*.v_proj`
- 梯度信息: `*/grad_in_out`

### 5. 可视化图表
- `attention_heatmaps_*.png`: 注意力热力图
- `hidden_states_analysis_*.png`: 隐藏状态统计分析
- `gradient_flow_*.png`: 梯度流分析
- `residual_flow_*.png`: 残差流分析
- `qkv_analysis_*.png`: QKV矩阵分析

## 技术原理

### Hook机制
使用PyTorch的hook机制捕获中间结果：
- `register_forward_pre_hook`: 捕获模块输入
- `register_forward_hook`: 捕获模块输出
- `register_full_backward_hook`: 捕获梯度信息

### 注意力分析
- 启用 `output_attentions=True` 获取注意力权重
- 使用 `attn_implementation="eager"` 确保兼容性
- 分析跨模态注意力模式（图像token ↔ 文本token）

### 梯度追踪
- 使用 `tensor.retain_grad()` 保留非叶子节点的梯度
- 通过反向传播获取梯度流信息
- 分析梯度范数了解训练动态

## 注意事项

1. **内存使用**: 分析会消耗大量内存，建议在GPU内存充足时运行
2. **存储空间**: 中间张量数据可能很大，注意磁盘空间
3. **模型兼容性**: 主要针对PaliGemma模型设计，其他模型可能需要调整
4. **性能影响**: Hook会影响推理速度，仅用于分析目的

## 扩展功能

### 自定义Hook
```python
def custom_hook(module, input, output):
    # 自定义处理逻辑
    pass

# 添加到特定模块
model.some_module.register_forward_hook(custom_hook)
```

### 批量分析
```python
images = ["image1.png", "image2.png", "image3.png"]
prompts = ["prompt1", "prompt2", "prompt3"]

for img, prompt in zip(images, prompts):
    results = interpreter.analyze_image_text(img, prompt, output_dir)
```

## 故障排除

1. **CUDA内存不足**: 减少batch size或使用CPU
2. **模型加载失败**: 检查模型路径和权限
3. **Hook错误**: 确保模型结构匹配预期
4. **可视化问题**: 检查matplotlib后端设置

## 参考资料

- [PaliGemma官方文档](https://huggingface.co/docs/transformers/model_doc/paligemma)
- [PyTorch Hook教程](https://pytorch.org/tutorials/beginner/former_torchies/nnft_tutorial.html#forward-and-backward-function-hooks)
- [Transformers可解释性指南](https://huggingface.co/docs/transformers/main/en/model_doc/auto#transformers.AutoModel.from_pretrained)
