# PaliGemma 可解释性分析报告

**生成时间**: 20250827_104940
**图像路径**: /home/<USER>/dataset/X/coke.png
**文本提示**: pick coke can
**损失值**: 17.880987

## 模型结构信息

- 注意力层数: 26
- 隐藏状态层数: 27
- 输入序列长度: 261
- 输出维度: [1, 261, 257216]

## 捕获的中间张量

- `language_model.model.layers.0.input_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.0.mlp/mlp_out`: 1 个张量
- `language_model.model.layers.0.post_attention_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.0.post_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.0.pre_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.0.self_attn.k_proj`: 1 个张量
- `language_model.model.layers.0.self_attn.q_proj`: 1 个张量
- `language_model.model.layers.0.self_attn.v_proj`: 1 个张量
- `language_model.model.layers.0.self_attn/attn_out`: 1 个张量
- `language_model.model.layers.0/resid_in`: 1 个张量
- `language_model.model.layers.0/resid_out:0`: 1 个张量
- `language_model.model.layers.0/resid_out:1`: 1 个张量
- `language_model.model.layers.1.input_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.1.mlp/mlp_out`: 1 个张量
- `language_model.model.layers.1.post_attention_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.1.post_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.1.pre_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.1.self_attn.k_proj`: 1 个张量
- `language_model.model.layers.1.self_attn.q_proj`: 1 个张量
- `language_model.model.layers.1.self_attn.v_proj`: 1 个张量
- `language_model.model.layers.1.self_attn/attn_out`: 1 个张量
- `language_model.model.layers.1/resid_in`: 1 个张量
- `language_model.model.layers.1/resid_out:0`: 1 个张量
- `language_model.model.layers.1/resid_out:1`: 1 个张量
- `language_model.model.layers.10.input_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.10.mlp/mlp_out`: 1 个张量
- `language_model.model.layers.10.post_attention_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.10.post_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.10.pre_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.10.self_attn.k_proj`: 1 个张量
- `language_model.model.layers.10.self_attn.q_proj`: 1 个张量
- `language_model.model.layers.10.self_attn.v_proj`: 1 个张量
- `language_model.model.layers.10.self_attn/attn_out`: 1 个张量
- `language_model.model.layers.10/resid_in`: 1 个张量
- `language_model.model.layers.10/resid_out:0`: 1 个张量
- `language_model.model.layers.10/resid_out:1`: 1 个张量
- `language_model.model.layers.11.input_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.11.mlp/mlp_out`: 1 个张量
- `language_model.model.layers.11.post_attention_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.11.post_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.11.pre_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.11.self_attn.k_proj`: 1 个张量
- `language_model.model.layers.11.self_attn.q_proj`: 1 个张量
- `language_model.model.layers.11.self_attn.v_proj`: 1 个张量
- `language_model.model.layers.11.self_attn/attn_out`: 1 个张量
- `language_model.model.layers.11/resid_in`: 1 个张量
- `language_model.model.layers.11/resid_out:0`: 1 个张量
- `language_model.model.layers.11/resid_out:1`: 1 个张量
- `language_model.model.layers.12.input_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.12.mlp/mlp_out`: 1 个张量
- `language_model.model.layers.12.post_attention_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.12.post_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.12.pre_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.12.self_attn.k_proj`: 1 个张量
- `language_model.model.layers.12.self_attn.q_proj`: 1 个张量
- `language_model.model.layers.12.self_attn.v_proj`: 1 个张量
- `language_model.model.layers.12.self_attn/attn_out`: 1 个张量
- `language_model.model.layers.12/resid_in`: 1 个张量
- `language_model.model.layers.12/resid_out:0`: 1 个张量
- `language_model.model.layers.12/resid_out:1`: 1 个张量
- `language_model.model.layers.13.input_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.13.mlp/mlp_out`: 1 个张量
- `language_model.model.layers.13.post_attention_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.13.post_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.13.pre_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.13.self_attn.k_proj`: 1 个张量
- `language_model.model.layers.13.self_attn.q_proj`: 1 个张量
- `language_model.model.layers.13.self_attn.v_proj`: 1 个张量
- `language_model.model.layers.13.self_attn/attn_out`: 1 个张量
- `language_model.model.layers.13/resid_in`: 1 个张量
- `language_model.model.layers.13/resid_out:0`: 1 个张量
- `language_model.model.layers.13/resid_out:1`: 1 个张量
- `language_model.model.layers.14.input_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.14.mlp/mlp_out`: 1 个张量
- `language_model.model.layers.14.post_attention_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.14.post_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.14.pre_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.14.self_attn.k_proj`: 1 个张量
- `language_model.model.layers.14.self_attn.q_proj`: 1 个张量
- `language_model.model.layers.14.self_attn.v_proj`: 1 个张量
- `language_model.model.layers.14.self_attn/attn_out`: 1 个张量
- `language_model.model.layers.14/resid_in`: 1 个张量
- `language_model.model.layers.14/resid_out:0`: 1 个张量
- `language_model.model.layers.14/resid_out:1`: 1 个张量
- `language_model.model.layers.15.input_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.15.mlp/mlp_out`: 1 个张量
- `language_model.model.layers.15.post_attention_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.15.post_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.15.pre_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.15.self_attn.k_proj`: 1 个张量
- `language_model.model.layers.15.self_attn.q_proj`: 1 个张量
- `language_model.model.layers.15.self_attn.v_proj`: 1 个张量
- `language_model.model.layers.15.self_attn/attn_out`: 1 个张量
- `language_model.model.layers.15/resid_in`: 1 个张量
- `language_model.model.layers.15/resid_out:0`: 1 个张量
- `language_model.model.layers.15/resid_out:1`: 1 个张量
- `language_model.model.layers.16.input_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.16.mlp/mlp_out`: 1 个张量
- `language_model.model.layers.16.post_attention_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.16.post_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.16.pre_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.16.self_attn.k_proj`: 1 个张量
- `language_model.model.layers.16.self_attn.q_proj`: 1 个张量
- `language_model.model.layers.16.self_attn.v_proj`: 1 个张量
- `language_model.model.layers.16.self_attn/attn_out`: 1 个张量
- `language_model.model.layers.16/resid_in`: 1 个张量
- `language_model.model.layers.16/resid_out:0`: 1 个张量
- `language_model.model.layers.16/resid_out:1`: 1 个张量
- `language_model.model.layers.17.input_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.17.mlp/mlp_out`: 1 个张量
- `language_model.model.layers.17.post_attention_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.17.post_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.17.pre_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.17.self_attn.k_proj`: 1 个张量
- `language_model.model.layers.17.self_attn.q_proj`: 1 个张量
- `language_model.model.layers.17.self_attn.v_proj`: 1 个张量
- `language_model.model.layers.17.self_attn/attn_out`: 1 个张量
- `language_model.model.layers.17/resid_in`: 1 个张量
- `language_model.model.layers.17/resid_out:0`: 1 个张量
- `language_model.model.layers.17/resid_out:1`: 1 个张量
- `language_model.model.layers.18.input_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.18.mlp/mlp_out`: 1 个张量
- `language_model.model.layers.18.post_attention_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.18.post_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.18.pre_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.18.self_attn.k_proj`: 1 个张量
- `language_model.model.layers.18.self_attn.q_proj`: 1 个张量
- `language_model.model.layers.18.self_attn.v_proj`: 1 个张量
- `language_model.model.layers.18.self_attn/attn_out`: 1 个张量
- `language_model.model.layers.18/resid_in`: 1 个张量
- `language_model.model.layers.18/resid_out:0`: 1 个张量
- `language_model.model.layers.18/resid_out:1`: 1 个张量
- `language_model.model.layers.19.input_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.19.mlp/mlp_out`: 1 个张量
- `language_model.model.layers.19.post_attention_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.19.post_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.19.pre_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.19.self_attn.k_proj`: 1 个张量
- `language_model.model.layers.19.self_attn.q_proj`: 1 个张量
- `language_model.model.layers.19.self_attn.v_proj`: 1 个张量
- `language_model.model.layers.19.self_attn/attn_out`: 1 个张量
- `language_model.model.layers.19/resid_in`: 1 个张量
- `language_model.model.layers.19/resid_out:0`: 1 个张量
- `language_model.model.layers.19/resid_out:1`: 1 个张量
- `language_model.model.layers.2.input_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.2.mlp/mlp_out`: 1 个张量
- `language_model.model.layers.2.post_attention_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.2.post_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.2.pre_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.2.self_attn.k_proj`: 1 个张量
- `language_model.model.layers.2.self_attn.q_proj`: 1 个张量
- `language_model.model.layers.2.self_attn.v_proj`: 1 个张量
- `language_model.model.layers.2.self_attn/attn_out`: 1 个张量
- `language_model.model.layers.2/resid_in`: 1 个张量
- `language_model.model.layers.2/resid_out:0`: 1 个张量
- `language_model.model.layers.2/resid_out:1`: 1 个张量
- `language_model.model.layers.20.input_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.20.mlp/mlp_out`: 1 个张量
- `language_model.model.layers.20.post_attention_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.20.post_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.20.pre_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.20.self_attn.k_proj`: 1 个张量
- `language_model.model.layers.20.self_attn.q_proj`: 1 个张量
- `language_model.model.layers.20.self_attn.v_proj`: 1 个张量
- `language_model.model.layers.20.self_attn/attn_out`: 1 个张量
- `language_model.model.layers.20/resid_in`: 1 个张量
- `language_model.model.layers.20/resid_out:0`: 1 个张量
- `language_model.model.layers.20/resid_out:1`: 1 个张量
- `language_model.model.layers.21.input_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.21.mlp/mlp_out`: 1 个张量
- `language_model.model.layers.21.post_attention_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.21.post_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.21.pre_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.21.self_attn.k_proj`: 1 个张量
- `language_model.model.layers.21.self_attn.q_proj`: 1 个张量
- `language_model.model.layers.21.self_attn.v_proj`: 1 个张量
- `language_model.model.layers.21.self_attn/attn_out`: 1 个张量
- `language_model.model.layers.21/resid_in`: 1 个张量
- `language_model.model.layers.21/resid_out:0`: 1 个张量
- `language_model.model.layers.21/resid_out:1`: 1 个张量
- `language_model.model.layers.22.input_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.22.mlp/mlp_out`: 1 个张量
- `language_model.model.layers.22.post_attention_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.22.post_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.22.pre_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.22.self_attn.k_proj`: 1 个张量
- `language_model.model.layers.22.self_attn.q_proj`: 1 个张量
- `language_model.model.layers.22.self_attn.v_proj`: 1 个张量
- `language_model.model.layers.22.self_attn/attn_out`: 1 个张量
- `language_model.model.layers.22/resid_in`: 1 个张量
- `language_model.model.layers.22/resid_out:0`: 1 个张量
- `language_model.model.layers.22/resid_out:1`: 1 个张量
- `language_model.model.layers.23.input_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.23.mlp/mlp_out`: 1 个张量
- `language_model.model.layers.23.post_attention_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.23.post_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.23.pre_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.23.self_attn.k_proj`: 1 个张量
- `language_model.model.layers.23.self_attn.q_proj`: 1 个张量
- `language_model.model.layers.23.self_attn.v_proj`: 1 个张量
- `language_model.model.layers.23.self_attn/attn_out`: 1 个张量
- `language_model.model.layers.23/resid_in`: 1 个张量
- `language_model.model.layers.23/resid_out:0`: 1 个张量
- `language_model.model.layers.23/resid_out:1`: 1 个张量
- `language_model.model.layers.24.input_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.24.mlp/mlp_out`: 1 个张量
- `language_model.model.layers.24.post_attention_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.24.post_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.24.pre_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.24.self_attn.k_proj`: 1 个张量
- `language_model.model.layers.24.self_attn.q_proj`: 1 个张量
- `language_model.model.layers.24.self_attn.v_proj`: 1 个张量
- `language_model.model.layers.24.self_attn/attn_out`: 1 个张量
- `language_model.model.layers.24/resid_in`: 1 个张量
- `language_model.model.layers.24/resid_out:0`: 1 个张量
- `language_model.model.layers.24/resid_out:1`: 1 个张量
- `language_model.model.layers.25.input_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.25.mlp/mlp_out`: 1 个张量
- `language_model.model.layers.25.post_attention_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.25.post_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.25.pre_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.25.self_attn.k_proj`: 1 个张量
- `language_model.model.layers.25.self_attn.q_proj`: 1 个张量
- `language_model.model.layers.25.self_attn.v_proj`: 1 个张量
- `language_model.model.layers.25.self_attn/attn_out`: 1 个张量
- `language_model.model.layers.25/resid_in`: 1 个张量
- `language_model.model.layers.25/resid_out:0`: 1 个张量
- `language_model.model.layers.25/resid_out:1`: 1 个张量
- `language_model.model.layers.3.input_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.3.mlp/mlp_out`: 1 个张量
- `language_model.model.layers.3.post_attention_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.3.post_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.3.pre_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.3.self_attn.k_proj`: 1 个张量
- `language_model.model.layers.3.self_attn.q_proj`: 1 个张量
- `language_model.model.layers.3.self_attn.v_proj`: 1 个张量
- `language_model.model.layers.3.self_attn/attn_out`: 1 个张量
- `language_model.model.layers.3/resid_in`: 1 个张量
- `language_model.model.layers.3/resid_out:0`: 1 个张量
- `language_model.model.layers.3/resid_out:1`: 1 个张量
- `language_model.model.layers.4.input_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.4.mlp/mlp_out`: 1 个张量
- `language_model.model.layers.4.post_attention_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.4.post_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.4.pre_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.4.self_attn.k_proj`: 1 个张量
- `language_model.model.layers.4.self_attn.q_proj`: 1 个张量
- `language_model.model.layers.4.self_attn.v_proj`: 1 个张量
- `language_model.model.layers.4.self_attn/attn_out`: 1 个张量
- `language_model.model.layers.4/resid_in`: 1 个张量
- `language_model.model.layers.4/resid_out:0`: 1 个张量
- `language_model.model.layers.4/resid_out:1`: 1 个张量
- `language_model.model.layers.5.input_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.5.mlp/mlp_out`: 1 个张量
- `language_model.model.layers.5.post_attention_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.5.post_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.5.pre_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.5.self_attn.k_proj`: 1 个张量
- `language_model.model.layers.5.self_attn.q_proj`: 1 个张量
- `language_model.model.layers.5.self_attn.v_proj`: 1 个张量
- `language_model.model.layers.5.self_attn/attn_out`: 1 个张量
- `language_model.model.layers.5/resid_in`: 1 个张量
- `language_model.model.layers.5/resid_out:0`: 1 个张量
- `language_model.model.layers.5/resid_out:1`: 1 个张量
- `language_model.model.layers.6.input_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.6.mlp/mlp_out`: 1 个张量
- `language_model.model.layers.6.post_attention_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.6.post_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.6.pre_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.6.self_attn.k_proj`: 1 个张量
- `language_model.model.layers.6.self_attn.q_proj`: 1 个张量
- `language_model.model.layers.6.self_attn.v_proj`: 1 个张量
- `language_model.model.layers.6.self_attn/attn_out`: 1 个张量
- `language_model.model.layers.6/resid_in`: 1 个张量
- `language_model.model.layers.6/resid_out:0`: 1 个张量
- `language_model.model.layers.6/resid_out:1`: 1 个张量
- `language_model.model.layers.7.input_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.7.mlp/mlp_out`: 1 个张量
- `language_model.model.layers.7.post_attention_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.7.post_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.7.pre_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.7.self_attn.k_proj`: 1 个张量
- `language_model.model.layers.7.self_attn.q_proj`: 1 个张量
- `language_model.model.layers.7.self_attn.v_proj`: 1 个张量
- `language_model.model.layers.7.self_attn/attn_out`: 1 个张量
- `language_model.model.layers.7/resid_in`: 1 个张量
- `language_model.model.layers.7/resid_out:0`: 1 个张量
- `language_model.model.layers.7/resid_out:1`: 1 个张量
- `language_model.model.layers.8.input_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.8.mlp/mlp_out`: 1 个张量
- `language_model.model.layers.8.post_attention_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.8.post_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.8.pre_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.8.self_attn.k_proj`: 1 个张量
- `language_model.model.layers.8.self_attn.q_proj`: 1 个张量
- `language_model.model.layers.8.self_attn.v_proj`: 1 个张量
- `language_model.model.layers.8.self_attn/attn_out`: 1 个张量
- `language_model.model.layers.8/resid_in`: 1 个张量
- `language_model.model.layers.8/resid_out:0`: 1 个张量
- `language_model.model.layers.8/resid_out:1`: 1 个张量
- `language_model.model.layers.9.input_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.9.mlp/mlp_out`: 1 个张量
- `language_model.model.layers.9.post_attention_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.9.post_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.9.pre_feedforward_layernorm/norm_out`: 1 个张量
- `language_model.model.layers.9.self_attn.k_proj`: 1 个张量
- `language_model.model.layers.9.self_attn.q_proj`: 1 个张量
- `language_model.model.layers.9.self_attn.v_proj`: 1 个张量
- `language_model.model.layers.9.self_attn/attn_out`: 1 个张量
- `language_model.model.layers.9/resid_in`: 1 个张量
- `language_model.model.layers.9/resid_out:0`: 1 个张量
- `language_model.model.layers.9/resid_out:1`: 1 个张量
- `language_model.model.norm/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.0.layer_norm1/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.0.layer_norm2/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.0.mlp/mlp_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.0.self_attn.k_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.0.self_attn.q_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.0.self_attn.v_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.0.self_attn/attn_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.0/resid_in`: 1 个张量
- `vision_tower.vision_model.encoder.layers.0/resid_out:0`: 1 个张量
- `vision_tower.vision_model.encoder.layers.1.layer_norm1/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.1.layer_norm2/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.1.mlp/mlp_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.1.self_attn.k_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.1.self_attn.q_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.1.self_attn.v_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.1.self_attn/attn_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.1/resid_in`: 1 个张量
- `vision_tower.vision_model.encoder.layers.1/resid_out:0`: 1 个张量
- `vision_tower.vision_model.encoder.layers.10.layer_norm1/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.10.layer_norm2/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.10.mlp/mlp_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.10.self_attn.k_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.10.self_attn.q_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.10.self_attn.v_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.10.self_attn/attn_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.10/resid_in`: 1 个张量
- `vision_tower.vision_model.encoder.layers.10/resid_out:0`: 1 个张量
- `vision_tower.vision_model.encoder.layers.11.layer_norm1/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.11.layer_norm2/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.11.mlp/mlp_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.11.self_attn.k_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.11.self_attn.q_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.11.self_attn.v_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.11.self_attn/attn_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.11/resid_in`: 1 个张量
- `vision_tower.vision_model.encoder.layers.11/resid_out:0`: 1 个张量
- `vision_tower.vision_model.encoder.layers.12.layer_norm1/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.12.layer_norm2/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.12.mlp/mlp_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.12.self_attn.k_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.12.self_attn.q_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.12.self_attn.v_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.12.self_attn/attn_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.12/resid_in`: 1 个张量
- `vision_tower.vision_model.encoder.layers.12/resid_out:0`: 1 个张量
- `vision_tower.vision_model.encoder.layers.13.layer_norm1/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.13.layer_norm2/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.13.mlp/mlp_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.13.self_attn.k_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.13.self_attn.q_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.13.self_attn.v_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.13.self_attn/attn_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.13/resid_in`: 1 个张量
- `vision_tower.vision_model.encoder.layers.13/resid_out:0`: 1 个张量
- `vision_tower.vision_model.encoder.layers.14.layer_norm1/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.14.layer_norm2/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.14.mlp/mlp_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.14.self_attn.k_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.14.self_attn.q_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.14.self_attn.v_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.14.self_attn/attn_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.14/resid_in`: 1 个张量
- `vision_tower.vision_model.encoder.layers.14/resid_out:0`: 1 个张量
- `vision_tower.vision_model.encoder.layers.15.layer_norm1/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.15.layer_norm2/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.15.mlp/mlp_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.15.self_attn.k_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.15.self_attn.q_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.15.self_attn.v_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.15.self_attn/attn_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.15/resid_in`: 1 个张量
- `vision_tower.vision_model.encoder.layers.15/resid_out:0`: 1 个张量
- `vision_tower.vision_model.encoder.layers.16.layer_norm1/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.16.layer_norm2/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.16.mlp/mlp_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.16.self_attn.k_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.16.self_attn.q_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.16.self_attn.v_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.16.self_attn/attn_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.16/resid_in`: 1 个张量
- `vision_tower.vision_model.encoder.layers.16/resid_out:0`: 1 个张量
- `vision_tower.vision_model.encoder.layers.17.layer_norm1/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.17.layer_norm2/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.17.mlp/mlp_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.17.self_attn.k_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.17.self_attn.q_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.17.self_attn.v_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.17.self_attn/attn_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.17/resid_in`: 1 个张量
- `vision_tower.vision_model.encoder.layers.17/resid_out:0`: 1 个张量
- `vision_tower.vision_model.encoder.layers.18.layer_norm1/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.18.layer_norm2/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.18.mlp/mlp_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.18.self_attn.k_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.18.self_attn.q_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.18.self_attn.v_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.18.self_attn/attn_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.18/resid_in`: 1 个张量
- `vision_tower.vision_model.encoder.layers.18/resid_out:0`: 1 个张量
- `vision_tower.vision_model.encoder.layers.19.layer_norm1/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.19.layer_norm2/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.19.mlp/mlp_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.19.self_attn.k_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.19.self_attn.q_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.19.self_attn.v_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.19.self_attn/attn_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.19/resid_in`: 1 个张量
- `vision_tower.vision_model.encoder.layers.19/resid_out:0`: 1 个张量
- `vision_tower.vision_model.encoder.layers.2.layer_norm1/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.2.layer_norm2/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.2.mlp/mlp_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.2.self_attn.k_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.2.self_attn.q_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.2.self_attn.v_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.2.self_attn/attn_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.2/resid_in`: 1 个张量
- `vision_tower.vision_model.encoder.layers.2/resid_out:0`: 1 个张量
- `vision_tower.vision_model.encoder.layers.20.layer_norm1/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.20.layer_norm2/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.20.mlp/mlp_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.20.self_attn.k_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.20.self_attn.q_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.20.self_attn.v_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.20.self_attn/attn_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.20/resid_in`: 1 个张量
- `vision_tower.vision_model.encoder.layers.20/resid_out:0`: 1 个张量
- `vision_tower.vision_model.encoder.layers.21.layer_norm1/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.21.layer_norm2/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.21.mlp/mlp_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.21.self_attn.k_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.21.self_attn.q_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.21.self_attn.v_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.21.self_attn/attn_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.21/resid_in`: 1 个张量
- `vision_tower.vision_model.encoder.layers.21/resid_out:0`: 1 个张量
- `vision_tower.vision_model.encoder.layers.22.layer_norm1/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.22.layer_norm2/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.22.mlp/mlp_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.22.self_attn.k_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.22.self_attn.q_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.22.self_attn.v_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.22.self_attn/attn_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.22/resid_in`: 1 个张量
- `vision_tower.vision_model.encoder.layers.22/resid_out:0`: 1 个张量
- `vision_tower.vision_model.encoder.layers.23.layer_norm1/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.23.layer_norm2/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.23.mlp/mlp_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.23.self_attn.k_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.23.self_attn.q_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.23.self_attn.v_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.23.self_attn/attn_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.23/resid_in`: 1 个张量
- `vision_tower.vision_model.encoder.layers.23/resid_out:0`: 1 个张量
- `vision_tower.vision_model.encoder.layers.24.layer_norm1/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.24.layer_norm2/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.24.mlp/mlp_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.24.self_attn.k_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.24.self_attn.q_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.24.self_attn.v_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.24.self_attn/attn_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.24/resid_in`: 1 个张量
- `vision_tower.vision_model.encoder.layers.24/resid_out:0`: 1 个张量
- `vision_tower.vision_model.encoder.layers.25.layer_norm1/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.25.layer_norm2/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.25.mlp/mlp_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.25.self_attn.k_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.25.self_attn.q_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.25.self_attn.v_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.25.self_attn/attn_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.25/resid_in`: 1 个张量
- `vision_tower.vision_model.encoder.layers.25/resid_out:0`: 1 个张量
- `vision_tower.vision_model.encoder.layers.26.layer_norm1/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.26.layer_norm2/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.26.mlp/mlp_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.26.self_attn.k_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.26.self_attn.q_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.26.self_attn.v_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.26.self_attn/attn_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.26/resid_in`: 1 个张量
- `vision_tower.vision_model.encoder.layers.26/resid_out:0`: 1 个张量
- `vision_tower.vision_model.encoder.layers.3.layer_norm1/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.3.layer_norm2/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.3.mlp/mlp_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.3.self_attn.k_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.3.self_attn.q_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.3.self_attn.v_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.3.self_attn/attn_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.3/resid_in`: 1 个张量
- `vision_tower.vision_model.encoder.layers.3/resid_out:0`: 1 个张量
- `vision_tower.vision_model.encoder.layers.4.layer_norm1/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.4.layer_norm2/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.4.mlp/mlp_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.4.self_attn.k_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.4.self_attn.q_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.4.self_attn.v_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.4.self_attn/attn_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.4/resid_in`: 1 个张量
- `vision_tower.vision_model.encoder.layers.4/resid_out:0`: 1 个张量
- `vision_tower.vision_model.encoder.layers.5.layer_norm1/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.5.layer_norm2/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.5.mlp/mlp_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.5.self_attn.k_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.5.self_attn.q_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.5.self_attn.v_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.5.self_attn/attn_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.5/resid_in`: 1 个张量
- `vision_tower.vision_model.encoder.layers.5/resid_out:0`: 1 个张量
- `vision_tower.vision_model.encoder.layers.6.layer_norm1/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.6.layer_norm2/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.6.mlp/mlp_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.6.self_attn.k_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.6.self_attn.q_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.6.self_attn.v_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.6.self_attn/attn_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.6/resid_in`: 1 个张量
- `vision_tower.vision_model.encoder.layers.6/resid_out:0`: 1 个张量
- `vision_tower.vision_model.encoder.layers.7.layer_norm1/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.7.layer_norm2/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.7.mlp/mlp_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.7.self_attn.k_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.7.self_attn.q_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.7.self_attn.v_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.7.self_attn/attn_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.7/resid_in`: 1 个张量
- `vision_tower.vision_model.encoder.layers.7/resid_out:0`: 1 个张量
- `vision_tower.vision_model.encoder.layers.8.layer_norm1/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.8.layer_norm2/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.8.mlp/mlp_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.8.self_attn.k_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.8.self_attn.q_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.8.self_attn.v_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.8.self_attn/attn_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.8/resid_in`: 1 个张量
- `vision_tower.vision_model.encoder.layers.8/resid_out:0`: 1 个张量
- `vision_tower.vision_model.encoder.layers.9.layer_norm1/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.9.layer_norm2/norm_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.9.mlp/mlp_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.9.self_attn.k_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.9.self_attn.q_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.9.self_attn.v_proj`: 1 个张量
- `vision_tower.vision_model.encoder.layers.9.self_attn/attn_out`: 1 个张量
- `vision_tower.vision_model.encoder.layers.9/resid_in`: 1 个张量
- `vision_tower.vision_model.encoder.layers.9/resid_out:0`: 1 个张量
- `vision_tower.vision_model.post_layernorm/norm_out`: 1 个张量

## 分析文件

- 注意力热力图: `attention_heatmaps_20250827_104940.png`
- 隐藏状态分析: `hidden_states_analysis_20250827_104940.png`
- 梯度流分析: `gradient_flow_20250827_104940.png`
- 残差流分析: `residual_flow_20250827_104940.png`
- QKV矩阵分析: `qkv_analysis_20250827_104940.png`
- 原始数据: `taps_20250827_104940.pkl`

## 使用说明

1. 查看各种可视化图表了解模型内部数据流
2. 使用pickle文件加载原始张量数据进行进一步分析
3. 注意力热力图显示了跨模态注意力模式
4. 梯度流分析有助于理解反向传播过程
