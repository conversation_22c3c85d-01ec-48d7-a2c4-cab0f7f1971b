{"input": {"image_path": "/home/<USER>/dataset/X/coke.png", "image_size": [640, 512], "text_prompt": "pick coke can", "input_ids_shape": [1, 261], "pixel_values_shape": [1, 3, 224, 224], "input_tokens": 261}, "language_embedding": {"shape": [1, 1, 2304], "dtype": "torch.bfloat16", "mean": -0.00019032756972592324, "std": 0.034848835319280624, "device": "cuda:0"}, "vision_encoder": {"shape": [1, 256, 1152], "dtype": "torch.bfloat16", "mean": 0.014364105649292469, "std": 1.7414774894714355, "device": "cuda:0"}, "vision_projection": {"shape": [1, 256, 2304], "dtype": "torch.bfloat16", "mean": -0.000374688213923946, "std": 0.2215261310338974, "device": "cuda:0"}, "first_attention": {"shape": [1, 1, 2304], "dtype": "torch.bfloat16", "mean": 3.88688531529624e-05, "std": 0.29381898045539856, "device": "cuda:0"}, "middle_attention": {"shape": [1, 1, 2304], "dtype": "torch.bfloat16", "mean": -0.0034278954844921827, "std": 0.29583001136779785, "device": "cuda:0"}, "last_attention": {"shape": [1, 1, 2304], "dtype": "torch.bfloat16", "mean": 0.0008862879476509988, "std": 0.12021410465240479, "device": "cuda:0"}, "final_norm": {"shape": [1, 1, 2304], "dtype": "torch.bfloat16", "mean": -0.017002969980239868, "std": 2.0507426261901855, "device": "cuda:0"}, "lm_head": {"shape": [1, 1, 257216], "dtype": "torch.bfloat16", "mean": -1.7438048124313354, "std": 2.4866809844970703, "device": "cuda:0"}, "output": {"generated_text": "on the table", "output_tokens": 4, "sequences_shape": [1, 265], "scores_length": 4}}