# PaliGemma 端到端数据流详细报告

## 输入信息
- **图像路径**: /home/<USER>/dataset/X/coke.png
- **图像尺寸**: (640, 512)
- **像素值张量形状**: [1, 3, 224, 224]
- **文本提示**: "pick coke can"
- **输入token数量**: 261
- **输入ID张量形状**: [1, 261]

## 数据流经过程

### 1. 视觉编码器 (SigLIP Vision Encoder)

- **输出形状**: [1, 256, 1152]
- **数据类型**: torch.bfloat16
- **均值**: 0.014364
- **标准差**: 1.741477
- **设备**: cuda:0
- **功能**: 将输入图像转换为视觉特征向量

### 2. 视觉投影层 (Vision Projection)
- **输出形状**: [1, 256, 2304]
- **数据类型**: torch.bfloat16
- **均值**: -0.000375
- **标准差**: 0.221526
- **功能**: 将视觉特征投影到语言模型的特征空间

### 3. 语言嵌入层 (Language Embedding)
- **输出形状**: [1, 1, 2304]
- **数据类型**: torch.bfloat16
- **均值**: -0.000190
- **标准差**: 0.034849
- **功能**: 将文本token转换为嵌入向量

### 4. 第一层注意力 (Cross-Modal Attention)
- **输出形状**: [1, 1, 2304]
- **数据类型**: torch.bfloat16
- **均值**: 0.000039
- **标准差**: 0.293819
- **功能**: 融合视觉和文本信息，实现跨模态理解

### 4. 中间层注意力 (Cross-Modal Attention)
- **输出形状**: [1, 1, 2304]
- **数据类型**: torch.bfloat16
- **均值**: -0.003428
- **标准差**: 0.295830
- **功能**: 融合视觉和文本信息，实现跨模态理解

### 4. 最后层注意力 (Cross-Modal Attention)
- **输出形状**: [1, 1, 2304]
- **数据类型**: torch.bfloat16
- **均值**: 0.000886
- **标准差**: 0.120214
- **功能**: 融合视觉和文本信息，实现跨模态理解

### 5. 最终归一化层 (Final Normalization)
- **输出形状**: [1, 1, 2304]
- **数据类型**: torch.bfloat16
- **均值**: -0.017003
- **标准差**: 2.050743
- **功能**: 对最终隐藏状态进行归一化

### 6. 语言模型头 (Language Model Head)
- **输出形状**: [1, 1, 257216]
- **数据类型**: torch.bfloat16
- **均值**: -1.743805
- **标准差**: 2.486681
- **功能**: 将隐藏状态转换为词汇表上的概率分布

## 最终输出
- **生成文本**: "on the table"
- **输出token数量**: 4
- **序列形状**: [1, 265]
- **分数数量**: 4

## 数据流总结
1. **图像处理**: (640, 512) → 视觉特征向量
2. **文本处理**: "pick coke can" → 嵌入向量
3. **多模态融合**: 通过交叉注意力机制融合视觉和文本信息
4. **序列生成**: 通过26层Transformer生成响应文本
5. **最终输出**: "on the table"

## 关键观察
- 模型成功将图像和文本信息融合
- 生成的文本与输入提示相关
- 数据在各个阶段保持了合理的数值范围
- 跨模态注意力机制有效工作
