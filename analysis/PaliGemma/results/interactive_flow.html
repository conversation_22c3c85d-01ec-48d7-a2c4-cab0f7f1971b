
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PaliGemma 交互式数据流可视化</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.8;
            font-size: 1.1em;
        }
        
        .flow-container {
            padding: 40px;
            background: #f8f9fa;
        }
        
        .flow-step {
            display: flex;
            align-items: center;
            margin: 30px 0;
            padding: 25px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            opacity: 0.3;
        }
        
        .flow-step.active {
            opacity: 1;
            transform: translateX(10px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }
        
        .step-number {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5em;
            font-weight: bold;
            margin-right: 25px;
            flex-shrink: 0;
        }
        
        .step-content {
            flex: 1;
        }
        
        .step-title {
            font-size: 1.4em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .step-description {
            color: #7f8c8d;
            margin-bottom: 15px;
            line-height: 1.6;
        }
        
        .step-data {
            background: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            border-left: 4px solid #3498db;
        }
        
        .input-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .input-image {
            text-align: center;
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .input-image img {
            max-width: 100%;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .input-text {
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .controls {
            text-align: center;
            margin: 30px 0;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .output-section {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-top: 30px;
            text-align: center;
        }
        
        .output-text {
            font-size: 1.3em;
            font-weight: 500;
            background: rgba(255,255,255,0.2);
            padding: 20px;
            border-radius: 8px;
            margin-top: 15px;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #ecf0f1;
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            width: 0%;
            transition: width 0.5s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>PaliGemma 数据流可视化</h1>
            <p>从图像和文本输入到生成输出的完整计算过程</p>
        </div>
        
        <div class="flow-container">
            <div class="input-section">
                <div class="input-image">
                    <h3>输入图像</h3>
                    <img src="data:image/png;base64,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" alt="输入图像">
                    <p>尺寸: (640, 512)</p>
                </div>
                <div class="input-text">
                    <h3>输入文本</h3>
                    <p style="font-size: 1.2em; font-weight: 500; color: #2c3e50;">
                        "pick coke can"
                    </p>
                    <p>Token数量: 261</p>
                </div>
            </div>
            
            <div class="controls">
                <button class="btn" onclick="startAnimation()">开始数据流动画</button>
                <button class="btn" onclick="resetAnimation()">重置</button>
                <button class="btn" onclick="stepByStep()">逐步执行</button>
            </div>
            
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            
            
            <div class="flow-step">
                <div class="step-number">1</div>
                <div class="step-content">
                    <div class="step-title">视觉编码器 (SigLIP)</div>
                    <div class="step-description">将输入图像转换为高维特征向量，提取视觉语义信息</div>
                    <div class="step-data">
                形状: [1, 256, 1152]
                数据类型: torch.bfloat16
                均值: 0.014364
                标准差: 1.741477
                设备: cuda:0
                </div>
                </div>
            </div>
            
            <div class="flow-step">
                <div class="step-number">2</div>
                <div class="step-content">
                    <div class="step-title">视觉投影层</div>
                    <div class="step-description">将视觉特征投影到语言模型的特征空间，实现模态对齐</div>
                    <div class="step-data">
                形状: [1, 256, 2304]
                数据类型: torch.bfloat16
                均值: -0.000375
                标准差: 0.221526
                设备: cuda:0
                </div>
                </div>
            </div>
            
            <div class="flow-step">
                <div class="step-number">3</div>
                <div class="step-content">
                    <div class="step-title">语言嵌入层</div>
                    <div class="step-description">将输入文本token转换为密集的嵌入向量表示</div>
                    <div class="step-data">
                形状: [1, 1, 2304]
                数据类型: torch.bfloat16
                均值: -0.000190
                标准差: 0.034849
                设备: cuda:0
                </div>
                </div>
            </div>
            
            <div class="flow-step">
                <div class="step-number">4</div>
                <div class="step-content">
                    <div class="step-title">多模态融合 (交叉注意力)</div>
                    <div class="step-description">通过注意力机制融合视觉和文本信息，建立跨模态理解</div>
                    <div class="step-data">
                形状: [1, 1, 2304]
                数据类型: torch.bfloat16
                均值: 0.000039
                标准差: 0.293819
                设备: cuda:0
                </div>
                </div>
            </div>
            
            <div class="flow-step">
                <div class="step-number">5</div>
                <div class="step-content">
                    <div class="step-title">Transformer层处理</div>
                    <div class="step-description">通过26层Transformer进行深度特征提取和序列建模</div>
                    <div class="step-data">
                形状: [1, 1, 2304]
                数据类型: torch.bfloat16
                均值: -0.003428
                标准差: 0.295830
                设备: cuda:0
                </div>
                </div>
            </div>
            
            <div class="flow-step">
                <div class="step-number">6</div>
                <div class="step-content">
                    <div class="step-title">最终归一化</div>
                    <div class="step-description">对最终隐藏状态进行归一化，稳定数值范围</div>
                    <div class="step-data">
                形状: [1, 1, 2304]
                数据类型: torch.bfloat16
                均值: -0.017003
                标准差: 2.050743
                设备: cuda:0
                </div>
                </div>
            </div>
            
            <div class="flow-step">
                <div class="step-number">7</div>
                <div class="step-content">
                    <div class="step-title">语言模型头</div>
                    <div class="step-description">将隐藏状态映射到词汇表，生成下一个token的概率分布</div>
                    <div class="step-data">
                形状: [1, 1, 257216]
                数据类型: torch.bfloat16
                均值: -1.743805
                标准差: 2.486681
                设备: cuda:0
                </div>
                </div>
            </div>
            
            
            <div class="output-section">
                <h3>生成输出</h3>
                <div class="output-text">
                    "on the table"
                </div>
                <p>输出Token数量: 4</p>
            </div>
        </div>
    </div>
    
    <script>
        let currentStep = 0;
        let isAnimating = false;
        let animationInterval;
        
        const steps = document.querySelectorAll('.flow-step');
        const progressFill = document.getElementById('progressFill');
        
        function startAnimation() {
            if (isAnimating) return;
            
            isAnimating = true;
            currentStep = 0;
            resetSteps();
            
            animationInterval = setInterval(() => {
                if (currentStep < steps.length) {
                    steps[currentStep].classList.add('active');
                    updateProgress();
                    currentStep++;
                } else {
                    clearInterval(animationInterval);
                    isAnimating = false;
                }
            }, 1500);
        }
        
        function resetAnimation() {
            clearInterval(animationInterval);
            isAnimating = false;
            currentStep = 0;
            resetSteps();
            progressFill.style.width = '0%';
        }
        
        function stepByStep() {
            if (isAnimating) return;
            
            if (currentStep < steps.length) {
                steps[currentStep].classList.add('active');
                updateProgress();
                currentStep++;
            }
        }
        
        function resetSteps() {
            steps.forEach(step => step.classList.remove('active'));
        }
        
        function updateProgress() {
            const progress = (currentStep / steps.length) * 100;
            progressFill.style.width = progress + '%';
        }
        
        // 添加点击事件
        steps.forEach((step, index) => {
            step.addEventListener('click', () => {
                if (!isAnimating) {
                    resetSteps();
                    for (let i = 0; i <= index; i++) {
                        steps[i].classList.add('active');
                    }
                    currentStep = index + 1;
                    updateProgress();
                }
            });
        });
    </script>
</body>
</html>
        