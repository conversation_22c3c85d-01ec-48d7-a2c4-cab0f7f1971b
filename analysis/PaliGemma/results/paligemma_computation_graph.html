<html>
    <head>
        <meta charset="utf-8">
        
            <script src="lib/bindings/utils.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/dist/vis-network.min.css" integrity="sha512-WgxfT5LWjfszlPHXRmBWHkV2eceiWTOBvrKCNbdgDYTHrT2AeLCGbF4sZlZw3UMN3WtL0tGUoIAKsu8mllg/XA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
            <script src="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/vis-network.min.js" integrity="sha512-LnvoEWDFrqGHlHmDD2101OrLcbsfkrzoSpvtSQtxK3RMnRV0eOkhhBN2dXHKRrUU8p2DGRTk35n4O8nWSVe1mQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
            
        
<center>
<h1></h1>
</center>

<!-- <link rel="stylesheet" href="../node_modules/vis/dist/vis.min.css" type="text/css" />
<script type="text/javascript" src="../node_modules/vis/dist/vis.js"> </script>-->
        <link
          href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/css/bootstrap.min.css"
          rel="stylesheet"
          integrity="sha384-eOJMYsd53ii+scO/bJGFsiCZc+5NDVN2yr8+0RDqr0Ql0h+rP48ckxlpbzKgwra6"
          crossorigin="anonymous"
        />
        <script
          src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/js/bootstrap.bundle.min.js"
          integrity="sha384-JEW9xMcG8R+pH31jmWH6WWP0WintQrMb4s7ZOdauHnUtxwoG2vI5DkLtS3qm9Ekf"
          crossorigin="anonymous"
        ></script>


        <center>
          <h1></h1>
        </center>
        <style type="text/css">

             #mynetwork {
                 width: 100%;
                 height: 800px;
                 background-color: #ffffff;
                 border: 1px solid lightgray;
                 position: relative;
                 float: left;
             }

             

             

             
        </style>
    </head>


    <body>
            <div style="text-align: center; padding: 20px; background: #f5f5f5; 
                        border-bottom: 1px solid #ddd;">
                <h1 style="margin: 0; color: #333;">PaliGemma Computation Graph</h1>
                <p style="margin: 5px 0 0 0; color: #666;">
                    交互式计算图可视化 - 可拖拽、缩放、点击查看详情
                </p>
            </div>
            
        <div id="legend" style="position: absolute; top: 10px; right: 10px;
                               background: rgba(255,255,255,0.95); padding: 20px;
                               border-radius: 8px; border: 2px solid #ddd;
                               font-family: Arial, sans-serif; font-size: 13px;
                               box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                               max-width: 300px;">
            <h3 style="margin-top: 0; color: #333; border-bottom: 2px solid #eee; padding-bottom: 10px;">
                PaliGemma 计算图说明
            </h3>

            <h4 style="margin: 15px 0 8px 0; color: #555;">模块类型颜色编码：</h4>
        
                <div style="margin: 8px 0; display: flex; align-items: center;">
                    <span style="display: inline-block; width: 18px; height: 18px;
                                 background-color: #2196F3; margin-right: 10px;
                                 border-radius: 4px; border: 1px solid #ccc;"></span>
                    <span style="font-weight: 500;">嵌入层</span>
                </div>
                
                <div style="margin: 8px 0; display: flex; align-items: center;">
                    <span style="display: inline-block; width: 18px; height: 18px;
                                 background-color: #FF9800; margin-right: 10px;
                                 border-radius: 4px; border: 1px solid #ccc;"></span>
                    <span style="font-weight: 500;">注意力机制</span>
                </div>
                
                <div style="margin: 8px 0; display: flex; align-items: center;">
                    <span style="display: inline-block; width: 18px; height: 18px;
                                 background-color: #9C27B0; margin-right: 10px;
                                 border-radius: 4px; border: 1px solid #ccc;"></span>
                    <span style="font-weight: 500;">MLP前馈网络</span>
                </div>
                
                <div style="margin: 8px 0; display: flex; align-items: center;">
                    <span style="display: inline-block; width: 18px; height: 18px;
                                 background-color: #FFC107; margin-right: 10px;
                                 border-radius: 4px; border: 1px solid #ccc;"></span>
                    <span style="font-weight: 500;">归一化层 (RMSNorm)</span>
                </div>
                
                <div style="margin: 8px 0; display: flex; align-items: center;">
                    <span style="display: inline-block; width: 18px; height: 18px;
                                 background-color: #F44336; margin-right: 10px;
                                 border-radius: 4px; border: 1px solid #ccc;"></span>
                    <span style="font-weight: 500;">输出层 (LM Head)</span>
                </div>
                
                <div style="margin: 8px 0; display: flex; align-items: center;">
                    <span style="display: inline-block; width: 18px; height: 18px;
                                 background-color: #00BCD4; margin-right: 10px;
                                 border-radius: 4px; border: 1px solid #ccc;"></span>
                    <span style="font-weight: 500;">视觉编码器 (SigLIP)</span>
                </div>
                
                <div style="margin: 8px 0; display: flex; align-items: center;">
                    <span style="display: inline-block; width: 18px; height: 18px;
                                 background-color: #8BC34A; margin-right: 10px;
                                 border-radius: 4px; border: 1px solid #ccc;"></span>
                    <span style="font-weight: 500;">语言模型 (Gemma2)</span>
                </div>
                
                <div style="margin: 8px 0; display: flex; align-items: center;">
                    <span style="display: inline-block; width: 18px; height: 18px;
                                 background-color: #E91E63; margin-right: 10px;
                                 border-radius: 4px; border: 1px solid #ccc;"></span>
                    <span style="font-weight: 500;">跨模态投影层</span>
                </div>
                
                <div style="margin: 8px 0; display: flex; align-items: center;">
                    <span style="display: inline-block; width: 18px; height: 18px;
                                 background-color: #9E9E9E; margin-right: 10px;
                                 border-radius: 4px; border: 1px solid #ccc;"></span>
                    <span style="font-weight: 500;">其他模块</span>
                </div>
                
            <h4 style="margin: 20px 0 8px 0; color: #555;">节点大小：</h4>
            <div style="margin: 8px 0; font-size: 12px; color: #666;">
                • 大节点: >100M 参数<br>
                • 中节点: 10M-100M 参数<br>
                • 小节点: <10M 参数
            </div>

            <h4 style="margin: 20px 0 8px 0; color: #555;">交互说明：</h4>
            <div style="margin: 8px 0; font-size: 12px; color: #666;">
                • 拖拽节点重新布局<br>
                • 滚轮缩放查看细节<br>
                • 悬停查看模块信息<br>
                • 点击选中相关连接
            </div>

            <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #eee;
                        font-size: 11px; color: #888;">
                <strong>计算图展示：</strong><br>
                PaliGemma模型的主要组件及其层次关系，
                从视觉编码器到语言模型的信息流动路径。
            </div>
        </div>
        
        <div class="card" style="width: 100%">
            
            
            <div id="mynetwork" class="card-body"></div>
        </div>

        
        

        <script type="text/javascript">

              // initialize global variables.
              var edges;
              var nodes;
              var allNodes;
              var allEdges;
              var nodeColors;
              var originalNodes;
              var network;
              var container;
              var options, data;
              var filter = {
                  item : '',
                  property : '',
                  value : []
              };

              

              

              // This method is responsible for drawing the graph, returns the drawn network
              function drawGraph() {
                  var container = document.getElementById('mynetwork');

                  

                  // parsing and collecting nodes and edges from the python
                  nodes = new vis.DataSet([{"font": {"color": "black"}, "group": "vision", "id": "vision_tower", "label": "vision_tower\nSiglipVisionModel\n412.4M", "shape": "dot", "size": 60, "title": "\u003cb\u003evision_tower\u003c/b\u003e\u003cbr\u003eType: SiglipVisionModel\u003cbr\u003eParameters: 412.4M\u003cbr\u003eTrainable: 412.4M\u003cbr\u003eChildren: 1\u003cbr\u003e"}, {"font": {"color": "black"}, "group": "vision", "id": "vision_tower.vision_model", "label": "Vision.vision_model\nSiglipVisionTransformer\n412.4M", "shape": "dot", "size": 60, "title": "\u003cb\u003evision_tower.vision_model\u003c/b\u003e\u003cbr\u003eType: SiglipVisionTransformer\u003cbr\u003eParameters: 412.4M\u003cbr\u003eTrainable: 412.4M\u003cbr\u003eChildren: 3\u003cbr\u003e"}, {"font": {"color": "black"}, "group": "projection", "id": "multi_modal_projector", "label": "multi_modal_projector\nPaliGemmaMultiModalProjector\n2.7M", "shape": "dot", "size": 30, "title": "\u003cb\u003emulti_modal_projector\u003c/b\u003e\u003cbr\u003eType: PaliGemmaMultiModalProjector\u003cbr\u003eParameters: 2.7M\u003cbr\u003eTrainable: 2.7M\u003cbr\u003eChildren: 1\u003cbr\u003e"}, {"font": {"color": "black"}, "group": "projection", "id": "multi_modal_projector.linear", "label": "multi_modal_projector.linear\nLinear\n2.7M", "shape": "dot", "size": 30, "title": "\u003cb\u003emulti_modal_projector.linear\u003c/b\u003e\u003cbr\u003eType: Linear\u003cbr\u003eParameters: 2.7M\u003cbr\u003eTrainable: 2.7M\u003cbr\u003e"}, {"font": {"color": "black"}, "group": "language", "id": "language_model", "label": "Language.language_model\nGemma2ForCausalLM\n2.6B", "shape": "dot", "size": 60, "title": "\u003cb\u003elanguage_model\u003c/b\u003e\u003cbr\u003eType: Gemma2ForCausalLM\u003cbr\u003eParameters: 2.6B\u003cbr\u003eTrainable: 2.6B\u003cbr\u003eChildren: 2\u003cbr\u003e"}, {"font": {"color": "black"}, "group": "language", "id": "language_model.model", "label": "Language.model\nGemma2Model\n2.6B", "shape": "dot", "size": 60, "title": "\u003cb\u003elanguage_model.model\u003c/b\u003e\u003cbr\u003eType: Gemma2Model\u003cbr\u003eParameters: 2.6B\u003cbr\u003eTrainable: 2.6B\u003cbr\u003eChildren: 3\u003cbr\u003e"}, {"font": {"color": "black"}, "group": "language", "id": "language_model.lm_head", "label": "Language.lm_head\nLinear\n592.6M", "shape": "dot", "size": 60, "title": "\u003cb\u003elanguage_model.lm_head\u003c/b\u003e\u003cbr\u003eType: Linear\u003cbr\u003eParameters: 592.6M\u003cbr\u003eTrainable: 592.6M\u003cbr\u003e"}]);
                  edges = new vis.DataSet([{"arrows": "to", "from": "vision_tower", "to": "vision_tower.vision_model", "width": 1}, {"arrows": "to", "from": "multi_modal_projector", "to": "multi_modal_projector.linear", "width": 1}, {"arrows": "to", "from": "language_model", "to": "language_model.model", "width": 1}, {"arrows": "to", "from": "language_model", "to": "language_model.lm_head", "width": 1}]);

                  nodeColors = {};
                  allNodes = nodes.get({ returnType: "Object" });
                  for (nodeId in allNodes) {
                    nodeColors[nodeId] = allNodes[nodeId].color;
                  }
                  allEdges = edges.get({ returnType: "Object" });
                  // adding nodes and edges to the graph
                  data = {nodes: nodes, edges: edges};

                  var options = {"physics": {"enabled": true, "stabilization": {"iterations": 200}, "barnesHut": {"gravitationalConstant": -15000, "centralGravity": 0.1, "springLength": 200, "springConstant": 0.02, "damping": 0.15, "avoidOverlap": 1}}, "layout": {"hierarchical": {"enabled": true, "direction": "UD", "sortMethod": "directed", "levelSeparation": 200, "nodeSpacing": 150, "treeSpacing": 200}}, "interaction": {"dragNodes": true, "dragView": true, "zoomView": true, "selectConnectedEdges": false}, "nodes": {"font": {"size": 14, "color": "black"}, "borderWidth": 2, "shadow": true}, "edges": {"color": {"color": "#848484", "highlight": "#FF0000"}, "width": 2, "smooth": {"enabled": true, "type": "continuous"}}};

                  


                  

                  network = new vis.Network(container, data, options);

                  

                  

                  


                  

                  return network;

              }
              drawGraph();
        </script>
    </body>
</html>