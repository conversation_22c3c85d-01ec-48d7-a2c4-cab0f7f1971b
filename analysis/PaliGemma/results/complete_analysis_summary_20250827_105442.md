# PaliGemma 完整分析报告

## 分析概览
- **分析时间**: 2025-08-27 10:54:42
- **模型路径**: /home/<USER>/dataset/X/models/PaliGemma
- **输入图像**: /home/<USER>/dataset/X/coke.png
- **文本提示**: "pick coke can"
- **输出目录**: /home/<USER>/dataset/X/analysis/PaliGemma/results

## 分析结果

### 1. 可解释性分析
❌ **失败** - 可解释性分析未能完成

### 2. 端到端数据流分析
✅ **完成** - 生成了数据流图、详细报告和交互式可视化

### 3. 计算图可视化
❌ **失败** - 计算图可视化未能完成

### 4. 结果可视化
❌ **失败** - 结果可视化未能完成

## 生成的文件

### 核心分析文件
- `paligemma_end_to_end_flow.png` - 端到端数据流图
- `interactive_flow.html` - 交互式数据流可视化
- `paligemma_computation_graph.html` - 交互式计算图
- `end_to_end_flow_report.md` - 详细数据流报告

### 可解释性分析文件
- `attention_heatmaps_*.png` - 注意力热力图
- `hidden_states_analysis_*.png` - 隐藏状态分析
- `gradient_flow_*.png` - 梯度流分析
- `residual_flow_*.png` - 残差流分析
- `qkv_analysis_*.png` - QKV矩阵分析

### 模型结构文件
- `paligemma_architecture.png` - 模型架构图
- `parameter_distribution.png` - 参数分布图
- `layer_depth_analysis.png` - 层深度分析

### 原始数据文件
- `taps_*.pkl` - 中间张量数据
- `attention_maps_*.npy` - 注意力权重
- `hidden_states_*.npy` - 隐藏状态
- `flow_data.json` - 数据流原始数据

## 使用建议

1. **查看交互式可视化**: 打开 `interactive_flow.html` 了解数据流过程
2. **分析注意力模式**: 查看注意力热力图了解跨模态注意力
3. **理解模型结构**: 查看计算图和架构图
4. **深入分析**: 使用pickle文件进行自定义分析

## 技术说明

本分析使用了PyTorch hooks机制捕获模型内部状态，包括：
- 前向传播中间结果
- 反向传播梯度信息
- 注意力权重和隐藏状态
- QKV矩阵和残差连接

分析结果有助于理解PaliGemma模型的工作机制和跨模态信息融合过程。
