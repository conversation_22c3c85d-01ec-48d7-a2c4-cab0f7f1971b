#!/usr/bin/env python3
"""
PaliGemma 可解释性分析运行脚本
简化版本，用于快速执行分析
"""

import sys
import os
import argparse
from interpretability_analysis import PaliGemmaInterpreter

def main():
    parser = argparse.ArgumentParser(description='PaliGemma 可解释性分析工具')
    parser.add_argument('--model_path', type=str, 
                       default='/home/<USER>/dataset/X/models/PaliGemma',
                       help='模型路径')
    parser.add_argument('--image_path', type=str,
                       default='/home/<USER>/dataset/X/coke.png', 
                       help='输入图像路径')
    parser.add_argument('--text_prompt', type=str,
                       default='pick coke can',
                       help='文本提示')
    parser.add_argument('--output_dir', type=str,
                       default='/home/<USER>/dataset/X/analysis/PaliGemma/results',
                       help='输出目录')
    parser.add_argument('--device', type=str, default='cuda:0',
                       help='设备 (cuda:0, cpu, etc.)')
    
    args = parser.parse_args()
    
    print("=== PaliGemma 可解释性分析 ===")
    print(f"模型路径: {args.model_path}")
    print(f"图像路径: {args.image_path}")
    print(f"文本提示: '{args.text_prompt}'")
    print(f"输出目录: {args.output_dir}")
    print(f"设备: {args.device}")
    
    # 检查文件是否存在
    if not os.path.exists(args.model_path):
        print(f"错误: 模型路径不存在: {args.model_path}")
        sys.exit(1)
    
    if not os.path.exists(args.image_path):
        print(f"错误: 图像路径不存在: {args.image_path}")
        sys.exit(1)
    
    try:
        # 创建分析器
        interpreter = PaliGemmaInterpreter(args.model_path, args.device)
        
        # 执行分析
        results = interpreter.analyze_image_text(
            args.image_path, 
            args.text_prompt, 
            args.output_dir
        )
        
        # 生成详细报告
        interpreter.generate_detailed_report(results, args.output_dir, results['timestamp'])
        
        print("\n=== 分析完成 ===")
        print(f"结果保存在: {args.output_dir}")
        print(f"时间戳: {results['timestamp']}")
        
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
