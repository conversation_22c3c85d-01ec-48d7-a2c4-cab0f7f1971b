#!/usr/bin/env python3
"""
加载和可视化已保存的PaliGemma分析结果
"""

import os
import pickle
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import json
import argparse
from datetime import datetime

class ResultsVisualizer:
    def __init__(self, results_dir):
        self.results_dir = results_dir
        
    def load_results(self, timestamp):
        """加载指定时间戳的分析结果"""
        results = {}
        
        # 加载基本信息
        summary_file = f"{self.results_dir}/analysis_summary_{timestamp}.json"
        if os.path.exists(summary_file):
            with open(summary_file, 'r', encoding='utf-8') as f:
                results['summary'] = json.load(f)
        
        # 加载attention maps
        attn_file = f"{self.results_dir}/attention_maps_{timestamp}.npy"
        if os.path.exists(attn_file):
            results['attention_maps'] = np.load(attn_file, allow_pickle=True)
        
        # 加载hidden states
        hidden_file = f"{self.results_dir}/hidden_states_{timestamp}.npy"
        if os.path.exists(hidden_file):
            results['hidden_states'] = np.load(hidden_file, allow_pickle=True)
        
        # 加载taps
        taps_file = f"{self.results_dir}/taps_{timestamp}.pkl"
        if os.path.exists(taps_file):
            with open(taps_file, 'rb') as f:
                results['taps'] = pickle.load(f)
        
        return results
    
    def list_available_results(self):
        """列出所有可用的分析结果"""
        timestamps = set()
        
        for filename in os.listdir(self.results_dir):
            if filename.startswith('analysis_summary_') and filename.endswith('.json'):
                timestamp = filename.replace('analysis_summary_', '').replace('.json', '')
                timestamps.add(timestamp)
        
        return sorted(list(timestamps))
    
    def visualize_cross_modal_attention(self, attention_maps, layer_idx=0, head_idx=0, save_path=None):
        """可视化跨模态注意力"""
        if not attention_maps or layer_idx >= len(attention_maps):
            print(f"无法访问层 {layer_idx}")
            return
        
        attn = attention_maps[layer_idx]  # [batch, heads, seq_len, seq_len]
        if attn.ndim == 4:
            attn = attn[0, head_idx]  # 选择第一个batch和指定head
        elif attn.ndim == 3:
            attn = attn[head_idx]  # 选择指定head
        
        plt.figure(figsize=(12, 10))
        sns.heatmap(attn, cmap='Blues', cbar=True)
        plt.title(f'Cross-Modal Attention - Layer {layer_idx}, Head {head_idx}')
        plt.xlabel('Key Position (Image + Text Tokens)')
        plt.ylabel('Query Position (Image + Text Tokens)')
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def analyze_attention_patterns(self, attention_maps, save_dir=None):
        """分析注意力模式"""
        if len(attention_maps) == 0:  # 修复数组比较问题
            return
        
        # 计算每层的注意力统计
        layer_stats = []
        for i, attn in enumerate(attention_maps):
            if attn.ndim == 4:
                attn = attn[0]  # 取第一个batch
            
            # 计算平均注意力
            avg_attn = np.mean(attn, axis=0)  # 平均所有头
            
            stats = {
                'layer': i,
                'entropy': -np.sum(avg_attn * np.log(avg_attn + 1e-10), axis=-1).mean(),
                'max_attention': np.max(avg_attn),
                'attention_spread': np.std(avg_attn)
            }
            layer_stats.append(stats)
        
        # 可视化统计信息
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        layers = [s['layer'] for s in layer_stats]
        
        axes[0].plot(layers, [s['entropy'] for s in layer_stats], 'b-o')
        axes[0].set_title('Attention Entropy by Layer')
        axes[0].set_xlabel('Layer')
        axes[0].set_ylabel('Entropy')
        
        axes[1].plot(layers, [s['max_attention'] for s in layer_stats], 'r-o')
        axes[1].set_title('Max Attention by Layer')
        axes[1].set_xlabel('Layer')
        axes[1].set_ylabel('Max Attention')
        
        axes[2].plot(layers, [s['attention_spread'] for s in layer_stats], 'g-o')
        axes[2].set_title('Attention Spread by Layer')
        axes[2].set_xlabel('Layer')
        axes[2].set_ylabel('Std Dev')
        
        plt.tight_layout()
        
        if save_dir:
            plt.savefig(f"{save_dir}/attention_patterns_analysis.png", dpi=300, bbox_inches='tight')
        plt.show()
        
        return layer_stats
    
    def analyze_tensor_flow(self, taps, save_dir=None):
        """分析张量流动"""
        if not taps:
            return
        
        # 分析不同类型的张量
        tensor_types = {
            'residual': [],
            'attention': [],
            'mlp': [],
            'norm': [],
            'qkv': []
        }
        
        for name, tensors in taps.items():
            if not tensors:
                continue
                
            tensor = tensors[-1]  # 取最后一个张量
            if hasattr(tensor, 'norm'):
                norm_val = tensor.norm().item()
            else:
                norm_val = np.linalg.norm(tensor)
            
            if 'resid' in name:
                tensor_types['residual'].append((name, norm_val))
            elif 'attn' in name:
                tensor_types['attention'].append((name, norm_val))
            elif 'mlp' in name:
                tensor_types['mlp'].append((name, norm_val))
            elif 'norm' in name:
                tensor_types['norm'].append((name, norm_val))
            elif any(proj in name for proj in ['q_proj', 'k_proj', 'v_proj']):
                tensor_types['qkv'].append((name, norm_val))
        
        # 可视化
        fig, axes = plt.subplots(2, 3, figsize=(18, 10))
        axes = axes.flatten()
        
        for i, (tensor_type, data) in enumerate(tensor_types.items()):
            if len(data) > 0 and i < len(axes):  # 修复数组比较问题
                names, values = zip(*data)
                axes[i].bar(range(len(names)), values)
                axes[i].set_title(f'{tensor_type.title()} Tensor Norms')
                axes[i].set_ylabel('Norm')
                axes[i].set_xticks(range(len(names)))
                axes[i].set_xticklabels([n.split('.')[-2] if '.' in n else n for n in names],
                                       rotation=45, ha='right')
        
        # 隐藏未使用的子图
        for i in range(len(tensor_types), len(axes)):
            axes[i].set_visible(False)
        
        plt.tight_layout()
        
        if save_dir:
            plt.savefig(f"{save_dir}/tensor_flow_analysis.png", dpi=300, bbox_inches='tight')
        plt.show()

def main():
    parser = argparse.ArgumentParser(description='加载和可视化PaliGemma分析结果')
    parser.add_argument('--results_dir', type=str,
                       default='/home/<USER>/dataset/X/analysis/PaliGemma/results',
                       help='结果目录')
    parser.add_argument('--timestamp', type=str, default=None,
                       help='指定时间戳，如果不指定则使用最新的')
    parser.add_argument('--layer', type=int, default=0,
                       help='要可视化的注意力层')
    parser.add_argument('--head', type=int, default=0,
                       help='要可视化的注意力头')
    
    args = parser.parse_args()
    
    visualizer = ResultsVisualizer(args.results_dir)
    
    # 获取可用的结果
    available_timestamps = visualizer.list_available_results()
    
    if not available_timestamps:
        print(f"在 {args.results_dir} 中没有找到分析结果")
        return
    
    # 选择时间戳
    if args.timestamp:
        if args.timestamp not in available_timestamps:
            print(f"时间戳 {args.timestamp} 不存在")
            print(f"可用的时间戳: {available_timestamps}")
            return
        timestamp = args.timestamp
    else:
        timestamp = available_timestamps[-1]  # 使用最新的
    
    print(f"加载时间戳: {timestamp}")
    
    # 加载结果
    results = visualizer.load_results(timestamp)
    
    if 'summary' in results:
        print(f"文本提示: {results['summary']['text_prompt']}")
        print(f"损失值: {results['summary']['loss']:.6f}")
    
    # 可视化跨模态注意力
    if 'attention_maps' in results:
        print("生成跨模态注意力可视化...")
        save_path = f"{args.results_dir}/cross_modal_attention_{timestamp}_L{args.layer}H{args.head}.png"
        visualizer.visualize_cross_modal_attention(
            results['attention_maps'], 
            args.layer, 
            args.head,
            save_path
        )
        
        # 分析注意力模式
        print("分析注意力模式...")
        visualizer.analyze_attention_patterns(results['attention_maps'], args.results_dir)
    
    # 分析张量流动
    if 'taps' in results:
        print("分析张量流动...")
        visualizer.analyze_tensor_flow(results['taps'], args.results_dir)
    
    print("可视化完成！")

if __name__ == "__main__":
    main()
