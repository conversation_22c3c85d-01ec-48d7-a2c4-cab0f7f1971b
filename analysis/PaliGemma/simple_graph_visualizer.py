#!/usr/bin/env python3
"""
PaliGemma 简化计算图可视化工具
使用matplotlib生成静态图表，不依赖额外的网络图库
"""

import os
import json
import torch
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch
import numpy as np
from transformers import AutoProcessor, PaliGemmaForConditionalGeneration
from collections import defaultdict
import re

class SimplePaliGemmaVisualizer:
    def __init__(self, model_path, device="cuda:0"):
        """初始化简化可视化器"""
        self.model_path = model_path
        self.device = device
        self.module_info = {}
        
        # 颜色映射
        self.colors = {
            'vision': '#00BCD4',     # 青色 - 视觉编码器
            'language': '#8BC34A',   # 浅绿 - 语言模型
            'embedding': '#2196F3',   # 蓝色 - 嵌入层
            'attention': '#FF9800',   # 橙色 - 注意力
            'mlp': '#9C27B0',        # 紫色 - MLP
            'norm': '#FFC107',       # 黄色 - 归一化
            'projection': '#E91E63', # 粉色 - 投影层
            'output': '#F44336',     # 红色 - 输出
            'other': '#9E9E9E'       # 灰色 - 其他
        }
        
        print("正在加载模型...")
        self.model = PaliGemmaForConditionalGeneration.from_pretrained(
            model_path,
            torch_dtype=torch.bfloat16,
            device_map=device,
        )
        print("模型加载完成")
        
    def analyze_model_structure(self):
        """分析模型结构"""
        print("分析模型结构...")
        
        for name, module in self.model.named_modules():
            if name:  # 跳过根模块
                self.module_info[name] = {
                    'type': module.__class__.__name__,
                    'parameters': sum(p.numel() for p in module.parameters()),
                    'depth': len(name.split('.')),
                    'category': self._classify_module(name, module.__class__.__name__)
                }
        
        print(f"发现 {len(self.module_info)} 个模块")
    
    def _classify_module(self, name, module_type):
        """分类模块类型"""
        name_lower = name.lower()
        type_lower = module_type.lower()
        
        if 'vision' in name_lower or 'siglip' in name_lower:
            return 'vision'
        elif 'language' in name_lower or 'gemma' in name_lower:
            return 'language'
        elif 'embedding' in type_lower or 'embed' in name_lower:
            return 'embedding'
        elif 'attention' in type_lower or 'attn' in name_lower:
            return 'attention'
        elif 'mlp' in type_lower or 'feed_forward' in name_lower:
            return 'mlp'
        elif 'norm' in type_lower or 'layernorm' in type_lower or 'rmsnorm' in type_lower:
            return 'norm'
        elif 'projection' in name_lower or 'proj' in name_lower:
            return 'projection'
        elif 'lm_head' in name_lower or 'classifier' in name_lower:
            return 'output'
        else:
            return 'other'
    
    def create_architecture_diagram(self, output_path, max_depth=3):
        """创建架构图"""
        print("创建架构图...")
        
        # 过滤模块
        filtered_modules = {
            name: info for name, info in self.module_info.items()
            if info['depth'] <= max_depth
        }
        
        # 按类别分组
        categories = defaultdict(list)
        for name, info in filtered_modules.items():
            categories[info['category']].append((name, info))
        
        # 创建图表
        fig, ax = plt.subplots(figsize=(16, 12))
        ax.set_xlim(0, 10)
        ax.set_ylim(0, 10)
        ax.axis('off')
        
        # 绘制主要组件
        self._draw_main_components(ax, categories)
        
        # 添加标题
        plt.title('PaliGemma Model Architecture', fontsize=20, fontweight='bold', pad=20)
        
        # 添加图例
        self._add_legend(ax)
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"架构图已保存到: {output_path}")
    
    def _draw_main_components(self, ax, categories):
        """绘制主要组件"""
        # 定义组件位置
        component_layout = {
            'vision': {'x': 1, 'y': 8, 'width': 2, 'height': 1.5},
            'language': {'x': 6, 'y': 8, 'width': 2.5, 'height': 1.5},
            'embedding': {'x': 1, 'y': 6, 'width': 2, 'height': 1},
            'attention': {'x': 4, 'y': 5, 'width': 2, 'height': 1.5},
            'mlp': {'x': 4, 'y': 3, 'width': 2, 'height': 1},
            'norm': {'x': 7, 'y': 5, 'width': 1.5, 'height': 1},
            'projection': {'x': 1, 'y': 2, 'width': 2, 'height': 1},
            'output': {'x': 6, 'y': 1, 'width': 2, 'height': 1}
        }
        
        # 绘制组件
        for category, modules in categories.items():
            if category in component_layout and modules:
                layout = component_layout[category]
                color = self.colors.get(category, self.colors['other'])
                
                # 绘制矩形
                rect = FancyBboxPatch(
                    (layout['x'], layout['y']), 
                    layout['width'], 
                    layout['height'],
                    boxstyle="round,pad=0.1",
                    facecolor=color,
                    edgecolor='black',
                    alpha=0.7,
                    linewidth=2
                )
                ax.add_patch(rect)
                
                # 添加文本
                ax.text(
                    layout['x'] + layout['width']/2,
                    layout['y'] + layout['height']/2,
                    f"{category.title()}\n({len(modules)} modules)",
                    ha='center', va='center',
                    fontsize=10, fontweight='bold',
                    color='white' if category in ['mlp', 'projection'] else 'black'
                )
        
        # 绘制连接线
        self._draw_connections(ax, component_layout)
    
    def _draw_connections(self, ax, layout):
        """绘制组件间的连接"""
        connections = [
            ('vision', 'attention'),
            ('language', 'attention'),
            ('embedding', 'attention'),
            ('attention', 'mlp'),
            ('mlp', 'norm'),
            ('norm', 'output'),
            ('projection', 'output')
        ]
        
        for start, end in connections:
            if start in layout and end in layout:
                start_pos = layout[start]
                end_pos = layout[end]
                
                # 计算连接点
                start_x = start_pos['x'] + start_pos['width']/2
                start_y = start_pos['y']
                end_x = end_pos['x'] + end_pos['width']/2
                end_y = end_pos['y'] + end_pos['height']
                
                # 绘制箭头
                ax.annotate('', xy=(end_x, end_y), xytext=(start_x, start_y),
                           arrowprops=dict(arrowstyle='->', lw=2, color='gray'))
    
    def _add_legend(self, ax):
        """添加图例"""
        legend_elements = []
        for category, color in self.colors.items():
            if category != 'other':
                legend_elements.append(
                    patches.Patch(color=color, label=category.title())
                )
        
        ax.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(0.98, 0.98))
    
    def create_parameter_distribution_chart(self, output_path):
        """创建参数分布图"""
        print("创建参数分布图...")
        
        # 按类别统计参数
        category_params = defaultdict(int)
        for name, info in self.module_info.items():
            category_params[info['category']] += info['parameters']
        
        # 创建饼图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
        
        # 参数分布饼图
        categories = list(category_params.keys())
        params = list(category_params.values())
        colors = [self.colors.get(cat, self.colors['other']) for cat in categories]
        
        wedges, texts, autotexts = ax1.pie(
            params, labels=categories, colors=colors, autopct='%1.1f%%',
            startangle=90, textprops={'fontsize': 10}
        )
        ax1.set_title('Parameter Distribution by Component Type', fontsize=14, fontweight='bold')
        
        # 参数数量柱状图
        formatted_params = [p/1e6 for p in params]  # 转换为百万参数
        bars = ax2.bar(categories, formatted_params, color=colors, alpha=0.7)
        ax2.set_title('Parameter Count by Component Type', fontsize=14, fontweight='bold')
        ax2.set_ylabel('Parameters (Millions)')
        ax2.tick_params(axis='x', rotation=45)
        
        # 添加数值标签
        for bar, param in zip(bars, formatted_params):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height,
                    f'{param:.1f}M', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"参数分布图已保存到: {output_path}")
    
    def create_layer_depth_analysis(self, output_path):
        """创建层深度分析图"""
        print("创建层深度分析图...")
        
        # 按深度统计
        depth_stats = defaultdict(lambda: defaultdict(int))
        for name, info in self.module_info.items():
            depth_stats[info['depth']][info['category']] += 1
        
        # 创建堆叠柱状图
        fig, ax = plt.subplots(figsize=(14, 8))
        
        depths = sorted(depth_stats.keys())
        categories = list(self.colors.keys())
        
        # 准备数据
        data = {}
        for category in categories:
            data[category] = [depth_stats[depth][category] for depth in depths]
        
        # 绘制堆叠柱状图
        bottom = np.zeros(len(depths))
        for category in categories:
            if any(data[category]):  # 只绘制有数据的类别
                ax.bar(depths, data[category], bottom=bottom, 
                      label=category.title(), color=self.colors[category], alpha=0.8)
                bottom += data[category]
        
        ax.set_xlabel('Module Depth Level')
        ax.set_ylabel('Number of Modules')
        ax.set_title('Module Distribution by Depth Level', fontsize=14, fontweight='bold')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"层深度分析图已保存到: {output_path}")

def main():
    """主函数"""
    model_path = "/home/<USER>/dataset/X/models/PaliGemma"
    output_dir = "/home/<USER>/dataset/X/analysis/PaliGemma/results"
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    print("=== PaliGemma 简化计算图可视化 ===")
    
    # 创建可视化器
    visualizer = SimplePaliGemmaVisualizer(model_path)
    
    # 分析模型结构
    visualizer.analyze_model_structure()
    
    # 创建各种可视化
    visualizer.create_architecture_diagram(
        os.path.join(output_dir, "paligemma_architecture.png")
    )
    
    visualizer.create_parameter_distribution_chart(
        os.path.join(output_dir, "parameter_distribution.png")
    )
    
    visualizer.create_layer_depth_analysis(
        os.path.join(output_dir, "layer_depth_analysis.png")
    )
    
    print("\n=== 可视化完成 ===")
    print(f"所有图表已保存到: {output_dir}")
    print("生成的文件:")
    print("- paligemma_architecture.png: 模型架构图")
    print("- parameter_distribution.png: 参数分布图")
    print("- layer_depth_analysis.png: 层深度分析图")

if __name__ == "__main__":
    main()
