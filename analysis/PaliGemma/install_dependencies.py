#!/usr/bin/env python3
"""
安装PaliGemma可解释性分析工具的依赖包
"""

import subprocess
import sys
import importlib

def install_package(package_name, import_name=None):
    """安装Python包"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        print(f"✓ {package_name} 已安装")
        return True
    except ImportError:
        print(f"正在安装 {package_name}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
            print(f"✓ {package_name} 安装成功")
            return True
        except subprocess.CalledProcessError:
            print(f"✗ {package_name} 安装失败")
            return False

def main():
    """安装所有必要的依赖"""
    print("=== 安装PaliGemma分析工具依赖 ===")
    
    # 必要的包列表
    packages = [
        ("torch", "torch"),
        ("transformers", "transformers"),
        ("pillow", "PIL"),
        ("matplotlib", "matplotlib"),
        ("seaborn", "seaborn"),
        ("numpy", "numpy"),
        ("networkx", "networkx"),
        ("pyvis", "pyvis"),
    ]
    
    success_count = 0
    total_count = len(packages)
    
    for package_name, import_name in packages:
        if install_package(package_name, import_name):
            success_count += 1
    
    print(f"\n=== 安装完成 ===")
    print(f"成功安装: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("✓ 所有依赖已成功安装！")
        print("\n您现在可以运行:")
        print("python visualize_computation_graph.py")
    else:
        print("✗ 部分依赖安装失败，请手动安装缺失的包")

if __name__ == "__main__":
    main()
