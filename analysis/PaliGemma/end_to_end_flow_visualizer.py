#!/usr/bin/env python3
"""
PaliGemma 端到端数据流可视化
展示从输入到输出的完整计算过程，包括实际的数据内容和形状变化
"""

import os
import torch
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyBboxPatch, ConnectionPatch
from PIL import Image
from transformers import AutoProcessor, PaliGemmaForConditionalGeneration
import json
from collections import defaultdict

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial Unicode MS', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

class EndToEndFlowVisualizer:
    def __init__(self, model_path, device="cuda:0"):
        """初始化端到端流可视化器"""
        self.model_path = model_path
        self.device = device
        self.flow_data = {}
        self.hooks = []
        
        print("正在加载模型...")
        self.model = PaliGemmaForConditionalGeneration.from_pretrained(
            model_path,
            torch_dtype=torch.bfloat16,
            device_map=device,
        )
        self.processor = AutoProcessor.from_pretrained(model_path)
        print("模型加载完成")
        
    def capture_data_flow(self, image_path, text_prompt):
        """捕获数据流经过程"""
        print(f"捕获数据流: 图像={image_path}, 文本='{text_prompt}'")
        
        # 加载输入
        image = Image.open(image_path).convert("RGB")
        inputs = self.processor(images=image, text=text_prompt, return_tensors="pt").to(self.model.device)
        
        # 记录输入信息
        self.flow_data['input'] = {
            'image_path': image_path,
            'image_size': image.size,
            'text_prompt': text_prompt,
            'input_ids_shape': list(inputs["input_ids"].shape),
            'pixel_values_shape': list(inputs["pixel_values"].shape),
            'input_tokens': inputs["input_ids"].shape[1]
        }
        
        # 添加hooks捕获关键中间结果
        self._add_flow_hooks()
        
        try:
            # 前向传播
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=50,
                    do_sample=False,
                    return_dict_in_generate=True,
                    output_scores=True
                )

            # 解码输出
            generated_ids = outputs.sequences[0][inputs["input_ids"].shape[1]:]
            generated_text = self.processor.decode(generated_ids, skip_special_tokens=True)
            
            # 记录输出信息
            self.flow_data['output'] = {
                'generated_text': generated_text,
                'output_tokens': len(generated_ids),
                'sequences_shape': list(outputs.sequences.shape) if hasattr(outputs, 'sequences') else None,
                'scores_length': len(outputs.scores) if hasattr(outputs, 'scores') and outputs.scores else 0
            }
            
        finally:
            self._remove_hooks()
        
        return self.flow_data
    
    def _add_flow_hooks(self):
        """添加数据流捕获hooks"""
        
        def create_hook(stage_name):
            def hook_fn(module, input, output):
                try:
                    if isinstance(output, torch.Tensor):
                        self.flow_data[stage_name] = {
                            'shape': list(output.shape),
                            'dtype': str(output.dtype),
                            'mean': float(output.float().mean().cpu()),
                            'std': float(output.float().std().cpu()),
                            'device': str(output.device)
                        }
                        print(f"成功捕获 {stage_name}: {output.shape}")
                    elif isinstance(output, (tuple, list)) and len(output) > 0:
                        # 对于vision_tower，输出通常是BaseModelOutput对象
                        if hasattr(output, 'last_hidden_state'):
                            main_output = output.last_hidden_state
                        elif hasattr(output, 'pooler_output'):
                            main_output = output.pooler_output
                        else:
                            main_output = output[0] if isinstance(output[0], torch.Tensor) else output

                        if isinstance(main_output, torch.Tensor):
                            self.flow_data[stage_name] = {
                                'shape': list(main_output.shape),
                                'dtype': str(main_output.dtype),
                                'mean': float(main_output.float().mean().cpu()),
                                'std': float(main_output.float().std().cpu()),
                                'device': str(main_output.device)
                            }
                            print(f"成功捕获 {stage_name}: {main_output.shape}")
                    elif hasattr(output, 'last_hidden_state'):
                        # 处理BaseModelOutput等对象
                        main_output = output.last_hidden_state
                        self.flow_data[stage_name] = {
                            'shape': list(main_output.shape),
                            'dtype': str(main_output.dtype),
                            'mean': float(main_output.float().mean().cpu()),
                            'std': float(main_output.float().std().cpu()),
                            'device': str(main_output.device)
                        }
                        print(f"成功捕获 {stage_name}: {main_output.shape}")
                except Exception as e:
                    print(f"捕获 {stage_name} 时出错: {e}")
            return hook_fn
        
        # 智能查找关键模块
        key_modules = self._find_key_modules()
        
        for stage_name, module_path in key_modules.items():
            try:
                module = self.model
                for part in module_path.split('.'):
                    module = getattr(module, part)
                hook = module.register_forward_hook(create_hook(stage_name))
                self.hooks.append(hook)
                print(f"已添加hook: {stage_name} -> {module_path}")
            except AttributeError:
                print(f"警告: 无法找到模块 {module_path}")
    
    def _find_key_modules(self):
        """智能查找关键模块路径"""
        key_modules = {}

        # 打印所有顶级模块以便调试
        print("模型顶级模块:")
        for name, module in self.model.named_children():
            print(f"  {name}: {type(module).__name__}")

        # 查找视觉编码器 - 尝试多种可能的路径
        vision_candidates = []
        for name, module in self.model.named_modules():
            if any(keyword in name.lower() for keyword in ['vision', 'siglip', 'image']):
                vision_candidates.append(name)

        print(f"找到的视觉相关模块: {vision_candidates[:5]}")  # 只显示前5个

        # 选择最合适的视觉编码器 - 直接使用vision_tower
        if 'vision_tower' in [name for name, _ in self.model.named_children()]:
            key_modules['vision_encoder'] = 'vision_tower'
        elif vision_candidates:
            key_modules['vision_encoder'] = vision_candidates[0]

        # 查找其他模块
        module_patterns = {
            'vision_projection': ['multi_modal_projector', 'projector'],
            'language_embedding': ['language_model.model.embed_tokens', 'embed_tokens'],
            'first_attention': ['language_model.model.layers.0.self_attn'],
            'middle_attention': ['language_model.model.layers.13.self_attn'],
            'last_attention': ['language_model.model.layers.25.self_attn'],
            'final_norm': ['language_model.model.norm'],
            'lm_head': ['language_model.lm_head', 'lm_head']
        }

        for stage_name, patterns in module_patterns.items():
            for pattern in patterns:
                try:
                    module = self.model
                    for part in pattern.split('.'):
                        module = getattr(module, part)
                    key_modules[stage_name] = pattern
                    break
                except AttributeError:
                    continue

        print(f"最终选择的关键模块: {key_modules}")
        return key_modules

    def _remove_hooks(self):
        """移除hooks"""
        for hook in self.hooks:
            hook.remove()
        self.hooks.clear()
    
    def create_flow_diagram(self, output_path, flow_data):
        """创建数据流图"""
        print("创建端到端数据流图...")
        
        fig, ax = plt.subplots(figsize=(20, 14))
        ax.set_xlim(0, 20)
        ax.set_ylim(0, 14)
        ax.axis('off')
        
        # 定义流程步骤和位置（使用英文标签）
        flow_steps = [
            {
                'name': 'Input Image',
                'pos': (2, 12),
                'size': (3, 1.5),
                'color': '#4CAF50',
                'content': self._format_input_info(flow_data)
            },
            {
                'name': 'Vision Encoder\n(SigLIP)',
                'pos': (2, 9.5),
                'size': (3, 1.5),
                'color': '#2196F3',
                'content': self._format_stage_info('vision_encoder', flow_data)
            },
            {
                'name': 'Vision Projection',
                'pos': (2, 7),
                'size': (3, 1.5),
                'color': '#FF9800',
                'content': self._format_stage_info('vision_projection', flow_data)
            },
            {
                'name': 'Text Input',
                'pos': (7, 12),
                'size': (3, 1.5),
                'color': '#4CAF50',
                'content': self._format_text_input(flow_data)
            },
            {
                'name': 'Language Embedding',
                'pos': (7, 9.5),
                'size': (3, 1.5),
                'color': '#9C27B0',
                'content': self._format_stage_info('language_embedding', flow_data)
            },
            {
                'name': 'Multi-Modal Fusion\n(Cross Attention)',
                'pos': (12, 9.5),
                'size': (4, 1.5),
                'color': '#E91E63',
                'content': self._format_attention_info(flow_data)
            },
            {
                'name': 'Transformer Layers\n(26 layers)',
                'pos': (12, 7),
                'size': (4, 1.5),
                'color': '#673AB7',
                'content': self._format_transformer_info(flow_data)
            },
            {
                'name': 'Final Normalization',
                'pos': (12, 4.5),
                'size': (4, 1.5),
                'color': '#FFC107',
                'content': self._format_stage_info('final_norm', flow_data)
            },
            {
                'name': 'Language Model Head',
                'pos': (12, 2),
                'size': (4, 1.5),
                'color': '#F44336',
                'content': self._format_stage_info('lm_head', flow_data)
            },
            {
                'name': 'Generated Output',
                'pos': (17, 0.5),
                'size': (2.5, 1),
                'color': '#795548',
                'content': self._format_output_info(flow_data)
            }
        ]
        
        # 绘制流程步骤
        for step in flow_steps:
            self._draw_flow_step(ax, step)
        
        # 绘制连接线
        self._draw_flow_connections(ax, flow_steps)
        
        # 添加标题（使用英文避免字体问题）
        plt.title('PaliGemma End-to-End Data Flow', fontsize=24, fontweight='bold', pad=20)
        
        # 添加说明
        self._add_flow_explanation(ax)
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"数据流图已保存到: {output_path}")
    
    def _draw_flow_step(self, ax, step):
        """绘制单个流程步骤"""
        x, y = step['pos']
        w, h = step['size']
        
        # 绘制矩形
        rect = FancyBboxPatch(
            (x, y), w, h,
            boxstyle="round,pad=0.1",
            facecolor=step['color'],
            edgecolor='black',
            alpha=0.8,
            linewidth=2
        )
        ax.add_patch(rect)
        
        # 添加标题
        ax.text(x + w/2, y + h - 0.2, step['name'], 
               ha='center', va='top', fontsize=11, fontweight='bold', color='white')
        
        # 添加内容
        if step['content']:
            ax.text(x + w/2, y + h/2 - 0.1, step['content'], 
                   ha='center', va='center', fontsize=9, color='white',
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='black', alpha=0.3))
    
    def _draw_flow_connections(self, ax, steps):
        """绘制流程连接线"""
        connections = [
            (0, 1),  # Input Image -> Vision Encoder
            (1, 2),  # Vision Encoder -> Vision Projection
            (3, 4),  # Text Input -> Language Embedding
            (2, 5),  # Vision Projection -> Multi-Modal Fusion
            (4, 5),  # Language Embedding -> Multi-Modal Fusion
            (5, 6),  # Multi-Modal Fusion -> Transformer Layers
            (6, 7),  # Transformer Layers -> Final Normalization
            (7, 8),  # Final Normalization -> Language Model Head
            (8, 9),  # Language Model Head -> Generated Output
        ]
        
        for start_idx, end_idx in connections:
            start = steps[start_idx]
            end = steps[end_idx]
            
            start_x = start['pos'][0] + start['size'][0]/2
            start_y = start['pos'][1]
            end_x = end['pos'][0] + end['size'][0]/2
            end_y = end['pos'][1] + end['size'][1]
            
            ax.annotate('', xy=(end_x, end_y), xytext=(start_x, start_y),
                       arrowprops=dict(arrowstyle='->', lw=3, color='#333333'))
    
    def _format_input_info(self, flow_data):
        """格式化输入信息"""
        if 'input' not in flow_data:
            return "No input data"
        
        input_info = flow_data['input']
        return f"Size: {input_info['image_size']}\nPixels: {input_info['pixel_values_shape']}"
    
    def _format_text_input(self, flow_data):
        """格式化文本输入信息"""
        if 'input' not in flow_data:
            return "No text data"
        
        input_info = flow_data['input']
        return f"Text: '{input_info['text_prompt']}'\nTokens: {input_info['input_tokens']}"
    
    def _format_stage_info(self, stage_name, flow_data):
        """格式化阶段信息"""
        if stage_name not in flow_data:
            return "No data captured"
        
        stage_info = flow_data[stage_name]
        return f"Shape: {stage_info['shape']}\nMean: {stage_info['mean']:.3f}\nStd: {stage_info['std']:.3f}"
    
    def _format_attention_info(self, flow_data):
        """格式化注意力信息"""
        if 'first_attention' in flow_data:
            info = flow_data['first_attention']
            return f"Cross-modal attention\nShape: {info['shape']}\nFusing vision & text"
        return "Cross-modal fusion"
    
    def _format_transformer_info(self, flow_data):
        """格式化Transformer信息"""
        layers_info = []
        for stage in ['first_attention', 'middle_attention', 'last_attention']:
            if stage in flow_data:
                layers_info.append(f"{stage.split('_')[0]}: {flow_data[stage]['shape']}")
        
        if layers_info:
            return "\n".join(layers_info)
        return "26 Transformer layers\nSelf-attention + MLP"
    
    def _format_output_info(self, flow_data):
        """格式化输出信息"""
        if 'output' not in flow_data:
            return "No output"

        output_info = flow_data['output']
        text = output_info['generated_text']
        if len(text) > 25:
            text = text[:25] + "..."
        return f"'{text}'\nTokens: {output_info['output_tokens']}"

    def create_detailed_flow_report(self, output_path, flow_data):
        """创建详细的数据流报告"""
        print("生成详细数据流报告...")

        report = f"""# PaliGemma 端到端数据流详细报告

## 输入信息
- **图像路径**: {flow_data['input']['image_path']}
- **图像尺寸**: {flow_data['input']['image_size']}
- **像素值张量形状**: {flow_data['input']['pixel_values_shape']}
- **文本提示**: "{flow_data['input']['text_prompt']}"
- **输入token数量**: {flow_data['input']['input_tokens']}
- **输入ID张量形状**: {flow_data['input']['input_ids_shape']}

## 数据流经过程

### 1. 视觉编码器 (SigLIP Vision Encoder)
"""

        if 'vision_encoder' in flow_data:
            ve_info = flow_data['vision_encoder']
            report += f"""
- **输出形状**: {ve_info['shape']}
- **数据类型**: {ve_info['dtype']}
- **均值**: {ve_info['mean']:.6f}
- **标准差**: {ve_info['std']:.6f}
- **设备**: {ve_info['device']}
- **功能**: 将输入图像转换为视觉特征向量
"""

        if 'vision_projection' in flow_data:
            vp_info = flow_data['vision_projection']
            report += f"""
### 2. 视觉投影层 (Vision Projection)
- **输出形状**: {vp_info['shape']}
- **数据类型**: {vp_info['dtype']}
- **均值**: {vp_info['mean']:.6f}
- **标准差**: {vp_info['std']:.6f}
- **功能**: 将视觉特征投影到语言模型的特征空间
"""

        if 'language_embedding' in flow_data:
            le_info = flow_data['language_embedding']
            report += f"""
### 3. 语言嵌入层 (Language Embedding)
- **输出形状**: {le_info['shape']}
- **数据类型**: {le_info['dtype']}
- **均值**: {le_info['mean']:.6f}
- **标准差**: {le_info['std']:.6f}
- **功能**: 将文本token转换为嵌入向量
"""

        # 注意力层信息
        attention_stages = [
            ('first_attention', '第一层注意力'),
            ('middle_attention', '中间层注意力'),
            ('last_attention', '最后层注意力')
        ]

        for stage_key, stage_name in attention_stages:
            if stage_key in flow_data:
                att_info = flow_data[stage_key]
                report += f"""
### 4. {stage_name} (Cross-Modal Attention)
- **输出形状**: {att_info['shape']}
- **数据类型**: {att_info['dtype']}
- **均值**: {att_info['mean']:.6f}
- **标准差**: {att_info['std']:.6f}
- **功能**: 融合视觉和文本信息，实现跨模态理解
"""

        if 'final_norm' in flow_data:
            fn_info = flow_data['final_norm']
            report += f"""
### 5. 最终归一化层 (Final Normalization)
- **输出形状**: {fn_info['shape']}
- **数据类型**: {fn_info['dtype']}
- **均值**: {fn_info['mean']:.6f}
- **标准差**: {fn_info['std']:.6f}
- **功能**: 对最终隐藏状态进行归一化
"""

        if 'lm_head' in flow_data:
            lm_info = flow_data['lm_head']
            report += f"""
### 6. 语言模型头 (Language Model Head)
- **输出形状**: {lm_info['shape']}
- **数据类型**: {lm_info['dtype']}
- **均值**: {lm_info['mean']:.6f}
- **标准差**: {lm_info['std']:.6f}
- **功能**: 将隐藏状态转换为词汇表上的概率分布
"""

        report += f"""
## 最终输出
- **生成文本**: "{flow_data['output']['generated_text']}"
- **输出token数量**: {flow_data['output']['output_tokens']}
- **序列形状**: {flow_data['output']['sequences_shape']}
- **分数数量**: {flow_data['output']['scores_length']}

## 数据流总结
1. **图像处理**: {flow_data['input']['image_size']} → 视觉特征向量
2. **文本处理**: "{flow_data['input']['text_prompt']}" → 嵌入向量
3. **多模态融合**: 通过交叉注意力机制融合视觉和文本信息
4. **序列生成**: 通过26层Transformer生成响应文本
5. **最终输出**: "{flow_data['output']['generated_text']}"

## 关键观察
- 模型成功将图像和文本信息融合
- 生成的文本与输入提示相关
- 数据在各个阶段保持了合理的数值范围
- 跨模态注意力机制有效工作
"""

        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(report)

        print(f"详细报告已保存到: {output_path}")
    
    def _add_flow_explanation(self, ax):
        """添加流程说明"""
        explanation = """
        Data Flow Process:
        1. Image features extracted by SigLIP vision encoder
        2. Text converted to embeddings
        3. Cross-modal projection aligns vision and text features
        4. Multi-modal fusion through cross-attention mechanism
        5. 26 Transformer layers process fused features
        6. Language model head generates text output
        """

        ax.text(0.5, 2, explanation, fontsize=10,
               bbox=dict(boxstyle="round,pad=0.5", facecolor='lightgray', alpha=0.8))

def main():
    """主函数"""
    model_path = "/home/<USER>/dataset/X/models/PaliGemma"
    image_path = "/home/<USER>/dataset/X/coke.png"
    text_prompt = "pick coke can"
    output_dir = "/home/<USER>/dataset/X/analysis/PaliGemma/results"
    
    os.makedirs(output_dir, exist_ok=True)
    
    print("=== PaliGemma 端到端数据流可视化 ===")
    
    # 创建可视化器
    visualizer = EndToEndFlowVisualizer(model_path)
    
    # 捕获数据流
    flow_data = visualizer.capture_data_flow(image_path, text_prompt)
    
    # 创建流程图
    flow_diagram_path = os.path.join(output_dir, "paligemma_end_to_end_flow.png")
    visualizer.create_flow_diagram(flow_diagram_path, flow_data)
    
    # 保存详细数据
    flow_data_path = os.path.join(output_dir, "flow_data.json")
    with open(flow_data_path, 'w', encoding='utf-8') as f:
        json.dump(flow_data, f, indent=2, ensure_ascii=False)

    # 生成详细报告
    report_path = os.path.join(output_dir, "end_to_end_flow_report.md")
    visualizer.create_detailed_flow_report(report_path, flow_data)

    # 生成交互式HTML可视化
    try:
        from interactive_flow_visualizer import InteractiveFlowVisualizer
        interactive_visualizer = InteractiveFlowVisualizer()
        interactive_html_path = os.path.join(output_dir, "interactive_flow.html")
        interactive_visualizer.create_interactive_html(flow_data, image_path, interactive_html_path)
        print(f"- 交互式可视化: {interactive_html_path}")
    except ImportError:
        print("- 交互式可视化: 需要interactive_flow_visualizer.py")

    print("\n=== 数据流分析完成 ===")
    print(f"输入图像: {flow_data['input']['image_size']}")
    print(f"输入文本: '{flow_data['input']['text_prompt']}'")
    print(f"生成文本: '{flow_data['output']['generated_text']}'")
    print(f"\n生成的文件:")
    print(f"- 流程图: {flow_diagram_path}")
    print(f"- 详细报告: {report_path}")
    print(f"- 原始数据: {flow_data_path}")

    print(f"\n=== 数据流各阶段形状变化 ===")
    for stage_name, stage_data in flow_data.items():
        if isinstance(stage_data, dict) and 'shape' in stage_data:
            print(f"{stage_name}: {stage_data['shape']}")

if __name__ == "__main__":
    main()
