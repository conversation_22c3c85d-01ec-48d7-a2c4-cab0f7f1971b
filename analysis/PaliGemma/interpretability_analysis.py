#!/usr/bin/env python3
"""
PaliGemma 可解释性分析工具
基于PyTorch hooks和Transformers配置，观察数据在模型内的流动

功能：
- Input在Attention计算后的结果
- 梯度流
- 残差流  
- Normalized结果
- QKV矩阵及其前后的结果
"""

import os
import re
import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict
from PIL import Image
from transformers import AutoProcessor, PaliGemmaForConditionalGeneration
import json
import pickle
from datetime import datetime

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class PaliGemmaInterpreter:
    def __init__(self, model_path, device="cuda:0"):
        """初始化PaliGemma可解释性分析器"""
        self.model_path = model_path
        self.device = device
        self.taps = defaultdict(list)  # 存储中间张量
        self.handles = []  # 存储hook句柄
        
        # 加载模型和处理器
        print("正在加载模型...")
        self.model = PaliGemmaForConditionalGeneration.from_pretrained(
            model_path,
            torch_dtype=torch.bfloat16,
            device_map=device,
            attn_implementation="eager",  # 确保能获取attention权重
        )
        self.processor = AutoProcessor.from_pretrained(model_path)
        
        # 配置模型输出
        self.model.config.output_attentions = True
        self.model.config.output_hidden_states = True
        self.model.config.return_dict = True
        
        print(f"模型加载完成，设备: {device}")
        
    def _save_tensor(self, name, tensor):
        """保存张量并设置梯度保留"""
        if isinstance(tensor, torch.Tensor):
            tensor.retain_grad()
            # 转换BFloat16到Float32以避免numpy兼容性问题
            saved_tensor = tensor.detach().cpu()
            if saved_tensor.dtype == torch.bfloat16:
                saved_tensor = saved_tensor.float()
            self.taps[name].append(saved_tensor)
        elif isinstance(tensor, (tuple, list)):
            for i, t in enumerate(tensor):
                if isinstance(t, torch.Tensor):
                    t.retain_grad()
                    saved_tensor = t.detach().cpu()
                    if saved_tensor.dtype == torch.bfloat16:
                        saved_tensor = saved_tensor.float()
                    self.taps[f"{name}:{i}"].append(saved_tensor)

    def add_hooks(self):
        """添加各种hooks来捕获中间结果"""
        print("正在添加hooks...")
        
        # 1) 残差流：对每层block记录pre/post
        for name, module in self.model.named_modules():
            if re.search(r"\blayers\.\d+\b$", name):  # 语言模型的block
                self.handles.append(module.register_forward_pre_hook(
                    lambda m, inp, n=name: self._save_tensor(f"{n}/resid_in", inp[0])
                ))
                self.handles.append(module.register_forward_hook(
                    lambda m, inp, out, n=name: self._save_tensor(f"{n}/resid_out", out)
                ))

        # 2) Norm：Gemma用RMSNorm
        for name, module in self.model.named_modules():
            cls = module.__class__.__name__.lower()
            if "rmsnorm" in cls or cls.endswith("norm"):
                self.handles.append(module.register_forward_hook(
                    lambda m, i, o, n=name: self._save_tensor(f"{n}/norm_out", o)
                ))

        # 3) 注意力 & MLP 的输出
        for name, module in self.model.named_modules():
            if name.endswith("self_attn"):
                self.handles.append(module.register_forward_hook(
                    lambda m, i, o, n=name: self._save_tensor(f"{n}/attn_out", o[0] if isinstance(o, tuple) else o)
                ))
            if name.endswith("mlp") or "feed_forward" in name:
                self.handles.append(module.register_forward_hook(
                    lambda m, i, o, n=name: self._save_tensor(f"{n}/mlp_out", o)
                ))

        # 4) Q/K/V（文本塔）：抓RoPE之前的线性投影结果
        for name, module in self.model.named_modules():
            if name.endswith(("q_proj", "k_proj", "v_proj")):
                self.handles.append(module.register_forward_hook(
                    lambda m, i, o, n=name: self._save_tensor(f"{n}", o)
                ))

        # 5) 视觉塔注意力（SigLIP是ViT变体）
        for name, module in self.model.named_modules():
            if module.__class__.__name__.lower().endswith("attention") and name.startswith("vision_model"):
                self.handles.append(module.register_forward_hook(
                    lambda m, i, o, n=name: self._save_tensor(f"{n}/attn_out", o)
                ))

        # 6) 反向：模块级full backward hook
        for name, module in self.model.named_modules():
            if re.search(r"\blayers\.\d+\b(self_attn|mlp)?$", name):
                self.handles.append(module.register_full_backward_hook(
                    lambda m, gin, gout, n=name: self._save_tensor(f"{n}/grad_in_out", (gin, gout))
                ))
        
        print(f"已添加 {len(self.handles)} 个hooks")

    def remove_hooks(self):
        """移除所有hooks"""
        for handle in self.handles:
            handle.remove()
        self.handles.clear()
        print("已移除所有hooks")

    def clear_taps(self):
        """清空存储的张量"""
        self.taps.clear()
        print("已清空张量存储")

    def analyze_image_text(self, image_path, text_prompt, output_dir="./analysis_results"):
        """分析图像和文本输入的数据流"""
        print(f"开始分析: 图像={image_path}, 文本='{text_prompt}'")
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 加载图像
        image = Image.open(image_path).convert("RGB")
        
        # 处理输入
        inputs = self.processor(images=image, text=text_prompt, return_tensors="pt").to(self.model.device)
        
        # 清空之前的记录
        self.clear_taps()
        
        # 添加hooks
        self.add_hooks()
        
        try:
            # 构造训练目标（用于反向传播）
            labels = inputs["input_ids"].clone()
            
            # 前向传播
            print("执行前向传播...")
            outputs = self.model(**inputs, labels=labels)
            
            # 反向传播
            print("执行反向传播...")
            loss = outputs.loss
            loss.backward()
            
            # 保存结果（处理BFloat16类型）
            results = {
                'timestamp': timestamp,
                'image_path': image_path,
                'text_prompt': text_prompt,
                'loss': loss.item(),
                'attention_maps': [attn.detach().cpu().float().numpy() for attn in outputs.attentions],
                'hidden_states': [hidden.detach().cpu().float().numpy() for hidden in outputs.hidden_states],
                'logits_shape': list(outputs.logits.shape),  # 转换为list以便JSON序列化
                'input_ids': inputs["input_ids"].cpu().numpy(),
                'taps_summary': {k: len(v) for k, v in self.taps.items()}
            }
            
            # 保存详细的中间结果
            print("保存分析结果...")
            
            # 保存基本信息（排除numpy数组）
            json_safe_results = {}
            for k, v in results.items():
                if k not in ['attention_maps', 'hidden_states', 'input_ids']:
                    json_safe_results[k] = v

            with open(f"{output_dir}/analysis_summary_{timestamp}.json", 'w', encoding='utf-8') as f:
                json.dump(json_safe_results, f, indent=2, ensure_ascii=False)
            
            # 保存attention maps
            np.save(f"{output_dir}/attention_maps_{timestamp}.npy", results['attention_maps'])
            
            # 保存hidden states
            np.save(f"{output_dir}/hidden_states_{timestamp}.npy", results['hidden_states'])
            
            # 保存taps（中间张量）
            with open(f"{output_dir}/taps_{timestamp}.pkl", 'wb') as f:
                pickle.dump(dict(self.taps), f)
            
            # 生成可视化
            self._visualize_results(results, output_dir, timestamp)

            # 分析QKV矩阵
            qkv_results = self.analyze_qkv_matrices(output_dir, timestamp)
            results['qkv_analysis'] = qkv_results
            
            print(f"分析完成！结果保存在: {output_dir}")
            return results
            
        finally:
            # 清理
            self.remove_hooks()

    def _visualize_results(self, results, output_dir, timestamp):
        """生成可视化结果"""
        print("生成可视化...")
        
        # 1. 注意力热力图
        self._plot_attention_heatmaps(results['attention_maps'], output_dir, timestamp)
        
        # 2. 隐藏状态分析
        self._plot_hidden_states_analysis(results['hidden_states'], output_dir, timestamp)
        
        # 3. 梯度流分析
        self._plot_gradient_flow(output_dir, timestamp)
        
        # 4. 残差流分析
        self._plot_residual_flow(output_dir, timestamp)

    def _plot_attention_heatmaps(self, attention_maps, output_dir, timestamp):
        """绘制注意力热力图"""
        if not attention_maps:
            return
            
        # 选择几个代表性的层进行可视化
        layers_to_plot = [0, len(attention_maps)//2, -1]  # 第一层、中间层、最后一层
        
        fig, axes = plt.subplots(1, len(layers_to_plot), figsize=(15, 5))
        if len(layers_to_plot) == 1:
            axes = [axes]
            
        for i, layer_idx in enumerate(layers_to_plot):
            if layer_idx < 0:
                layer_idx = len(attention_maps) + layer_idx
                
            # 取第一个头的注意力（平均所有头也可以）
            attn = attention_maps[layer_idx][0, 0]  # [seq_len, seq_len]
            
            sns.heatmap(attn, ax=axes[i], cmap='Blues', cbar=True)
            axes[i].set_title(f'Layer {layer_idx} Attention')
            axes[i].set_xlabel('Key Position')
            axes[i].set_ylabel('Query Position')
        
        plt.tight_layout()
        plt.savefig(f"{output_dir}/attention_heatmaps_{timestamp}.png", dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_hidden_states_analysis(self, hidden_states, output_dir, timestamp):
        """分析隐藏状态"""
        if not hidden_states:
            return
            
        # 计算每层的统计信息
        layer_stats = []
        for i, hidden in enumerate(hidden_states):
            stats = {
                'layer': i,
                'mean': np.mean(hidden),
                'std': np.std(hidden),
                'max': np.max(hidden),
                'min': np.min(hidden)
            }
            layer_stats.append(stats)
        
        # 绘制统计图
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        
        layers = [s['layer'] for s in layer_stats]
        
        axes[0,0].plot(layers, [s['mean'] for s in layer_stats], 'b-o')
        axes[0,0].set_title('Hidden States Mean by Layer')
        axes[0,0].set_xlabel('Layer')
        axes[0,0].set_ylabel('Mean Value')
        
        axes[0,1].plot(layers, [s['std'] for s in layer_stats], 'r-o')
        axes[0,1].set_title('Hidden States Std by Layer')
        axes[0,1].set_xlabel('Layer')
        axes[0,1].set_ylabel('Std Value')
        
        axes[1,0].plot(layers, [s['max'] for s in layer_stats], 'g-o')
        axes[1,0].set_title('Hidden States Max by Layer')
        axes[1,0].set_xlabel('Layer')
        axes[1,0].set_ylabel('Max Value')
        
        axes[1,1].plot(layers, [s['min'] for s in layer_stats], 'm-o')
        axes[1,1].set_title('Hidden States Min by Layer')
        axes[1,1].set_xlabel('Layer')
        axes[1,1].set_ylabel('Min Value')
        
        plt.tight_layout()
        plt.savefig(f"{output_dir}/hidden_states_analysis_{timestamp}.png", dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_gradient_flow(self, output_dir, timestamp):
        """分析梯度流"""
        gradient_data = {}
        
        for name, tensors in self.taps.items():
            if 'grad' in name and tensors:
                # 计算梯度的统计信息
                if isinstance(tensors[-1], (tuple, list)):
                    # 处理grad_in_out的情况
                    grad_in, grad_out = tensors[-1]
                    if grad_out and len(grad_out) > 0 and grad_out[0] is not None:
                        grad_norm = torch.norm(grad_out[0]).item()
                        gradient_data[name] = grad_norm
                else:
                    # 处理单个梯度张量
                    if tensors[-1].grad is not None:
                        grad_norm = torch.norm(tensors[-1].grad).item()
                        gradient_data[name] = grad_norm
        
        if gradient_data:
            plt.figure(figsize=(12, 6))
            names = list(gradient_data.keys())
            values = list(gradient_data.values())
            
            plt.bar(range(len(names)), values)
            plt.xticks(range(len(names)), names, rotation=45, ha='right')
            plt.title('Gradient Flow Analysis')
            plt.ylabel('Gradient Norm')
            plt.tight_layout()
            plt.savefig(f"{output_dir}/gradient_flow_{timestamp}.png", dpi=300, bbox_inches='tight')
            plt.close()

    def _plot_residual_flow(self, output_dir, timestamp):
        """分析残差流"""
        residual_data = {}
        
        for name, tensors in self.taps.items():
            if 'resid' in name and tensors:
                tensor = tensors[-1]
                norm = torch.norm(tensor).item()
                residual_data[name] = norm
        
        if residual_data:
            plt.figure(figsize=(12, 6))
            names = list(residual_data.keys())
            values = list(residual_data.values())
            
            plt.bar(range(len(names)), values)
            plt.xticks(range(len(names)), names, rotation=45, ha='right')
            plt.title('Residual Flow Analysis')
            plt.ylabel('Tensor Norm')
            plt.tight_layout()
            plt.savefig(f"{output_dir}/residual_flow_{timestamp}.png", dpi=300, bbox_inches='tight')
            plt.close()

    def analyze_qkv_matrices(self, output_dir, timestamp):
        """分析Q、K、V矩阵"""
        print("分析QKV矩阵...")

        qkv_data = {'q_proj': [], 'k_proj': [], 'v_proj': []}

        for name, tensors in self.taps.items():
            if any(proj in name for proj in ['q_proj', 'k_proj', 'v_proj']) and tensors:
                proj_type = next(proj for proj in ['q_proj', 'k_proj', 'v_proj'] if proj in name)
                tensor = tensors[-1]

                # 计算统计信息
                stats = {
                    'name': name,
                    'shape': tensor.shape,
                    'mean': torch.mean(tensor).item(),
                    'std': torch.std(tensor).item(),
                    'norm': torch.norm(tensor).item()
                }
                qkv_data[proj_type].append(stats)

        # 可视化QKV统计
        if any(qkv_data.values()):
            fig, axes = plt.subplots(1, 3, figsize=(15, 5))

            for i, (proj_type, data) in enumerate(qkv_data.items()):
                if data:
                    layers = [d['name'].split('.')[-2] for d in data]  # 提取层号
                    norms = [d['norm'] for d in data]

                    axes[i].bar(range(len(layers)), norms)
                    axes[i].set_title(f'{proj_type.upper()} Matrix Norms')
                    axes[i].set_xlabel('Layer')
                    axes[i].set_ylabel('Norm')
                    axes[i].set_xticks(range(len(layers)))
                    axes[i].set_xticklabels(layers, rotation=45)

            plt.tight_layout()
            plt.savefig(f"{output_dir}/qkv_analysis_{timestamp}.png", dpi=300, bbox_inches='tight')
            plt.close()

        return qkv_data

    def generate_detailed_report(self, results, output_dir, timestamp):
        """生成详细的分析报告"""
        report_path = f"{output_dir}/detailed_report_{timestamp}.md"

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(f"# PaliGemma 可解释性分析报告\n\n")
            f.write(f"**生成时间**: {results['timestamp']}\n")
            f.write(f"**图像路径**: {results['image_path']}\n")
            f.write(f"**文本提示**: {results['text_prompt']}\n")
            f.write(f"**损失值**: {results['loss']:.6f}\n\n")

            f.write(f"## 模型结构信息\n\n")
            f.write(f"- 注意力层数: {len(results['attention_maps'])}\n")
            f.write(f"- 隐藏状态层数: {len(results['hidden_states'])}\n")
            f.write(f"- 输入序列长度: {results['logits_shape'][1]}\n")
            f.write(f"- 输出维度: {results['logits_shape']}\n\n")

            f.write(f"## 捕获的中间张量\n\n")
            for name, count in sorted(results['taps_summary'].items()):
                f.write(f"- `{name}`: {count} 个张量\n")

            f.write(f"\n## 分析文件\n\n")
            f.write(f"- 注意力热力图: `attention_heatmaps_{timestamp}.png`\n")
            f.write(f"- 隐藏状态分析: `hidden_states_analysis_{timestamp}.png`\n")
            f.write(f"- 梯度流分析: `gradient_flow_{timestamp}.png`\n")
            f.write(f"- 残差流分析: `residual_flow_{timestamp}.png`\n")
            f.write(f"- QKV矩阵分析: `qkv_analysis_{timestamp}.png`\n")
            f.write(f"- 原始数据: `taps_{timestamp}.pkl`\n")

            f.write(f"\n## 使用说明\n\n")
            f.write(f"1. 查看各种可视化图表了解模型内部数据流\n")
            f.write(f"2. 使用pickle文件加载原始张量数据进行进一步分析\n")
            f.write(f"3. 注意力热力图显示了跨模态注意力模式\n")
            f.write(f"4. 梯度流分析有助于理解反向传播过程\n")

        print(f"详细报告已保存: {report_path}")

def main():
    """主函数"""
    # 配置路径
    model_path = "/home/<USER>/dataset/X/models/PaliGemma"
    image_path = "/home/<USER>/dataset/X/coke.png"
    text_prompt = "pick coke can"
    output_dir = "/home/<USER>/dataset/X/analysis/PaliGemma/results"

    print("=== PaliGemma 可解释性分析工具 ===")
    print(f"模型路径: {model_path}")
    print(f"图像路径: {image_path}")
    print(f"文本提示: '{text_prompt}'")
    print(f"输出目录: {output_dir}")

    # 创建分析器
    interpreter = PaliGemmaInterpreter(model_path)

    # 执行分析
    results = interpreter.analyze_image_text(image_path, text_prompt, output_dir)

    # 生成QKV分析
    qkv_results = interpreter.analyze_qkv_matrices(output_dir, results['timestamp'])

    # 生成详细报告
    interpreter.generate_detailed_report(results, output_dir, results['timestamp'])

    print("\n=== 分析结果摘要 ===")
    print(f"损失值: {results['loss']:.4f}")
    print(f"注意力层数: {len(results['attention_maps'])}")
    print(f"隐藏状态层数: {len(results['hidden_states'])}")
    print(f"捕获的中间张量类型: {len(results['taps_summary'])}")
    print(f"输入序列长度: {results['logits_shape'][1]}")

    print("\n=== 捕获的张量统计 ===")
    for name, count in results['taps_summary'].items():
        print(f"{name}: {count} 个张量")

    print("\n=== QKV矩阵统计 ===")
    for proj_type, data in qkv_results.items():
        if data:
            print(f"{proj_type.upper()}: {len(data)} 层")
            avg_norm = np.mean([d['norm'] for d in data])
            print(f"  平均范数: {avg_norm:.4f}")

if __name__ == "__main__":
    main()
