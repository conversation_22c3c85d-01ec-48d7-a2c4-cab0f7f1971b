#!/usr/bin/env python3
"""
PaliGemma 计算图可视化工具
生成交互式HTML可视化，展示模型的计算流程
"""

import os
import json
import torch
import torch.nn as nn
from transformers import AutoProcessor, PaliGemmaForConditionalGeneration
from collections import defaultdict, OrderedDict
import networkx as nx
from pyvis.network import Network
import re

class PaliGemmaGraphVisualizer:
    def __init__(self, model_path, device="cuda:0"):
        """初始化计算图可视化器"""
        self.model_path = model_path
        self.device = device
        self.graph = nx.DiGraph()
        self.module_info = {}
        self.layer_colors = {
            'input': '#4CAF50',      # 绿色 - 输入
            'embedding': '#2196F3',   # 蓝色 - 嵌入层
            'attention': '#FF9800',   # 橙色 - 注意力
            'mlp': '#9C27B0',        # 紫色 - MLP
            'norm': '#FFC107',       # 黄色 - 归一化
            'output': '#F44336',     # 红色 - 输出
            'vision': '#00BCD4',     # 青色 - 视觉编码器
            'language': '#8BC34A',   # 浅绿 - 语言模型
            'projection': '#E91E63', # 粉色 - 投影层
            'other': '#9E9E9E'       # 灰色 - 其他
        }
        
        print("正在加载模型...")
        self.model = PaliGemmaForConditionalGeneration.from_pretrained(
            model_path,
            torch_dtype=torch.bfloat16,
            device_map=device,
        )
        self.processor = AutoProcessor.from_pretrained(model_path)
        print("模型加载完成")
        
    def analyze_model_structure(self):
        """分析模型结构"""
        print("分析模型结构...")
        
        # 收集所有模块信息
        for name, module in self.model.named_modules():
            if name:  # 跳过根模块
                self.module_info[name] = {
                    'type': module.__class__.__name__,
                    'parameters': sum(p.numel() for p in module.parameters()),
                    'trainable_params': sum(p.numel() for p in module.parameters() if p.requires_grad),
                    'children': [child_name for child_name, _ in module.named_children()],
                    'parent': self._get_parent_name(name)
                }
        
        print(f"发现 {len(self.module_info)} 个模块")
        
    def _get_parent_name(self, module_name):
        """获取父模块名称"""
        parts = module_name.split('.')
        if len(parts) > 1:
            return '.'.join(parts[:-1])
        return None
    
    def _filter_important_modules(self, max_depth):
        """过滤重要模块，避免图表过于密集"""
        important_modules = {}

        for name, info in self.module_info.items():
            depth = len(name.split('.'))

            # 保留的条件：
            # 1. 深度不超过限制
            # 2. 是重要的模块类型
            # 3. 参数量较大的模块
            if depth <= max_depth:
                module_class = self._classify_module(name, info['type'])

                # 总是保留的重要模块
                if (module_class in ['vision', 'language', 'embedding', 'output', 'projection'] or
                    info['parameters'] > 1000000 or  # 参数量大于1M
                    'layers.' in name and depth <= 3):  # transformer层
                    important_modules[name] = info

                # 对于attention和mlp，只保留主要的
                elif module_class in ['attention', 'mlp'] and depth <= 2:
                    important_modules[name] = info

                # 对于norm，只保留层级较高的
                elif module_class == 'norm' and depth <= 2:
                    important_modules[name] = info

        return important_modules

    def _get_short_name(self, full_name):
        """获取简化的模块名称"""
        parts = full_name.split('.')

        # 特殊处理一些常见模式
        if 'vision_model' in full_name:
            return f"Vision.{parts[-1]}"
        elif 'language_model' in full_name:
            return f"Language.{parts[-1]}"
        elif 'layers' in parts:
            layer_idx = None
            for i, part in enumerate(parts):
                if part == 'layers' and i + 1 < len(parts):
                    layer_idx = parts[i + 1]
                    break
            if layer_idx is not None:
                return f"Layer{layer_idx}.{parts[-1]}"

        # 默认返回最后两个部分
        if len(parts) >= 2:
            return f"{parts[-2]}.{parts[-1]}"
        else:
            return parts[-1]

    def _classify_module(self, name, module_type):
        """分类模块类型"""
        name_lower = name.lower()
        type_lower = module_type.lower()

        if 'vision' in name_lower or 'siglip' in name_lower:
            return 'vision'
        elif 'language' in name_lower or 'gemma' in name_lower:
            return 'language'
        elif 'embedding' in type_lower or 'embed' in name_lower:
            return 'embedding'
        elif 'attention' in type_lower or 'attn' in name_lower:
            return 'attention'
        elif 'mlp' in type_lower or 'feed_forward' in name_lower:
            return 'mlp'
        elif 'norm' in type_lower or 'layernorm' in type_lower or 'rmsnorm' in type_lower:
            return 'norm'
        elif 'projection' in name_lower or 'proj' in name_lower:
            return 'projection'
        elif 'lm_head' in name_lower or 'classifier' in name_lower:
            return 'output'
        else:
            return 'other'
    
    def build_computation_graph(self, max_depth=2, include_params=True):
        """构建计算图"""
        print("构建计算图...")

        # 过滤重要模块，避免过于密集
        important_modules = self._filter_important_modules(max_depth)

        # 添加节点
        for name, info in important_modules.items():
            module_class = self._classify_module(name, info['type'])
            color = self.layer_colors.get(module_class, self.layer_colors['other'])

            # 创建简化的节点标签
            short_name = self._get_short_name(name)
            label = f"{short_name}\n{info['type']}"
            if include_params and info['parameters'] > 1000000:  # 只显示大于1M的参数
                param_str = self._format_params(info['parameters'])
                label += f"\n{param_str}"

            # 根据参数量和重要性调整节点大小
            base_size = 20
            if info['parameters'] > 100000000:  # >100M参数
                size = 60
            elif info['parameters'] > 10000000:  # >10M参数
                size = 40
            elif info['parameters'] > 1000000:  # >1M参数
                size = 30
            else:
                size = base_size

            # 添加节点
            self.graph.add_node(
                name,
                label=label,
                color=color,
                title=self._create_tooltip(name, info),
                group=module_class,
                size=size,
                font={'size': 12, 'color': 'black'}
            )
        
        # 添加边（父子关系）
        for name, info in self.module_info.items():
            depth = len(name.split('.'))
            if depth > max_depth:
                continue
                
            parent = info['parent']
            if parent and parent in self.graph.nodes:
                self.graph.add_edge(parent, name)
        
        print(f"计算图包含 {self.graph.number_of_nodes()} 个节点和 {self.graph.number_of_edges()} 条边")
    
    def _format_params(self, num_params):
        """格式化参数数量"""
        if num_params >= 1e9:
            return f"{num_params/1e9:.1f}B"
        elif num_params >= 1e6:
            return f"{num_params/1e6:.1f}M"
        elif num_params >= 1e3:
            return f"{num_params/1e3:.1f}K"
        else:
            return str(num_params)
    
    def _create_tooltip(self, name, info):
        """创建节点提示信息"""
        tooltip = f"<b>{name}</b><br>"
        tooltip += f"Type: {info['type']}<br>"
        tooltip += f"Parameters: {self._format_params(info['parameters'])}<br>"
        tooltip += f"Trainable: {self._format_params(info['trainable_params'])}<br>"
        if info['children']:
            tooltip += f"Children: {len(info['children'])}<br>"
        return tooltip
    
    def create_interactive_visualization(self, output_path, title="PaliGemma Computation Graph"):
        """创建交互式可视化"""
        print("创建交互式可视化...")
        
        # 创建pyvis网络
        net = Network(
            height="800px",
            width="100%",
            bgcolor="#ffffff",
            font_color="black",
            directed=True
        )
        
        # 设置物理引擎和布局
        net.set_options("""
        var options = {
          "physics": {
            "enabled": true,
            "stabilization": {"iterations": 200},
            "barnesHut": {
              "gravitationalConstant": -15000,
              "centralGravity": 0.1,
              "springLength": 200,
              "springConstant": 0.02,
              "damping": 0.15,
              "avoidOverlap": 1
            }
          },
          "layout": {
            "hierarchical": {
              "enabled": true,
              "direction": "UD",
              "sortMethod": "directed",
              "levelSeparation": 200,
              "nodeSpacing": 150,
              "treeSpacing": 200
            }
          },
          "interaction": {
            "dragNodes": true,
            "dragView": true,
            "zoomView": true,
            "selectConnectedEdges": false
          },
          "nodes": {
            "font": {
              "size": 14,
              "color": "black"
            },
            "borderWidth": 2,
            "shadow": true
          },
          "edges": {
            "color": {
              "color": "#848484",
              "highlight": "#FF0000"
            },
            "width": 2,
            "smooth": {
              "enabled": true,
              "type": "continuous"
            }
          }
        }
        """)
        
        # 从NetworkX图导入
        net.from_nx(self.graph)
        
        # 添加图例
        legend_html = self._create_legend()
        
        # 生成HTML
        html_content = net.generate_html()
        
        # 插入自定义样式和图例
        custom_html = self._customize_html(html_content, title, legend_html)
        
        # 保存文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(custom_html)
        
        print(f"可视化已保存到: {output_path}")
    
    def _create_legend(self):
        """创建图例"""
        legend_html = """
        <div id="legend" style="position: absolute; top: 10px; right: 10px;
                               background: rgba(255,255,255,0.95); padding: 20px;
                               border-radius: 8px; border: 2px solid #ddd;
                               font-family: Arial, sans-serif; font-size: 13px;
                               box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                               max-width: 300px;">
            <h3 style="margin-top: 0; color: #333; border-bottom: 2px solid #eee; padding-bottom: 10px;">
                PaliGemma 计算图说明
            </h3>

            <h4 style="margin: 15px 0 8px 0; color: #555;">模块类型颜色编码：</h4>
        """

        type_descriptions = {
            'vision': '视觉编码器 (SigLIP)',
            'language': '语言模型 (Gemma2)',
            'embedding': '嵌入层',
            'attention': '注意力机制',
            'mlp': 'MLP前馈网络',
            'norm': '归一化层 (RMSNorm)',
            'projection': '跨模态投影层',
            'output': '输出层 (LM Head)',
            'other': '其他模块'
        }

        for module_type, color in self.layer_colors.items():
            if module_type in type_descriptions:
                legend_html += f"""
                <div style="margin: 8px 0; display: flex; align-items: center;">
                    <span style="display: inline-block; width: 18px; height: 18px;
                                 background-color: {color}; margin-right: 10px;
                                 border-radius: 4px; border: 1px solid #ccc;"></span>
                    <span style="font-weight: 500;">{type_descriptions[module_type]}</span>
                </div>
                """

        legend_html += """
            <h4 style="margin: 20px 0 8px 0; color: #555;">节点大小：</h4>
            <div style="margin: 8px 0; font-size: 12px; color: #666;">
                • 大节点: >100M 参数<br>
                • 中节点: 10M-100M 参数<br>
                • 小节点: <10M 参数
            </div>

            <h4 style="margin: 20px 0 8px 0; color: #555;">交互说明：</h4>
            <div style="margin: 8px 0; font-size: 12px; color: #666;">
                • 拖拽节点重新布局<br>
                • 滚轮缩放查看细节<br>
                • 悬停查看模块信息<br>
                • 点击选中相关连接
            </div>

            <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #eee;
                        font-size: 11px; color: #888;">
                <strong>计算图展示：</strong><br>
                PaliGemma模型的主要组件及其层次关系，
                从视觉编码器到语言模型的信息流动路径。
            </div>
        </div>
        """
        return legend_html
    
    def _customize_html(self, html_content, title, legend_html):
        """自定义HTML内容"""
        # 添加标题和图例
        custom_html = html_content.replace(
            '<body>',
            f'''<body>
            <div style="text-align: center; padding: 20px; background: #f5f5f5; 
                        border-bottom: 1px solid #ddd;">
                <h1 style="margin: 0; color: #333;">{title}</h1>
                <p style="margin: 5px 0 0 0; color: #666;">
                    交互式计算图可视化 - 可拖拽、缩放、点击查看详情
                </p>
            </div>
            {legend_html}'''
        )
        
        return custom_html
    
    def generate_summary_report(self, output_dir):
        """生成模型结构摘要报告"""
        print("生成摘要报告...")
        
        # 统计信息
        stats = {
            'total_modules': len(self.module_info),
            'total_parameters': sum(info['parameters'] for info in self.module_info.values()),
            'trainable_parameters': sum(info['trainable_params'] for info in self.module_info.values()),
            'module_types': defaultdict(int),
            'layer_distribution': defaultdict(int)
        }
        
        for name, info in self.module_info.items():
            module_class = self._classify_module(name, info['type'])
            stats['module_types'][info['type']] += 1
            stats['layer_distribution'][module_class] += 1
        
        # 保存JSON报告
        report_path = os.path.join(output_dir, "model_structure_report.json")
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump({
                'model_path': self.model_path,
                'statistics': {
                    'total_modules': stats['total_modules'],
                    'total_parameters': stats['total_parameters'],
                    'trainable_parameters': stats['trainable_parameters'],
                    'parameter_efficiency': stats['trainable_parameters'] / stats['total_parameters'] if stats['total_parameters'] > 0 else 0
                },
                'module_types': dict(stats['module_types']),
                'layer_distribution': dict(stats['layer_distribution'])
            }, f, indent=2, ensure_ascii=False)
        
        print(f"摘要报告已保存到: {report_path}")
        return stats

def main():
    """主函数"""
    model_path = "/home/<USER>/dataset/X/models/PaliGemma"
    output_dir = "/home/<USER>/dataset/X/analysis/PaliGemma/results"
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    print("=== PaliGemma 计算图可视化 ===")
    
    # 创建可视化器
    visualizer = PaliGemmaGraphVisualizer(model_path)
    
    # 分析模型结构
    visualizer.analyze_model_structure()
    
    # 构建计算图
    visualizer.build_computation_graph(max_depth=4, include_params=True)
    
    # 创建可视化
    html_path = os.path.join(output_dir, "paligemma_computation_graph.html")
    visualizer.create_interactive_visualization(html_path)
    
    # 生成摘要报告
    stats = visualizer.generate_summary_report(output_dir)
    
    print("\n=== 模型结构统计 ===")
    print(f"总模块数: {stats['total_modules']}")
    print(f"总参数量: {visualizer._format_params(stats['total_parameters'])}")
    print(f"可训练参数: {visualizer._format_params(stats['trainable_parameters'])}")
    
    print("\n=== 层类型分布 ===")
    for layer_type, count in stats['layer_distribution'].items():
        print(f"{layer_type}: {count}")
    
    print(f"\n可视化文件已生成: {html_path}")
    print("请在浏览器中打开HTML文件查看交互式计算图")

if __name__ == "__main__":
    main()
