#!/usr/bin/env python3
"""
PaliGemma 交互式数据流可视化
生成HTML页面，展示动态的端到端数据流过程
"""

import os
import json
import base64
from PIL import Image
import io

class InteractiveFlowVisualizer:
    def __init__(self):
        """初始化交互式可视化器"""
        pass
    
    def create_interactive_html(self, flow_data, image_path, output_path):
        """创建交互式HTML可视化"""
        print("创建交互式数据流可视化...")
        
        # 读取并编码图像
        image_data = self._encode_image(image_path)
        
        # 生成HTML内容
        html_content = self._generate_html_template(flow_data, image_data)
        
        # 保存HTML文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"交互式可视化已保存到: {output_path}")
    
    def _encode_image(self, image_path):
        """将图像编码为base64"""
        try:
            with Image.open(image_path) as img:
                # 调整图像大小以适合显示
                img.thumbnail((300, 300), Image.Resampling.LANCZOS)
                buffer = io.BytesIO()
                img.save(buffer, format='PNG')
                img_str = base64.b64encode(buffer.getvalue()).decode()
                return f"data:image/png;base64,{img_str}"
        except Exception as e:
            print(f"图像编码失败: {e}")
            return ""
    
    def _generate_html_template(self, flow_data, image_data):
        """生成HTML模板"""
        return f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PaliGemma 交互式数据流可视化</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }}
        
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        
        .header {{
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        
        .header h1 {{
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }}
        
        .header p {{
            margin: 10px 0 0 0;
            opacity: 0.8;
            font-size: 1.1em;
        }}
        
        .flow-container {{
            padding: 40px;
            background: #f8f9fa;
        }}
        
        .flow-step {{
            display: flex;
            align-items: center;
            margin: 30px 0;
            padding: 25px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            opacity: 0.3;
        }}
        
        .flow-step.active {{
            opacity: 1;
            transform: translateX(10px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }}
        
        .step-number {{
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5em;
            font-weight: bold;
            margin-right: 25px;
            flex-shrink: 0;
        }}
        
        .step-content {{
            flex: 1;
        }}
        
        .step-title {{
            font-size: 1.4em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }}
        
        .step-description {{
            color: #7f8c8d;
            margin-bottom: 15px;
            line-height: 1.6;
        }}
        
        .step-data {{
            background: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            border-left: 4px solid #3498db;
        }}
        
        .input-section {{
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }}
        
        .input-image {{
            text-align: center;
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }}
        
        .input-image img {{
            max-width: 100%;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }}
        
        .input-text {{
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }}
        
        .controls {{
            text-align: center;
            margin: 30px 0;
        }}
        
        .btn {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
        }}
        
        .btn:hover {{
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }}
        
        .output-section {{
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-top: 30px;
            text-align: center;
        }}
        
        .output-text {{
            font-size: 1.3em;
            font-weight: 500;
            background: rgba(255,255,255,0.2);
            padding: 20px;
            border-radius: 8px;
            margin-top: 15px;
        }}
        
        .progress-bar {{
            width: 100%;
            height: 6px;
            background: #ecf0f1;
            border-radius: 3px;
            margin: 20px 0;
            overflow: hidden;
        }}
        
        .progress-fill {{
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            width: 0%;
            transition: width 0.5s ease;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>PaliGemma 数据流可视化</h1>
            <p>从图像和文本输入到生成输出的完整计算过程</p>
        </div>
        
        <div class="flow-container">
            <div class="input-section">
                <div class="input-image">
                    <h3>输入图像</h3>
                    <img src="{image_data}" alt="输入图像">
                    <p>尺寸: {flow_data.get('input', {}).get('image_size', 'Unknown')}</p>
                </div>
                <div class="input-text">
                    <h3>输入文本</h3>
                    <p style="font-size: 1.2em; font-weight: 500; color: #2c3e50;">
                        "{flow_data.get('input', {}).get('text_prompt', 'Unknown')}"
                    </p>
                    <p>Token数量: {flow_data.get('input', {}).get('input_tokens', 'Unknown')}</p>
                </div>
            </div>
            
            <div class="controls">
                <button class="btn" onclick="startAnimation()">开始数据流动画</button>
                <button class="btn" onclick="resetAnimation()">重置</button>
                <button class="btn" onclick="stepByStep()">逐步执行</button>
            </div>
            
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            
            {self._generate_flow_steps(flow_data)}
            
            <div class="output-section">
                <h3>生成输出</h3>
                <div class="output-text">
                    "{flow_data.get('output', {}).get('generated_text', 'No output generated')}"
                </div>
                <p>输出Token数量: {flow_data.get('output', {}).get('output_tokens', 'Unknown')}</p>
            </div>
        </div>
    </div>
    
    <script>
        let currentStep = 0;
        let isAnimating = false;
        let animationInterval;
        
        const steps = document.querySelectorAll('.flow-step');
        const progressFill = document.getElementById('progressFill');
        
        function startAnimation() {{
            if (isAnimating) return;
            
            isAnimating = true;
            currentStep = 0;
            resetSteps();
            
            animationInterval = setInterval(() => {{
                if (currentStep < steps.length) {{
                    steps[currentStep].classList.add('active');
                    updateProgress();
                    currentStep++;
                }} else {{
                    clearInterval(animationInterval);
                    isAnimating = false;
                }}
            }}, 1500);
        }}
        
        function resetAnimation() {{
            clearInterval(animationInterval);
            isAnimating = false;
            currentStep = 0;
            resetSteps();
            progressFill.style.width = '0%';
        }}
        
        function stepByStep() {{
            if (isAnimating) return;
            
            if (currentStep < steps.length) {{
                steps[currentStep].classList.add('active');
                updateProgress();
                currentStep++;
            }}
        }}
        
        function resetSteps() {{
            steps.forEach(step => step.classList.remove('active'));
        }}
        
        function updateProgress() {{
            const progress = (currentStep / steps.length) * 100;
            progressFill.style.width = progress + '%';
        }}
        
        // 添加点击事件
        steps.forEach((step, index) => {{
            step.addEventListener('click', () => {{
                if (!isAnimating) {{
                    resetSteps();
                    for (let i = 0; i <= index; i++) {{
                        steps[i].classList.add('active');
                    }}
                    currentStep = index + 1;
                    updateProgress();
                }}
            }});
        }});
    </script>
</body>
</html>
        """
    
    def _generate_flow_steps(self, flow_data):
        """生成流程步骤HTML"""
        steps = [
            {
                'title': '视觉编码器 (SigLIP)',
                'description': '将输入图像转换为高维特征向量，提取视觉语义信息',
                'data_key': 'vision_encoder'
            },
            {
                'title': '视觉投影层',
                'description': '将视觉特征投影到语言模型的特征空间，实现模态对齐',
                'data_key': 'vision_projection'
            },
            {
                'title': '语言嵌入层',
                'description': '将输入文本token转换为密集的嵌入向量表示',
                'data_key': 'language_embedding'
            },
            {
                'title': '多模态融合 (交叉注意力)',
                'description': '通过注意力机制融合视觉和文本信息，建立跨模态理解',
                'data_key': 'first_attention'
            },
            {
                'title': 'Transformer层处理',
                'description': '通过26层Transformer进行深度特征提取和序列建模',
                'data_key': 'middle_attention'
            },
            {
                'title': '最终归一化',
                'description': '对最终隐藏状态进行归一化，稳定数值范围',
                'data_key': 'final_norm'
            },
            {
                'title': '语言模型头',
                'description': '将隐藏状态映射到词汇表，生成下一个token的概率分布',
                'data_key': 'lm_head'
            }
        ]
        
        html = ""
        for i, step in enumerate(steps, 1):
            data_info = flow_data.get(step['data_key'], {})
            
            if data_info:
                data_content = f"""
                形状: {data_info.get('shape', 'Unknown')}
                数据类型: {data_info.get('dtype', 'Unknown')}
                均值: {data_info.get('mean', 0):.6f}
                标准差: {data_info.get('std', 0):.6f}
                设备: {data_info.get('device', 'Unknown')}
                """
            else:
                data_content = "数据未捕获"
            
            html += f"""
            <div class="flow-step">
                <div class="step-number">{i}</div>
                <div class="step-content">
                    <div class="step-title">{step['title']}</div>
                    <div class="step-description">{step['description']}</div>
                    <div class="step-data">{data_content}</div>
                </div>
            </div>
            """
        
        return html

def main():
    """主函数"""
    # 这个脚本需要与end_to_end_flow_visualizer.py配合使用
    print("请先运行 end_to_end_flow_visualizer.py 生成数据流数据")
    print("然后使用以下代码创建交互式可视化:")
    print("""
from interactive_flow_visualizer import InteractiveFlowVisualizer
import json

# 加载数据流数据
with open('results/flow_data.json', 'r') as f:
    flow_data = json.load(f)

# 创建交互式可视化
visualizer = InteractiveFlowVisualizer()
visualizer.create_interactive_html(
    flow_data, 
    '/home/<USER>/dataset/X/coke.png',
    'results/interactive_flow.html'
)
    """)

if __name__ == "__main__":
    main()
