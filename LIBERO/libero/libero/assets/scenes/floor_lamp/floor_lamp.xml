<mujoco model="floor_lamp">
  <asset>
  <texture file="lamp_texture.png" name="tex-floor_lamp" type="2d" />
  <material name="floor_lamp" reflectance="0.5" texrepeat="1 1" texture="tex-floor_lamp" texuniform="false" />
  <mesh file="visual/floor_lamp_vis.msh" name="floor_lamp_vis" scale="1.0 1.0 1.0" /><mesh file="collision/floor_lamp_ch.stl" name="floor_lamp_coll" scale="1.0 1.0 1.0" /></asset>
  <worldbody>
    <body>
      <body name="object">
      <geom solimp="0.998 0.998 0.001" solref="0.001 1" density="100" friction="0.95 0.3 0.1" type="mesh" mesh="floor_lamp_vis" conaffinity="0" contype="0" group="1" material="floor_lamp" /><geom solimp="0.998 0.998 0.001" solref="0.001 1" density="100" friction="0.95 0.3 0.1" type="mesh" mesh="floor_lamp_coll" group="0" rgba="0.8 0.8 0.8 0.0" /></body>
      <site rgba="0 0 0 0" size="0.005" pos="0 0 -0.06" name="bottom_site" />
      <site rgba="0 0 0 0" size="0.005" pos="0 0 0.04" name="top_site" />
      <site rgba="0 0 0 0" size="0.005" pos="0.025 0.025 0" name="horizontal_radius_site" />
    </body>
  </worldbody>
</mujoco>