<mujoco model="table_arena">
  <asset>
    <texture builtin="gradient" height="256" rgb1=".9 .9 1." rgb2=".2 .3 .4" type="skybox" width="256"/>
    <texture file="../textures/tile_grigia_caldera_porcelain_floor.png" type="2d" name="texplane"/>
    <material name="floorplane" reflectance="0.0" shininess="0.0"
	      specular="0.0" texrepeat="3 3" texture="texplane" texuniform="true"/>
    <!-- ceramic table texture and material-->
    <texture file="../textures/martin_novak_wood_table.png" type="cube" name="tex-table"/>
    <material name="table_texture" reflectance="0.0" shininess="0.0" specular="0.2" texrepeat="1 1" texture="tex-table" />
    <!-- steel legs -->
    <texture file="../textures/martin_novak_wood_table.png" type="cube" name="tex-table-legs"/>
    <material name="table_legs" reflectance="0.8" shininess="0.8" texrepeat="1 1" texture="tex-table-legs" />
    <!-- plaster walls -->
    <texture file="../textures/smooth_light_gray_plaster.png" type="2d" name="tex-wall"/>

    <material name="walls_mat" reflectance="0.0" shininess="0.1" specular="0.8" texrepeat="3 3" texture="tex-wall" texuniform="true" />
    <!-- added table texture and material for domain randomization -->
    <texture  name="textable" builtin="flat" height="512" width="512" rgb1="0.5 0.5 0.5" rgb2="0.5 0.5 0.5"/>
    <material name="table_mat" texture="textable" />

    <!-- <material name="wall_frame_mat" specular="0.5" shininess="0.45" rgba="1.0 0.0 .0 1.000000"/>     -->
    <!-- <mesh file="wall_frames.stl" name="wall_frames" scale="0.2 0.1 0.1" /> -->
    <texture file="study_wall_painting/study_wall_painting.png" name="tex-study_wall_painting" type="2d"/>
    <material name="study_wall_painting" reflectance="0.5" texrepeat="1 1" texture="tex-study_wall_painting" texuniform="false"/>
    <mesh file="study_wall_painting/visual/study_wall_painting_vis.msh" name="study_wall_painting_vis" scale="1.0 1.0 1.0"/>

    <texture file="office_book_shelf/ceramic.png" name="tex-office_book_shelf" type="2d"/>
    <material name="office_book_shelf" reflectance="0.5" texrepeat="1 1" texture="tex-office_book_shelf" texuniform="false"/>
    <mesh file="office_book_shelf/visual/office_book_shelf_vis.msh" name="office_book_shelf_vis" scale="0.7 0.7 0.7"/>

    <texture file="plant/plant_texture.png" name="tex-plant" type="2d"/>
    <material name="plant" reflectance="0.5" texrepeat="1 1" texture="tex-plant" texuniform="false"/>
    <mesh file="plant/visual/plant_vis.msh" name="plant_vis" scale="0.7 0.7 0.7"/>

    <texture file="black_book/black_book_texture.png" name="tex-black-book" type="2d"/>
    <material name="black_book" reflectance="0.5" texrepeat="1 1" texture="tex-black-book" texuniform="false"/>
    <mesh file="black_book/visual/black_book_vis.msh" name="black_book_vis" scale="0.7 0.7 0.7"/>

    <!-- <texture file="living_room_table/living_room_table_texture.png" name="tex-living_room_table" type="2d"/>
    <material name="living_room_table" reflectance="0.5" texrepeat="1 1" texture="tex-living_room_table" texuniform="false"/>
    <mesh file="living_room_table/visual/living_room_table_vis.msh" name="living_room_table_vis" scale="1.0 1.0 1.0"/> -->

    <texture file="desk/desk_texture.png" name="tex-desk" type="2d"/>
    <material name="desk" reflectance="0.5" texrepeat="1 1" texture="tex-desk" texuniform="false"/>
    <mesh file="desk/visual/desk_vis.msh" name="desk_vis" scale="1.0 1.0 1.0"/>

    <texture file="floor_lamp/lamp_texture.png" name="tex-floor_lamp" type="2d"/>
    <material name="floor_lamp" reflectance="0.5" texrepeat="1 1" texture="tex-floor_lamp" texuniform="false"/>
    <mesh file="floor_lamp/visual/floor_lamp_vis.msh" name="floor_lamp_vis" scale="1.0 1.0 1.0"/>

  </asset>
  <worldbody>
    <!-- Floor -->
    <geom condim="3" group="1" material="floorplane" name="floor" pos="0 0 0" size="3 3 .125" type="plane"/>

    <geom pos="-1.9 0.5 1." quat="0.7071068 0 0 -0.7071068" type="mesh" mesh="study_wall_painting_vis" conaffinity="0" contype="0" group="1" name="study_wall_painging" material="study_wall_painting"/>

    <body name="study_table" pos="0 0 0" quat="1 0 0 0">
        <geom solimp="0.998 0.998 0.001" solref="0.001 1" density="100" friction="0.95 0.3 0.1" type="mesh" mesh="desk_vis" conaffinity="0" contype="0" group="1" material="desk"/>
        <geom solimp="0.998 0.998 0.001" solref="0.001 1" density="100" friction="0.95 0.3 0.1" type="box" pos="-0.00000 0.00000 0.84929" quat="0.50000 -0.50000 0.50000 0.50000" size="0.03584 0.68946 1.39273" group="0" rgba="0.8 0.8 0.8 0.3"/>
        <geom solimp="0.998 0.998 0.001" solref="0.001 1" density="100" friction="0.95 0.3 0.1" type="box" pos="-0.50048 -0.69064 0.39431" quat="0.00000 0.00000 1.00000 0.00000" size="0.02002 0.05031 0.39407" group="0" rgba="0.8 0.8 0.8 0.3"/>
        <geom solimp="0.998 0.998 0.001" solref="0.001 1" density="100" friction="0.95 0.3 0.1" type="box" pos="0.49617 -0.69064 0.39431" quat="1.00000 -0.00000 -0.00000 -0.00000" size="0.02002 0.05031 0.39407" group="0" rgba="0.8 0.8 0.8 0.3"/>
        <geom solimp="0.998 0.998 0.001" solref="0.001 1" density="100" friction="0.95 0.3 0.1" type="box" pos="0.49617 0.70968 0.39431" quat="0.00000 0.00000 1.00000 0.00000" size="0.02002 0.05032 0.39407" group="0" rgba="0.8 0.8 0.8 0.3"/>
        <geom solimp="0.998 0.998 0.001" solref="0.001 1" density="100" friction="0.95 0.3 0.1" type="box" pos="-0.50048 0.71473 0.39431" quat="1.00000 -0.00000 -0.00000 -0.00000" size="0.02002 0.05031 0.39407" group="0" rgba="0.8 0.8 0.8 0.3"/>
        <geom solimp="0.998 0.998 0.001" solref="0.001 1" density="100" friction="0.95 0.3 0.1" type="box" pos="-0.00488 0.71473 0.02528" quat="0.70811 0.00000 -0.70610 -0.00000" size="0.02002 0.05031 0.51262" group="0" rgba="0.8 0.8 0.8 0.3"/>
        <geom solimp="0.998 0.998 0.001" solref="0.001 1" density="100" friction="0.95 0.3 0.1" type="box" pos="-0.00488 -0.69765 0.02528" quat="0.70811 0.00000 -0.70610 -0.00000" size="0.02002 0.05032 0.51262" group="0" rgba="0.8 0.8 0.8 0.3"/>    
     </body>
    <!-- <geom pos="-1.85 0.5 0" quat="0.7071068 0 0 0.7071068" type="mesh" mesh="study_background" conaffinity="0" contype="0" group="1" name="plant" material="study_background"/> -->
    <geom pos="0.3 -0.6 0.9" quat="0.7071068 0 0 -0.7071068" solimp="0.998 0.998 0.001" solref="0.001 1" density="100" friction="0.95 0.3 0.1" type="mesh" mesh="floor_lamp_vis" conaffinity="0" contype="0" group="1" material="floor_lamp"/>
    <!-- <geom pos="0.3 -0.25 1.6" conaffinity="0" contype="0" group="1"  quat="1 0 0 0" type="sphere" rgba="1 0 0 1" size="0.05"/> -->
    <!-- <light pos="0.3 -0.25 1.6" name="lamp_light" diffuse=".8 0.8 0." dir="0 0 -1" directional="false" specular="0.3 0.3 0.3" castshadow="false"/> -->

    <geom pos="-1.85 0.5 0" quat="0.7071068 0 0 0.7071068" type="mesh" mesh="plant_vis" conaffinity="0" contype="0" group="1" name="plant" material="plant"/>
    <geom pos="-1.7 -1.0 0" quat="0.7071068 0 0 -0.7071068" type="mesh" mesh="office_book_shelf_vis" conaffinity="0" contype="0" group="1" name="office_book_shelf" material="office_book_shelf"/>
    
    <geom pos="-1.7 -0.50 0.9" quat="0.7071068 0 0 -0.7071068" type="mesh" mesh="black_book_vis" conaffinity="0" contype="0" group="1" name="black_book_1" material="black_book"/>
    <geom pos="-1.7 -0.45 0.9" quat="0.7071068 0 0 -0.7071068" type="mesh" mesh="black_book_vis" conaffinity="0" contype="0" group="1" name="black_book_2" material="black_book"/>

    <geom pos="-1.25 2.25 1.5" quat="0.6532815 0.6532815 0.2705981 0.2705981" size="1.06 1.5 0.01" type="box" conaffinity="0" contype="0" group="1" name="wall_leftcorner_visual" material="walls_mat"/>
    <geom pos="-1.25 -2.25 1.5" quat="0.6532815 0.6532815 -0.2705981 -0.2705981" size="1.06 1.5 0.01" type="box" conaffinity="0" contype="0" group="1" name="wall_rightcorner_visual" material="walls_mat"/>
    <geom pos="1.25 3 1.5" quat="0.7071 0.7071 0 0" size="1.75 1.5 0.01" type="box" conaffinity="0" contype="0" group="1" name="wall_left_visual" material="walls_mat"/>
    <geom pos="1.25 -3 1.5" quat="0.7071 -0.7071 0 0" size="1.75 1.5 0.01" type="box" conaffinity="0" contype="0" group="1" name="wall_right_visual" material="walls_mat"/>
    <geom pos="-2 0 1.5" quat="0.5 0.5 0.5 0.5" size="1.5 1.5 0.01" type="box" conaffinity="0" contype="0" group="1" name="wall_rear_visual" material="walls_mat"/>
    <geom pos="3 0 1.5" quat="0.5 0.5 -0.5 -0.5" size="3 1.5 0.01" type="box" conaffinity="0" contype="0" group="1" name="wall_front_visual" material="walls_mat"/>

    <light name="light1" diffuse=".8 .8 .8" dir="0 -.15 -1" directional="false" pos="1 1 4.0" specular="0.3 0.3 0.3" castshadow="false"/>
    <light name="light2" diffuse=".8 .8 .8" dir="0 -.15 -1" directional="false" pos="-3. -3. 4.0" specular="0.3 0.3 0.3" castshadow="false"/>

    <!-- front view -->
    <camera mode="fixed" name="frontview" pos="1.0 0 1.45" quat="0.56 0.43 0.43 0.56"/>
    <!-- bird view -->
    <camera mode="fixed" name="birdview" pos="-0.2 0 3.0" quat="0.7071 0 0 0.7071"/>
    <!-- agent view -->
    <camera mode="fixed" name="agentview" pos="0.5 0 1.35" quat="0.653 0.271 0.271 0.653"/>

    <!-- side view -->
    <camera mode="fixed" name="sideview" pos="-0.05651774593317116 1.2761224129427358 1.4879572214102434" quat="0.009905065491771751 0.006877963156909582 0.5912228352893879 0.806418094001364" />
  </worldbody>
</mujoco>
