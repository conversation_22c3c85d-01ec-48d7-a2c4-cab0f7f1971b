<mujoco model="kitchen_background_pot">
  <asset>
  <texture file="kitchen_background_pot.png" name="tex-kitchen_background_pot" type="2d" />
  <material name="kitchen_background_pot" reflectance="0.5" texrepeat="1 1" texture="tex-kitchen_background_pot" texuniform="false" />
  <mesh file="visual/kitchen_background_pot_vis.msh" name="kitchen_background_pot_vis" scale="0.001 0.001 0.001" /><mesh file="collision/kitchen_background_pot_ch.stl" name="kitchen_background_pot_coll" scale="0.001 0.001 0.001" /></asset>
  <worldbody>
    <body>
      <body name="object">
      <geom solimp="0.998 0.998 0.001" solref="0.001 1" density="100" friction="0.95 0.3 0.1" type="mesh" mesh="kitchen_background_pot_vis" conaffinity="0" contype="0" group="1" material="kitchen_background_pot" /><geom solimp="0.998 0.998 0.001" solref="0.001 1" density="100" friction="0.95 0.3 0.1" type="mesh" mesh="kitchen_background_pot_coll" group="0" rgba="0.8 0.8 0.8 0.0" /></body>
      <site rgba="0 0 0 0" size="0.005" pos="0 0 -0.06" name="bottom_site" />
      <site rgba="0 0 0 0" size="0.005" pos="0 0 0.04" name="top_site" />
      <site rgba="0 0 0 0" size="0.005" pos="0.025 0.025 0" name="horizontal_radius_site" />
    </body>
  </worldbody>
</mujoco>