<mujoco model="wine_rack_planes">
  <asset>
  <texture file="light-wood.png" name="tex-wine_rack_planes" type="2d" />
  <material name="wine_rack_planes" reflectance="0.5" texrepeat="1 1" texture="tex-wine_rack_planes" texuniform="false" />
  <mesh file="visual/wine_rack_planes_vis.msh" name="wine_rack_planes_vis" scale="0.0045 0.0045 0.0045" /></asset>
  <worldbody>
    <body>
      <body name="object">
      <geom solimp="0.998 0.998 0.001" solref="0.001 1" density="100" friction="0.95 0.3 0.1" type="mesh" mesh="wine_rack_planes_vis" conaffinity="0" contype="0" group="1" material="wine_rack_planes" /><geom solimp="0.998 0.998 0.001" solref="0.001 1" density="100" friction="0.95 0.3 0.1" type="box" pos="-0.00000 -0.00610 0.25118" quat="0.68093 0.19061 0.68093 0.19061" size="0.00450 0.07894 0.13438" group="0" rgba="0.8 0.8 0.8 0.3" /><geom solimp="0.998 0.998 0.001" solref="0.001 1" density="100" friction="0.95 0.3 0.1" type="box" pos="0.00000 -0.04273 0.12727" quat="0.68093 0.19061 -0.68093 -0.19061" size="0.00450 0.07894 0.13438" group="0" rgba="0.8 0.8 0.8 0.3" /></body>
      <site rgba="0 0 0 0" size="0.005" pos="0 0 -0.06" name="bottom_site" />
      <site rgba="0 0 0 0" size="0.005" pos="0 0 0.04" name="top_site" />
      <site rgba="0 0 0 0" size="0.005" pos="0.025 0.025 0" name="horizontal_radius_site" />
    </body>
  </worldbody>
</mujoco>