(define (problem LIBERO_Floor_Manipulation)
  (:domain robosuite)
  (:language Pick the bbq sauce and place it in the basket)
    (:regions
      (bin_region
          (:target floor)
          (:ranges (
              (-0.01 0.25 0.01 0.27)
            )
          )
      )
      (target_object_region
          (:target floor)
          (:ranges (
              (0.025 -0.125 0.07500000000000001 -0.07500000000000001)
            )
          )
      )
      (other_object_region_0
          (:target floor)
          (:ranges (
              (-0.145 -0.265 -0.095 -0.215)
            )
          )
      )
      (other_object_region_1
          (:target floor)
          (:ranges (
              (-0.175 0.034999999999999996 -0.125 0.08499999999999999)
            )
          )
      )
      (other_object_region_2
          (:target floor)
          (:ranges (
              (0.07500000000000001 -0.225 0.125 -0.17500000000000002)
            )
          )
      )
      (other_object_region_3
          (:target floor)
          (:ranges (
              (0.125 0.0049999999999999975 0.175 0.055)
            )
          )
      )
      (other_object_region_4
          (:target floor)
          (:ranges (
              (-0.225 -0.10500000000000001 -0.17500000000000002 -0.055)
            )
          )
      )
      (contain_region
          (:target basket_1)
      )
    )

  (:fixtures
    floor - floor
  )

  (:objects
    bbq_sauce_1 - bbq_sauce
    basket_1 - basket
    chocolate_pudding_1 - chocolate_pudding
    ketchup_1 - ketchup
    salad_dressing_1 - salad_dressing
    alphabet_soup_1 - alphabet_soup
    cream_cheese_1 - cream_cheese
  )

  (:obj_of_interest
    bbq_sauce_1
    basket_1
  )

  (:init
    (On bbq_sauce_1 floor_target_object_region)
    (On chocolate_pudding_1 floor_other_object_region_0)
    (On ketchup_1 floor_other_object_region_1)
    (On salad_dressing_1 floor_other_object_region_2)
    (On alphabet_soup_1 floor_other_object_region_3)
    (On cream_cheese_1 floor_other_object_region_4)
    (On basket_1 floor_bin_region)
  )

  (:goal
    (And (In bbq_sauce_1 basket_1_contain_region))
  )

)
