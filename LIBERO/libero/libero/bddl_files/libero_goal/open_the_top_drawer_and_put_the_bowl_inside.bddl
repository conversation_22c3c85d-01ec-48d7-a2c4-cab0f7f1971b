(define (problem LIBERO_Tabletop_Manipulation)
  (:domain robosuite)
  (:language Open the top layer of the drawer and put the bowl inside)
    (:regions
      (plate_region
          (:target main_table)
          (:ranges (
              (0.04 -0.03 0.060000000000000005 -0.01)
            )
          )
      )
      (akita_black_bowl_region
          (:target main_table)
          (:ranges (
              (-0.09999999999999999 -0.01 -0.08 0.01)
            )
          )
      )
      (wine_bottle_region
          (:target main_table)
          (:ranges (
              (-0.21000000000000002 -0.060000000000000005 -0.19 -0.04)
            )
          )
      )
      (cream_cheese_region
          (:target main_table)
          (:ranges (
              (-0.060000000000000005 0.12000000000000001 -0.04 0.14)
            )
          )
      )
      (stove_front_region
          (:target main_table)
          (:ranges (
              (-0.09 0.16999999999999998 -0.010000000000000002 0.25)
            )
          )
      )
      (cabinet_region
          (:target main_table)
          (:ranges (
              (0.02 -0.25 0.04 -0.23)
            )
          )
          (:yaw_rotation (
              (3.141592653589793 3.141592653589793)
            )
          )
      )
      (stove_region
          (:target main_table)
          (:ranges (
              (-0.42 0.2 -0.4 0.22)
            )
          )
      )
      (wine_rack_region
          (:target main_table)
          (:ranges (
              (-0.27 -0.27 -0.25 -0.25)
            )
          )
          (:yaw_rotation (
              (3.141592653589793 3.141592653589793)
            )
          )
      )
      (top_region
          (:target wooden_cabinet_1)
      )
      (middle_region
          (:target wooden_cabinet_1)
      )
      (bottom_region
          (:target wooden_cabinet_1)
      )
      (top_side
          (:target wooden_cabinet_1)
      )
      (cook_region
          (:target flat_stove_1)
      )
      (right_region
          (:target bowl_drainer_1)
      )
      (left_region
          (:target bowl_drainer_1)
      )
      (top_region
          (:target wine_rack_1)
      )
    )

  (:fixtures
    main_table - table
    wooden_cabinet_1 - wooden_cabinet
    flat_stove_1 - flat_stove
    wine_rack_1 - wine_rack
  )

  (:objects
    akita_black_bowl_1 - akita_black_bowl
    cream_cheese_1 - cream_cheese
    wine_bottle_1 - wine_bottle
    plate_1 - plate
  )

  (:obj_of_interest
    akita_black_bowl_1
    wooden_cabinet_1_top_region
  )

  (:init
    (On wine_bottle_1 main_table_wine_bottle_region)
    (On akita_black_bowl_1 main_table_akita_black_bowl_region)
    (On plate_1 main_table_plate_region)
    (On cream_cheese_1 main_table_cream_cheese_region)
    (On wooden_cabinet_1 main_table_cabinet_region)
    (On flat_stove_1 main_table_stove_region)
    (On wine_rack_1 main_table_wine_rack_region)
  )

  (:goal
    (And (In akita_black_bowl_1 wooden_cabinet_1_top_region))
  )

)
