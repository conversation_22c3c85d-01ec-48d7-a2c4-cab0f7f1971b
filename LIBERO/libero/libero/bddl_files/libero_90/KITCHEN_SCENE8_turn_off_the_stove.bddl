(define (problem LIBERO_Kitchen_Tabletop_Manipulation)
  (:domain robosuite)
  (:language turn off the stove)
    (:regions
      (flat_stove_init_region
          (:target kitchen_table)
          (:ranges (
              (-0.21000000000000002 -0.21000000000000002 -0.19 -0.19)
            )
          )
          (:yaw_rotation (
              (0.0 0.0)
            )
          )
      )
      (moka_pot_right_init_region
          (:target kitchen_table)
          (:ranges (
              (-0.07500000000000001 0.225 -0.025 0.275)
            )
          )
          (:yaw_rotation (
              (0.0 0.0)
            )
          )
      )
      (moka_pot_left_init_region
          (:target kitchen_table)
          (:ranges (
              (0.025 0.025 0.07500000000000001 0.07500000000000001)
            )
          )
          (:yaw_rotation (
              (0.0 0.0)
            )
          )
      )
      (cook_region
          (:target flat_stove_1)
      )
    )

  (:fixtures
    kitchen_table - kitchen_table
    flat_stove_1 - flat_stove
  )

  (:objects
    moka_pot_1 moka_pot_2 - moka_pot
  )

  (:obj_of_interest
    flat_stove_1
  )

  (:init
    (On flat_stove_1 kitchen_table_flat_stove_init_region)
    (On moka_pot_1 kitchen_table_moka_pot_right_init_region)
    (On moka_pot_2 kitchen_table_moka_pot_left_init_region)
    (Turnon flat_stove_1)
  )

  (:goal
    (And (Turnoff flat_stove_1))
  )

)
