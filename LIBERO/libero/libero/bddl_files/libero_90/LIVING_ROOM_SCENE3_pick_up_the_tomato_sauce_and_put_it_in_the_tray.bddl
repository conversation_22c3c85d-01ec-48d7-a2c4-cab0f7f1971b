(define (problem LIBERO_Living_Room_Tabletop_Manipulation)
  (:domain robosuite)
  (:language pick up the tomato sauce and put it in the tray)
    (:regions
      (wooden_tray_init_region
          (:target living_room_table)
          (:ranges (
              (-0.01 0.25 0.01 0.27)
            )
          )
          (:yaw_rotation (
              (0.0 0.0)
            )
          )
      )
      (cream_cheese_init_region
          (:target living_room_table)
          (:ranges (
              (0.07500000000000001 -0.225 0.125 -0.17500000000000002)
            )
          )
          (:yaw_rotation (
              (0.0 0.0)
            )
          )
      )
      (tomato_sauce_init_region
          (:target living_room_table)
          (:ranges (
              (-0.125 0.025 -0.07500000000000001 0.07500000000000001)
            )
          )
          (:yaw_rotation (
              (0.0 0.0)
            )
          )
      )
      (alphabet_soup_init_region
          (:target living_room_table)
          (:ranges (
              (-0.125 -0.175 -0.07500000000000001 -0.125)
            )
          )
          (:yaw_rotation (
              (0.0 0.0)
            )
          )
      )
      (butter_init_region
          (:target living_room_table)
          (:ranges (
              (0.025 0.025 0.07500000000000001 0.07500000000000001)
            )
          )
          (:yaw_rotation (
              (0.0 0.0)
            )
          )
      )
      (ketchup_init_region
          (:target living_room_table)
          (:ranges (
              (-0.275 -0.175 -0.225 -0.125)
            )
          )
          (:yaw_rotation (
              (0.0 0.0)
            )
          )
      )
      (contain_region
          (:target wooden_tray_1)
      )
    )

  (:fixtures
    living_room_table - living_room_table
  )

  (:objects
    alphabet_soup_1 - alphabet_soup
    cream_cheese_1 - cream_cheese
    tomato_sauce_1 - tomato_sauce
    ketchup_1 - ketchup
    butter_1 - butter
    wooden_tray_1 - wooden_tray
  )

  (:obj_of_interest
    tomato_sauce_1
    wooden_tray_1
  )

  (:init
    (On alphabet_soup_1 living_room_table_alphabet_soup_init_region)
    (On cream_cheese_1 living_room_table_cream_cheese_init_region)
    (On tomato_sauce_1 living_room_table_tomato_sauce_init_region)
    (On ketchup_1 living_room_table_ketchup_init_region)
    (On butter_1 living_room_table_butter_init_region)
    (On wooden_tray_1 living_room_table_wooden_tray_init_region)
  )

  (:goal
    (And (In tomato_sauce_1 wooden_tray_1_contain_region))
  )

)
