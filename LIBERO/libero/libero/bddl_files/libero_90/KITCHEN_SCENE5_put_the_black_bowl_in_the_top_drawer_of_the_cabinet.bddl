(define (problem LIBERO_Kitchen_Tabletop_Manipulation)
  (:domain robosuite)
  (:language put the black bowl in the top drawer of the cabinet)
    (:regions
      (white_cabinet_init_region
          (:target kitchen_table)
          (:ranges (
              (-0.01 0.29 0.01 0.31)
            )
          )
          (:yaw_rotation (
              (0.0 0.0)
            )
          )
      )
      (akita_black_bowl_init_region
          (:target kitchen_table)
          (:ranges (
              (0.0049999999999999975 -0.07500000000000001 0.055 -0.025)
            )
          )
          (:yaw_rotation (
              (0.0 0.0)
            )
          )
      )
      (ketchup_init_region
          (:target kitchen_table)
          (:ranges (
              (-0.125 -0.125 -0.07500000000000001 -0.07500000000000001)
            )
          )
          (:yaw_rotation (
              (0.0 0.0)
            )
          )
      )
      (plate_init_region
          (:target kitchen_table)
          (:ranges (
              (-0.07500000000000001 -0.275 -0.025 -0.225)
            )
          )
          (:yaw_rotation (
              (0.0 0.0)
            )
          )
      )
      (top_side
          (:target white_cabinet_1)
      )
      (top_region
          (:target white_cabinet_1)
      )
      (middle_region
          (:target white_cabinet_1)
      )
      (bottom_region
          (:target white_cabinet_1)
      )
    )

  (:fixtures
    kitchen_table - kitchen_table
    white_cabinet_1 - white_cabinet
  )

  (:objects
    akita_black_bowl_1 - akita_black_bowl
    plate_1 - plate
    ketchup_1 - ketchup
  )

  (:obj_of_interest
    akita_black_bowl_1
    white_cabinet_1
  )

  (:init
    (On akita_black_bowl_1 kitchen_table_akita_black_bowl_init_region)
    (On plate_1 kitchen_table_plate_init_region)
    (On white_cabinet_1 kitchen_table_white_cabinet_init_region)
    (On ketchup_1 kitchen_table_ketchup_init_region)
    (Open white_cabinet_1_top_region)
  )

  (:goal
    (And (In akita_black_bowl_1 white_cabinet_1_top_region))
  )

)
