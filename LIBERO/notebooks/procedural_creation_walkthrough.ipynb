{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Overview\n", "\n", "In this notebook, we will go through the task generation pipeline in LIBERO. We will cover the following contents:\n", "\n", "1. Retrieve a list of available objects, predicates\n", " in the codebase\n", "2. Define your own initial state distribution\n", "3. Define your own task goal\n", "4. Generate the pddl file for the task\n", "\n", "Now, let's get started!"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## 1. Retrieve a list of objects available\n", "\n", "In order for the sucess in task generation, we need to make sure that the objects and the predicates (object relations) specified are available in the codebase."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[1m\u001b[33m[robosuite WARNING] \u001b[0mNo private macro file found! (__init__.py:7)\n", "\u001b[1m\u001b[33m[robosuite WARNING] \u001b[0mIt is recommended to use a private macro file (__init__.py:8)\n", "\u001b[1m\u001b[33m[robosuite WARNING] \u001b[0mTo setup, run: python /Users/<USER>/workspace/robosuite-master/robosuite/scripts/setup_macros.py (__init__.py:9)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'alphabet_soup': <class 'libero.libero.envs.objects.hope_objects.AlphabetSoup'>, 'bbq_sauce': <class 'libero.libero.envs.objects.hope_objects.BbqSauce'>, 'butter': <class 'libero.libero.envs.objects.hope_objects.Butter'>, 'cherries': <class 'libero.libero.envs.objects.hope_objects.Cherries'>, 'chocolate_pudding': <class 'libero.libero.envs.objects.hope_objects.ChocolatePudding'>, 'cookies': <class 'libero.libero.envs.objects.hope_objects.Cookies'>, 'corn': <class 'libero.libero.envs.objects.hope_objects.Corn'>, 'cream_cheese': <class 'libero.libero.envs.objects.hope_objects.CreamCheese'>, 'ketchup': <class 'libero.libero.envs.objects.hope_objects.Ketchup'>, 'macaroni_and_cheese': <class 'libero.libero.envs.objects.hope_objects.MacaroniAndCheese'>, 'mayo': <class 'libero.libero.envs.objects.hope_objects.Mayo'>, 'milk': <class 'libero.libero.envs.objects.hope_objects.Milk'>, 'orange_juice': <class 'libero.libero.envs.objects.hope_objects.OrangeJuice'>, 'popcorn': <class 'libero.libero.envs.objects.hope_objects.Popcorn'>, 'salad_dressing': <class 'libero.libero.envs.objects.hope_objects.SaladDressing'>, 'new_salad_dressing': <class 'libero.libero.envs.objects.hope_objects.NewSaladDressing'>, 'tomato_sauce': <class 'libero.libero.envs.objects.hope_objects.TomatoSauce'>, 'rack': <class 'libero.libero.envs.objects.google_scanned_objects.Rack'>, 'white_bowl': <class 'libero.libero.envs.objects.google_scanned_objects.WhiteBowl'>, 'akita_black_bowl': <class 'libero.libero.envs.objects.google_scanned_objects.AkitaBlackBowl'>, 'plate': <class 'libero.libero.envs.objects.google_scanned_objects.Plate'>, 'basket': <class 'libero.libero.envs.objects.google_scanned_objects.Basket'>, 'chefmate_8_frypan': <class 'libero.libero.envs.objects.google_scanned_objects.Chefmate8Frypan'>, 'glazed_rim_porcelain_ramekin': <class 'libero.libero.envs.objects.google_scanned_objects.GlazedRimPorcelainRamekin'>, 'microwave': <class 'libero.libero.envs.objects.articulated_objects.Microwave'>, 'slide_cabinet': <class 'libero.libero.envs.objects.articulated_objects.SlideCabinet'>, 'window': <class 'libero.libero.envs.objects.articulated_objects.Window'>, 'faucet': <class 'libero.libero.envs.objects.articulated_objects.Faucet'>, 'basin_faucet': <class 'libero.libero.envs.objects.articulated_objects.BasinFaucet'>, 'short_cabinet': <class 'libero.libero.envs.objects.articulated_objects.ShortCabinet'>, 'short_fridge': <class 'libero.libero.envs.objects.articulated_objects.ShortFridge'>, 'wooden_cabinet': <class 'libero.libero.envs.objects.articulated_objects.WoodenCabinet'>, 'white_cabinet': <class 'libero.libero.envs.objects.articulated_objects.WhiteCabinet'>, 'flat_stove': <class 'libero.libero.envs.objects.articulated_objects.FlatStove'>, 'wooden_tray': <class 'libero.libero.envs.objects.turbosquid_objects.WoodenTray'>, 'white_storage_box': <class 'libero.libero.envs.objects.turbosquid_objects.WhiteStorageBox'>, 'wooden_shelf': <class 'libero.libero.envs.objects.turbosquid_objects.WoodenShelf'>, 'wooden_two_layer_shelf': <class 'libero.libero.envs.objects.turbosquid_objects.WoodenTwoLayerShelf'>, 'wine_rack': <class 'libero.libero.envs.objects.turbosquid_objects.WineRack'>, 'wine_bottle': <class 'libero.libero.envs.objects.turbosquid_objects.WineBottle'>, 'dining_set_group': <class 'libero.libero.envs.objects.turbosquid_objects.DiningSetGroup'>, 'bowl_drainer': <class 'libero.libero.envs.objects.turbosquid_objects.BowlDrainer'>, 'moka_pot': <class 'libero.libero.envs.objects.turbosquid_objects.MokaPot'>, 'black_book': <class 'libero.libero.envs.objects.turbosquid_objects.BlackBook'>, 'yellow_book': <class 'libero.libero.envs.objects.turbosquid_objects.YellowBook'>, 'red_coffee_mug': <class 'libero.libero.envs.objects.turbosquid_objects.RedCoffeeMug'>, 'desk_caddy': <class 'libero.libero.envs.objects.turbosquid_objects.DeskCaddy'>, 'porcelain_mug': <class 'libero.libero.envs.objects.turbosquid_objects.PorcelainMug'>, 'white_yellow_mug': <class 'libero.libero.envs.objects.turbosquid_objects.WhiteYellowMug'>, 'target_zone': <class 'libero.libero.envs.objects.target_zones.TargetZone'>}\n"]}], "source": ["from libero.libero.envs.objects import get_object_dict, get_object_fn\n", "\n", "# Get a dictionary of all the objects\n", "object_dict = get_object_dict()\n", "print(object_dict)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Now you can see all the available objects, you can retrieve the object class by specifying their categories (which are the keys in the dictionary)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["moka_pot : defined in the class  <class 'libero.libero.envs.objects.turbosquid_objects.MokaPot'>\n"]}], "source": ["category_name = \"moka_pot\"\n", "object_cls = get_object_fn(category_name)\n", "print(category_name, \": defined in the class \", object_cls)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Similarly, you can retrieve the information about predicates."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'true': <libero.libero.envs.predicates.base_predicates.TruePredicateFn object at 0x1377b7fd0>, 'false': <libero.libero.envs.predicates.base_predicates.FalsePredicateFn object at 0x1377b7ee0>, 'in': <libero.libero.envs.predicates.base_predicates.In object at 0x1377b7820>, 'on': <libero.libero.envs.predicates.base_predicates.On object at 0x1377b7580>, 'up': <libero.libero.envs.predicates.base_predicates.Up object at 0x1377b6b30>, 'printjointstate': <libero.libero.envs.predicates.base_predicates.PrintJointState object at 0x1377b6a40>, 'open': <libero.libero.envs.predicates.base_predicates.Open object at 0x1377b69b0>, 'close': <libero.libero.envs.predicates.base_predicates.Close object at 0x1377b5cc0>, 'turnon': <libero.libero.envs.predicates.base_predicates.TurnOn object at 0x1377b45e0>, 'turnoff': <libero.libero.envs.predicates.base_predicates.TurnOff object at 0x1377b4610>}\n", "=============\n", "<libero.libero.envs.predicates.base_predicates.On object at 0x1377b7580>\n"]}], "source": ["from libero.libero.envs.predicates import get_predicate_fn_dict, get_predicate_fn\n", "\n", "predicate_dict = get_predicate_fn_dict()\n", "print(predicate_dict)\n", "print(\"=============\")\n", "predicate_name = \"on\"\n", "print(get_predicate_fn(predicate_name))\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## 2. Define your own initial state distribution"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from libero.libero.utils.bddl_generation_utils import get_xy_region_kwargs_list_from_regions_info\n", "from libero.libero.utils.mu_utils import register_mu, InitialSceneTemplates\n", "from libero.libero.utils.task_generation_utils import register_task_info, get_task_info, generate_bddl_from_task_info\n", "\n", "@register_mu(scene_type=\"kitchen\")\n", "class KitchenScene1(InitialSceneTemplates):\n", "    def __init__(self):\n", "\n", "        fixture_num_info = {\n", "            \"kitchen_table\": 1,\n", "            \"wooden_cabinet\": 1,\n", "        }\n", "\n", "        object_num_info = {\n", "            \"akita_black_bowl\": 1,\n", "            \"plate\": 1,\n", "        }\n", "\n", "        super().__init__(\n", "            workspace_name=\"kitchen_table\",\n", "            fixture_num_info=fixture_num_info,\n", "            object_num_info=object_num_info\n", "        )\n", "\n", "    def define_regions(self):\n", "        self.regions.update(\n", "            self.get_region_dict(region_centroid_xy=[0.0, -0.30], \n", "                                 region_name=\"wooden_cabinet_init_region\", \n", "                                 target_name=self.workspace_name, \n", "                                 region_half_len=0.01,\n", "                                 yaw_rotation=(np.pi, np.pi))\n", "        )\n", "\n", "        self.regions.update(\n", "            self.get_region_dict(region_centroid_xy=[0., 0.0], \n", "                                 region_name=\"akita_black_bowl_init_region\", \n", "                                 target_name=self.workspace_name, \n", "                                 region_half_len=0.025)\n", "        )\n", "\n", "        self.regions.update(\n", "            self.get_region_dict(region_centroid_xy=[0.0, 0.25], \n", "                                 region_name=\"plate_init_region\", \n", "                                 target_name=self.workspace_name, \n", "                                 region_half_len=0.025)\n", "        )\n", "        self.xy_region_kwargs_list = get_xy_region_kwargs_list_from_regions_info(self.regions)\n", "\n", "    @property\n", "    def init_states(self):\n", "        states = [\n", "            (\"On\", \"akita_black_bowl_1\", \"kitchen_table_akita_black_bowl_init_region\"),\n", "            (\"On\", \"plate_1\", \"kitchen_table_plate_init_region\"),\n", "            (\"On\", \"wooden_cabinet_1\", \"kitchen_table_wooden_cabinet_init_region\")]\n", "        return states"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## 3. Define your own task goal\n", "\n", "Now that you've defined the initial state distributions, you can specify a task goal based on the available objects and the potential goals it can acehive."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["scene_name = \"kitchen_scene1\"\n", "language = \"Your Language 1\"\n", "register_task_info(language,\n", "                    scene_name=scene_name,\n", "                    objects_of_interest=[\"wooden_cabinet_1\", \"akita_black_bowl_1\"],\n", "                    goal_states=[(\"Open\", \"wooden_cabinet_1_top_region\"), (\"In\", \"akita_black_bowl_1\", \"wooden_cabinet_1_top_region\")]\n", ")\n", "\n", "# Create another task with the same scene layout\n", "scene_name = \"kitchen_scene1\"\n", "language = \"Your Language 2\"\n", "register_task_info(language,\n", "                    scene_name=scene_name,\n", "                    objects_of_interest=[\"wooden_cabinet_1\", \"akita_black_bowl_1\"],\n", "                    goal_states=[(\"Open\", \"wooden_cabinet_1_top_region\"), (\"In\", \"akita_black_bowl_1\", \"wooden_cabinet_1_bottom_region\")]\n", ")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["The task goals will be temporarily saved in the variable `libero.libero.utils.task_generation_utils.TASK_INFO` in the format of namedtuple `libero.libero.utils.task_generation_utils.TaskInfoTuple`. This design aims to make it easy for batch creation of tasks."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Succefully generated: 2\n", "['tmp/pddl_files/KITCHEN_SCENE1_your_language_1.bddl', 'tmp/pddl_files/KITCHEN_SCENE1_your_language_2.bddl']\n", "Encountered some failures:  []\n"]}], "source": ["# This is the default path to store all the pddl scene files. Here we store the files in the temporary folder. If you want to directly add files into the libero codebase, get the default path use the following commented lines:\n", "# from libero.libero import get_libero_path\n", "# YOUR_BDDL_FILE_PATH = get_libero_path(\"bddl_files\")\n", "\n", "YOUR_BDDL_FILE_PATH = \"tmp/pddl_files\"\n", "bddl_file_names, failures = generate_bddl_from_task_info(folder=YOUR_BDDL_FILE_PATH)\n", "\n", "print(bddl_file_names)\n", "\n", "print(\"Encountered some failures: \", failures)\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Now you can see the content of the pddl file name. (Notice that we named our variable with bddl, since we are actually using the bddl package from Behavior. However, bddl is a subset of pddl, so we stick to the word PDDL for consistency in our paper writing and avoid confusion to the community.)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(define (problem LIBERO_Kitchen_Tabletop_Manipulation)\n", "  (:domain robosuite)\n", "  (:language Your Language 1)\n", "    (:regions\n", "      (wooden_cabinet_init_region\n", "          (:target kitchen_table)\n", "          (:ranges (\n", "              (-0.01 -0.31 0.01 -0.29)\n", "            )\n", "          )\n", "          (:yaw_rotation (\n", "              (3.141592653589793 3.141592653589793)\n", "            )\n", "          )\n", "      )\n", "      (akita_black_bowl_init_region\n", "          (:target kitchen_table)\n", "          (:ranges (\n", "              (-0.025 -0.025 0.025 0.025)\n", "            )\n", "          )\n", "          (:yaw_rotation (\n", "              (0.0 0.0)\n", "            )\n", "          )\n", "      )\n", "      (plate_init_region\n", "          (:target kitchen_table)\n", "          (:ranges (\n", "              (-0.025 0.225 0.025 0.275)\n", "            )\n", "          )\n", "          (:yaw_rotation (\n", "              (0.0 0.0)\n", "            )\n", "          )\n", "      )\n", "      (top_side\n", "          (:target wooden_cabinet_1)\n", "      )\n", "      (top_region\n", "          (:target wooden_cabinet_1)\n", "      )\n", "      (middle_region\n", "          (:target wooden_cabinet_1)\n", "      )\n", "      (bottom_region\n", "          (:target wooden_cabinet_1)\n", "      )\n", "    )\n", "\n", "  (:fixtures\n", "    kitchen_table - kitchen_table\n", "    wooden_cabinet_1 - wooden_cabinet\n", "  )\n", "\n", "  (:objects\n", "    akita_black_bowl_1 - akita_black_bowl\n", "    plate_1 - plate\n", "  )\n", "\n", "  (:obj_of_interest\n", "    wooden_cabinet_1\n", "    akita_black_bowl_1\n", "  )\n", "\n", "  (:init\n", "    (On akita_black_bowl_1 kitchen_table_akita_black_bowl_init_region)\n", "    (On plate_1 kitchen_table_plate_init_region)\n", "    (On wooden_cabinet_1 kitchen_table_wooden_cabinet_init_region)\n", "  )\n", "\n", "  (:goal\n", "    (And (Open wooden_cabinet_1_top_region) (In akita_black_bowl_1 wooden_cabinet_1_top_region))\n", "  )\n", "\n", ")\n", "\n"]}], "source": ["with open(bddl_file_names[0], \"r\") as f:\n", "    content = f.read()\n", "print(content)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.7"}}, "nbformat": 4, "nbformat_minor": 2}