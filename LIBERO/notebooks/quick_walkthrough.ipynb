{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Overview\n", "This jupyter notebook covers the following contents:\n", "1. Default configuration in LIBERO\n", "2. Basic information about available LIBERO benchmarks\n", "   - Get a dictionary of mapping from benchmark name to benchmark class\n", "   - Check the integrity of benchmarks\n", "   - Check the integrity of init files\n", "   - Visualize all the init states of a task\n", "   - Download datasets\n", "   - Get information about a demonstration file and replay a trajectory\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/anaconda3/envs/libero/lib/python3.8/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["from libero.libero import benchmark, get_libero_path, set_libero_default_path\n", "import os\n", "from termcolor import colored"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## 1. Default file paths"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["All the paths are retrieved from a yaml config file located at `~/.libero/config.yaml`. And the default paths are set to relative to the libero codebase."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Default benchmark root path:  /home/<USER>/repo/LIBERO/libero/libero\n", "Default dataset root path:  /home/<USER>/repo/LIBERO/libero/libero/../datasets\n", "Default bddl files root path:  /home/<USER>/repo/LIBERO/libero/libero/./bddl_files\n"]}], "source": ["benchmark_root_path = get_libero_path(\"benchmark_root\")\n", "init_states_default_path = get_libero_path(\"init_states\")\n", "datasets_default_path = get_libero_path(\"datasets\")\n", "bddl_files_default_path = get_libero_path(\"bddl_files\")\n", "print(\"Default benchmark root path: \", benchmark_root_path)\n", "print(\"Default dataset root path: \", datasets_default_path)\n", "print(\"Default bddl files root path: \", bddl_files_default_path)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Now if you want to point your codebase to custom path, you can use `set_libero_path` function to do that. Notice that all the paths change according to `benchmark_root` value."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[Warning] You are changing the default path for Libero config. This will affect all the paths in the config file.\n", "[Warning]: assets path /home/<USER>/custom_project/./assets does not exist!\n", "[Warning]: bddl_files path /home/<USER>/custom_project/./bddl_files does not exist!\n", "[Warning]: benchmark_root path /home/<USER>/custom_project does not exist!\n", "[Warning]: datasets path /home/<USER>/custom_project/../datasets does not exist!\n", "[Warning]: init_states path /home/<USER>/custom_project/./init_files does not exist!\n", "[Warning]: assets path /home/<USER>/custom_project/./assets does not exist!\n", "[Warning]: bddl_files path /home/<USER>/custom_project/./bddl_files does not exist!\n", "[Warning]: benchmark_root path /home/<USER>/custom_project does not exist!\n", "[Warning]: datasets path /home/<USER>/custom_project/../datasets does not exist!\n", "[Warning]: init_states path /home/<USER>/custom_project/./init_files does not exist!\n", "[Warning]: assets path /home/<USER>/custom_project/./assets does not exist!\n", "[Warning]: bddl_files path /home/<USER>/custom_project/./bddl_files does not exist!\n", "[Warning]: benchmark_root path /home/<USER>/custom_project does not exist!\n", "[Warning]: datasets path /home/<USER>/custom_project/../datasets does not exist!\n", "[Warning]: init_states path /home/<USER>/custom_project/./init_files does not exist!\n", "[Warning]: assets path /home/<USER>/custom_project/./assets does not exist!\n", "[Warning]: bddl_files path /home/<USER>/custom_project/./bddl_files does not exist!\n", "[Warning]: benchmark_root path /home/<USER>/custom_project does not exist!\n", "[Warning]: datasets path /home/<USER>/custom_project/../datasets does not exist!\n", "[Warning]: init_states path /home/<USER>/custom_project/./init_files does not exist!\n", "Default benchmark root path:  /home/<USER>/custom_project\n", "Default dataset root path:  /home/<USER>/custom_project/../datasets\n", "Default bddl files root path:  /home/<USER>/custom_project/./bddl_files\n", "[Warning] You are changing the default path for Libero config. This will affect all the paths in the config file.\n", "Default benchmark root path:  /home/<USER>/repo/LIBERO/libero/libero\n", "Default dataset root path:  /home/<USER>/repo/LIBERO/libero/libero/../datasets\n", "Default bddl files root path:  /home/<USER>/repo/LIBERO/libero/libero/./bddl_files\n"]}], "source": ["set_libero_default_path(os.path.join(os.path.expanduser(\"~\"), \"custom_project\"))\n", "benchmark_root_path = get_libero_path(\"benchmark_root\")\n", "init_states_default_path = get_libero_path(\"init_states\")\n", "datasets_default_path = get_libero_path(\"datasets\")\n", "bddl_files_default_path = get_libero_path(\"bddl_files\")\n", "print(\"Default benchmark root path: \", benchmark_root_path)\n", "print(\"Default dataset root path: \", datasets_default_path)\n", "print(\"Default bddl files root path: \", bddl_files_default_path)\n", "\n", "# If nothing is specified in the `set_libero_default_path` function, the path will be changed back to the default path\n", "# We will set back the path to the default path for the subsequent examples\n", "set_libero_default_path()\n", "benchmark_root_path = get_libero_path(\"benchmark_root\")\n", "init_states_default_path = get_libero_path(\"init_states\")\n", "datasets_default_path = get_libero_path(\"datasets\")\n", "bddl_files_default_path = get_libero_path(\"bddl_files\")\n", "print(\"Default benchmark root path: \", benchmark_root_path)\n", "print(\"Default dataset root path: \", datasets_default_path)\n", "print(\"Default bddl files root path: \", bddl_files_default_path)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# 2. See available benchmarks"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### 2.1 Get a dictionary of mapping from benchmark name to benchmark class"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'libero_spatial': <class 'libero.libero.benchmark.LIBERO_SPATIAL'>, 'libero_object': <class 'libero.libero.benchmark.LIBERO_OBJECT'>, 'libero_goal': <class 'libero.libero.benchmark.LIBERO_GOAL'>, 'libero_90': <class 'libero.libero.benchmark.LIBERO_90'>, 'libero_10': <class 'libero.libero.benchmark.LIBERO_10'>, 'libero_100': <class 'libero.libero.benchmark.LIBERO_100'>}\n"]}], "source": ["benchmark_dict = benchmark.get_benchmark_dict()\n", "print(benchmark_dict)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### 2.2 Check the integrity of benchmarks"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[info] using task orders [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]\n", "10 tasks in the benchmark libero_10: \n", "The benchmark contains the following tasks:\n", "\t LIVING_ROOM_SCENE2_put_both_the_alphabet_soup_and_the_tomato_sauce_in_the_basket, detail definition stored in /home/<USER>/repo/LIBERO/libero/libero/./bddl_files/libero_10/LIVING_ROOM_SCENE2_put_both_the_alphabet_soup_and_the_tomato_sauce_in_the_basket.bddl\n", "\t LIVING_ROOM_SCENE2_put_both_the_cream_cheese_box_and_the_butter_in_the_basket, detail definition stored in /home/<USER>/repo/LIBERO/libero/libero/./bddl_files/libero_10/LIVING_ROOM_SCENE2_put_both_the_cream_cheese_box_and_the_butter_in_the_basket.bddl\n", "\t KITCHEN_SCENE3_turn_on_the_stove_and_put_the_moka_pot_on_it, detail definition stored in /home/<USER>/repo/LIBERO/libero/libero/./bddl_files/libero_10/KITCHEN_SCENE3_turn_on_the_stove_and_put_the_moka_pot_on_it.bddl\n", "\t KITCHEN_SCENE4_put_the_black_bowl_in_the_bottom_drawer_of_the_cabinet_and_close_it, detail definition stored in /home/<USER>/repo/LIBERO/libero/libero/./bddl_files/libero_10/KITCHEN_SCENE4_put_the_black_bowl_in_the_bottom_drawer_of_the_cabinet_and_close_it.bddl\n", "\t LIVING_ROOM_SCENE5_put_the_white_mug_on_the_left_plate_and_put_the_yellow_and_white_mug_on_the_right_plate, detail definition stored in /home/<USER>/repo/LIBERO/libero/libero/./bddl_files/libero_10/LIVING_ROOM_SCENE5_put_the_white_mug_on_the_left_plate_and_put_the_yellow_and_white_mug_on_the_right_plate.bddl\n", "\t STUDY_SCENE1_pick_up_the_book_and_place_it_in_the_back_compartment_of_the_caddy, detail definition stored in /home/<USER>/repo/LIBERO/libero/libero/./bddl_files/libero_10/STUDY_SCENE1_pick_up_the_book_and_place_it_in_the_back_compartment_of_the_caddy.bddl\n", "\t LIVING_ROOM_SCENE6_put_the_white_mug_on_the_plate_and_put_the_chocolate_pudding_to_the_right_of_the_plate, detail definition stored in /home/<USER>/repo/LIBERO/libero/libero/./bddl_files/libero_10/LIVING_ROOM_SCENE6_put_the_white_mug_on_the_plate_and_put_the_chocolate_pudding_to_the_right_of_the_plate.bddl\n", "\t LIVING_ROOM_SCENE1_put_both_the_alphabet_soup_and_the_cream_cheese_box_in_the_basket, detail definition stored in /home/<USER>/repo/LIBERO/libero/libero/./bddl_files/libero_10/LIVING_ROOM_SCENE1_put_both_the_alphabet_soup_and_the_cream_cheese_box_in_the_basket.bddl\n", "\t KITCHEN_SCENE8_put_both_moka_pots_on_the_stove, detail definition stored in /home/<USER>/repo/LIBERO/libero/libero/./bddl_files/libero_10/KITCHEN_SCENE8_put_both_moka_pots_on_the_stove.bddl\n", "\t KITCHEN_SCENE6_put_the_yellow_and_white_mug_in_the_microwave_and_close_it, detail definition stored in /home/<USER>/repo/LIBERO/libero/libero/./bddl_files/libero_10/KITCHEN_SCENE6_put_the_yellow_and_white_mug_in_the_microwave_and_close_it.bddl\n"]}], "source": ["# initialize a benchmark\n", "benchmark_instance = benchmark_dict[\"libero_10\"]()\n", "num_tasks = benchmark_instance.get_num_tasks()\n", "# see how many tasks involved in the benchmark\n", "print(f\"{num_tasks} tasks in the benchmark {benchmark_instance.name}: \")\n", "\n", "# Check if all the task names and their bddl file names\n", "task_names = benchmark_instance.get_task_names()\n", "print(\"The benchmark contains the following tasks:\")\n", "for i in range(num_tasks):\n", "    task_name = task_names[i]\n", "    task = benchmark_instance.get_task(i)\n", "    bddl_file = os.path.join(bddl_files_default_path, task.problem_folder, task.bddl_file)\n", "    print(f\"\\t {task_name}, detail definition stored in {bddl_file}\")\n", "    if not os.path.exists(bddl_file):\n", "        print(colored(f\"[error] bddl file {bddl_file} cannot be found. Check your paths\", \"red\"))"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### 2.3 Check the integrity of init files"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The benchmark contains the following tasks:\n", "An example of init file is named like this: KITCHEN_SCENE6_put_the_yellow_and_white_mug_in_the_microwave_and_close_it.pruned_init\n", "(50, 123)\n"]}], "source": ["# Check if all the init states files exist for tasks\n", "task_names = benchmark_instance.get_task_names()\n", "print(\"The benchmark contains the following tasks:\")\n", "for i in range(num_tasks):\n", "    task_name = task_names[i]\n", "    task = benchmark_instance.get_task(i)\n", "    init_states_path = os.path.join(init_states_default_path, task.problem_folder, task.init_states_file)\n", "    if not os.path.exists(init_states_path):\n", "        print(colored(f\"[error] the init states {init_states_path} cannot be found. Check your paths\", \"red\"))\n", "print(f\"An example of init file is named like this: {task.init_states_file}\")\n", "\n", "# Load torch init files\n", "init_states = benchmark_instance.get_task_init_states(0)\n", "# Init states in the same (num_init_rollouts, num_simulation_states)\n", "print(init_states.shape)\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### 2.4 Visualize all the init states of a task"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[robosuite WARNING] No private macro file found! (__init__.py:7)\n", "[robosuite WARNING] It is recommended to use a private macro file (__init__.py:8)\n", "[robosuite WARNING] To setup, run: python /home/<USER>/anaconda3/envs/libero/lib/python3.8/site-packages/robosuite/scripts/setup_macros.py (__init__.py:9)\n"]}, {"data": {"image/png": "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", "text/plain": ["<PIL.Image.Image image mode=RGB size=1302x652>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from libero.libero.envs import OffScreenRenderEnv\n", "from IPython.display import display\n", "from PIL import Image\n", "\n", "import torch\n", "import torchvision\n", "\n", "# task_id is the (task_id + 1)th task in the benchmark\n", "task_id = 9\n", "task = benchmark_instance.get_task(task_id)\n", "\n", "env_args = {\n", "    \"bddl_file_name\": os.path.join(bddl_files_default_path, task.problem_folder, task.bddl_file),\n", "    \"camera_heights\": 128,\n", "    \"camera_widths\": 128\n", "}\n", "\n", "env = OffScreenRenderEnv(**env_args)\n", "\n", "\n", "init_states = benchmark_instance.get_task_init_states(task_id)\n", "\n", "# Fix random seeds for reproducibility\n", "env.seed(0)\n", "\n", "def make_grid(images, nrow=8, padding=2, normalize=False, pad_value=0):\n", "    \"\"\"Make a grid of images. Make sure images is a 4D tensor in the shape of (B x C x H x W)) or a list of torch tensors.\"\"\"\n", "    grid_image = torchvision.utils.make_grid(images, nrow=nrow, padding=padding, normalize=normalize, pad_value=pad_value).permute(1, 2, 0)\n", "    return grid_image\n", "\n", "images = []\n", "env.reset()\n", "for eval_index in range(len(init_states)):\n", "    env.set_init_state(init_states[eval_index])\n", "\n", "    for _ in range(5):\n", "        obs, _, _, _ = env.step([0.] * 7)\n", "    images.append(torch.from_numpy(obs[\"agentview_image\"]).permute(2, 0, 1))\n", "\n", "# # images = torch.stack(images, dim=0).permute(0, 3, 1, 2)\n", "# print(images.shape)\n", "grid_image = make_grid(images, nrow=10, padding=2, pad_value=0)\n", "display(Image.fromarray(grid_image.numpy()[::-1]))\n", "env.close()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### 2.5 Download datasets"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ ] Dataset libero_object not found!!!\n", "[ ] Dataset libero_goal not found!!!\n", "[X] Dataset libero_spatial is complete\n", "[X] Dataset libero_10 is complete\n", "[X] Dataset libero_90 is complete\n", "Downloading libero_spatial\n"]}, {"name": "stderr", "output_type": "stream", "text": ["04k94hyizn4huhbv5sz4ev9p2h1p6s7f.zip: 2.88GB [02:56, 16.3MB/s]                            \n"]}], "source": ["import libero.libero.utils.download_utils as download_utils\n", "\n", "download_dir = get_libero_path(\"datasets\")\n", "datasets = \"libero_spatial\" # Can specify \"all\", \"libero_goal\", \"libero_spatial\", \"libero_object\", \"libero_100\"\n", "\n", "libero_datasets_exist = download_utils.check_libero_dataset(download_dir=download_dir)\n", "\n", "if not libero_datasets_exist:\n", "    download_utils.libero_dataset_download(download_dir=download_dir, datasets=datasets)\n", "\n", "# Check if the demo files exist\n", "demo_files = [os.path.join(datasets_default_path, benchmark_instance.get_task_demonstration(i)) for i in range(num_tasks)]\n", "for demo_file in demo_files:\n", "    if not os.path.exists(demo_file):\n", "        print(colored(f\"[error] demo file {demo_file} cannot be found. Check your paths\", \"red\"))"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### 2.6 Get information about a demonstration file and replay a trajectory"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "total transitions: 15232\n", "total trajectories: 50\n", "traj length mean: 304.64\n", "traj length std: 46.59989699559432\n", "traj length min: 224\n", "traj length max: 449\n", "action min: -1.0\n", "action max: 1.0\n", "language instruction: put the yellow and white mug in the microwave and close it\n", "\n", "==== Filter Keys ====\n", "no filter keys\n", "\n", "\n", "==== Env Meta ====\n", "{\n", "    \"type\": 1,\n", "    \"env_name\": \"Libero_Kitchen_Tabletop_Manipulation\",\n", "    \"problem_name\": \"libero_kitchen_tabletop_manipulation\",\n", "    \"bddl_file\": \"chiliocosm/bddl_files/libero_100/KITCHEN_SCENE6_put_the_yellow_and_white_mug_in_the_microwave_and_close_it.bddl\",\n", "    \"env_kwargs\": {\n", "        \"robots\": [\n", "            \"Panda\"\n", "        ],\n", "        \"controller_configs\": {\n", "            \"type\": \"OSC_POSE\",\n", "            \"input_max\": 1,\n", "            \"input_min\": -1,\n", "            \"output_max\": [\n", "                0.05,\n", "                0.05,\n", "                0.05,\n", "                0.5,\n", "                0.5,\n", "                0.5\n", "            ],\n", "            \"output_min\": [\n", "                -0.05,\n", "                -0.05,\n", "                -0.05,\n", "                -0.5,\n", "                -0.5,\n", "                -0.5\n", "            ],\n", "            \"kp\": 150,\n", "            \"damping_ratio\": 1,\n", "            \"impedance_mode\": \"fixed\",\n", "            \"kp_limits\": [\n", "                0,\n", "                300\n", "            ],\n", "            \"damping_ratio_limits\": [\n", "                0,\n", "                10\n", "            ],\n", "            \"position_limits\": null,\n", "            \"orientation_limits\": null,\n", "            \"uncouple_pos_ori\": true,\n", "            \"control_delta\": true,\n", "            \"interpolation\": null,\n", "            \"ramp_ratio\": 0.2\n", "        },\n", "        \"bddl_file_name\": \"chiliocosm/bddl_files/libero_100/KITCHEN_SCENE6_put_the_yellow_and_white_mug_in_the_microwave_and_close_it.bddl\",\n", "        \"has_renderer\": false,\n", "        \"has_offscreen_renderer\": true,\n", "        \"ignore_done\": true,\n", "        \"use_camera_obs\": true,\n", "        \"camera_depths\": false,\n", "        \"camera_names\": [\n", "            \"robot0_eye_in_hand\",\n", "            \"agentview\"\n", "        ],\n", "        \"reward_shaping\": true,\n", "        \"control_freq\": 20,\n", "        \"camera_heights\": 128,\n", "        \"camera_widths\": 128,\n", "        \"camera_segmentations\": null\n", "    }\n", "}\n", "\n", "==== Dataset Structure ====\n", "episode demo_0 with 329 transitions\n", "    key: actions with shape (329, 7)\n", "    key: dones with shape (329,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (329, 128, 128, 3)\n", "        observation key ee_ori with shape (329, 3)\n", "        observation key ee_pos with shape (329, 3)\n", "        observation key ee_states with shape (329, 6)\n", "        observation key eye_in_hand_rgb with shape (329, 128, 128, 3)\n", "        observation key gripper_states with shape (329, 2)\n", "        observation key joint_states with shape (329, 7)\n", "    key: rewards with shape (329,)\n", "    key: robot_states with shape (329, 9)\n", "    key: states with shape (329, 47)\n", "episode demo_1 with 248 transitions\n", "    key: actions with shape (248, 7)\n", "    key: dones with shape (248,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (248, 128, 128, 3)\n", "        observation key ee_ori with shape (248, 3)\n", "        observation key ee_pos with shape (248, 3)\n", "        observation key ee_states with shape (248, 6)\n", "        observation key eye_in_hand_rgb with shape (248, 128, 128, 3)\n", "        observation key gripper_states with shape (248, 2)\n", "        observation key joint_states with shape (248, 7)\n", "    key: rewards with shape (248,)\n", "    key: robot_states with shape (248, 9)\n", "    key: states with shape (248, 47)\n", "episode demo_2 with 271 transitions\n", "    key: actions with shape (271, 7)\n", "    key: dones with shape (271,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (271, 128, 128, 3)\n", "        observation key ee_ori with shape (271, 3)\n", "        observation key ee_pos with shape (271, 3)\n", "        observation key ee_states with shape (271, 6)\n", "        observation key eye_in_hand_rgb with shape (271, 128, 128, 3)\n", "        observation key gripper_states with shape (271, 2)\n", "        observation key joint_states with shape (271, 7)\n", "    key: rewards with shape (271,)\n", "    key: robot_states with shape (271, 9)\n", "    key: states with shape (271, 47)\n", "episode demo_3 with 415 transitions\n", "    key: actions with shape (415, 7)\n", "    key: dones with shape (415,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (415, 128, 128, 3)\n", "        observation key ee_ori with shape (415, 3)\n", "        observation key ee_pos with shape (415, 3)\n", "        observation key ee_states with shape (415, 6)\n", "        observation key eye_in_hand_rgb with shape (415, 128, 128, 3)\n", "        observation key gripper_states with shape (415, 2)\n", "        observation key joint_states with shape (415, 7)\n", "    key: rewards with shape (415,)\n", "    key: robot_states with shape (415, 9)\n", "    key: states with shape (415, 47)\n", "episode demo_4 with 364 transitions\n", "    key: actions with shape (364, 7)\n", "    key: dones with shape (364,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (364, 128, 128, 3)\n", "        observation key ee_ori with shape (364, 3)\n", "        observation key ee_pos with shape (364, 3)\n", "        observation key ee_states with shape (364, 6)\n", "        observation key eye_in_hand_rgb with shape (364, 128, 128, 3)\n", "        observation key gripper_states with shape (364, 2)\n", "        observation key joint_states with shape (364, 7)\n", "    key: rewards with shape (364,)\n", "    key: robot_states with shape (364, 9)\n", "    key: states with shape (364, 47)\n", "episode demo_5 with 293 transitions\n", "    key: actions with shape (293, 7)\n", "    key: dones with shape (293,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (293, 128, 128, 3)\n", "        observation key ee_ori with shape (293, 3)\n", "        observation key ee_pos with shape (293, 3)\n", "        observation key ee_states with shape (293, 6)\n", "        observation key eye_in_hand_rgb with shape (293, 128, 128, 3)\n", "        observation key gripper_states with shape (293, 2)\n", "        observation key joint_states with shape (293, 7)\n", "    key: rewards with shape (293,)\n", "    key: robot_states with shape (293, 9)\n", "    key: states with shape (293, 47)\n", "episode demo_6 with 317 transitions\n", "    key: actions with shape (317, 7)\n", "    key: dones with shape (317,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (317, 128, 128, 3)\n", "        observation key ee_ori with shape (317, 3)\n", "        observation key ee_pos with shape (317, 3)\n", "        observation key ee_states with shape (317, 6)\n", "        observation key eye_in_hand_rgb with shape (317, 128, 128, 3)\n", "        observation key gripper_states with shape (317, 2)\n", "        observation key joint_states with shape (317, 7)\n", "    key: rewards with shape (317,)\n", "    key: robot_states with shape (317, 9)\n", "    key: states with shape (317, 47)\n", "episode demo_7 with 307 transitions\n", "    key: actions with shape (307, 7)\n", "    key: dones with shape (307,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (307, 128, 128, 3)\n", "        observation key ee_ori with shape (307, 3)\n", "        observation key ee_pos with shape (307, 3)\n", "        observation key ee_states with shape (307, 6)\n", "        observation key eye_in_hand_rgb with shape (307, 128, 128, 3)\n", "        observation key gripper_states with shape (307, 2)\n", "        observation key joint_states with shape (307, 7)\n", "    key: rewards with shape (307,)\n", "    key: robot_states with shape (307, 9)\n", "    key: states with shape (307, 47)\n", "episode demo_8 with 317 transitions\n", "    key: actions with shape (317, 7)\n", "    key: dones with shape (317,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (317, 128, 128, 3)\n", "        observation key ee_ori with shape (317, 3)\n", "        observation key ee_pos with shape (317, 3)\n", "        observation key ee_states with shape (317, 6)\n", "        observation key eye_in_hand_rgb with shape (317, 128, 128, 3)\n", "        observation key gripper_states with shape (317, 2)\n", "        observation key joint_states with shape (317, 7)\n", "    key: rewards with shape (317,)\n", "    key: robot_states with shape (317, 9)\n", "    key: states with shape (317, 47)\n", "episode demo_9 with 301 transitions\n", "    key: actions with shape (301, 7)\n", "    key: dones with shape (301,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (301, 128, 128, 3)\n", "        observation key ee_ori with shape (301, 3)\n", "        observation key ee_pos with shape (301, 3)\n", "        observation key ee_states with shape (301, 6)\n", "        observation key eye_in_hand_rgb with shape (301, 128, 128, 3)\n", "        observation key gripper_states with shape (301, 2)\n", "        observation key joint_states with shape (301, 7)\n", "    key: rewards with shape (301,)\n", "    key: robot_states with shape (301, 9)\n", "    key: states with shape (301, 47)\n", "episode demo_10 with 285 transitions\n", "    key: actions with shape (285, 7)\n", "    key: dones with shape (285,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (285, 128, 128, 3)\n", "        observation key ee_ori with shape (285, 3)\n", "        observation key ee_pos with shape (285, 3)\n", "        observation key ee_states with shape (285, 6)\n", "        observation key eye_in_hand_rgb with shape (285, 128, 128, 3)\n", "        observation key gripper_states with shape (285, 2)\n", "        observation key joint_states with shape (285, 7)\n", "    key: rewards with shape (285,)\n", "    key: robot_states with shape (285, 9)\n", "    key: states with shape (285, 47)\n", "episode demo_11 with 253 transitions\n", "    key: actions with shape (253, 7)\n", "    key: dones with shape (253,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (253, 128, 128, 3)\n", "        observation key ee_ori with shape (253, 3)\n", "        observation key ee_pos with shape (253, 3)\n", "        observation key ee_states with shape (253, 6)\n", "        observation key eye_in_hand_rgb with shape (253, 128, 128, 3)\n", "        observation key gripper_states with shape (253, 2)\n", "        observation key joint_states with shape (253, 7)\n", "    key: rewards with shape (253,)\n", "    key: robot_states with shape (253, 9)\n", "    key: states with shape (253, 47)\n", "episode demo_12 with 334 transitions\n", "    key: actions with shape (334, 7)\n", "    key: dones with shape (334,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (334, 128, 128, 3)\n", "        observation key ee_ori with shape (334, 3)\n", "        observation key ee_pos with shape (334, 3)\n", "        observation key ee_states with shape (334, 6)\n", "        observation key eye_in_hand_rgb with shape (334, 128, 128, 3)\n", "        observation key gripper_states with shape (334, 2)\n", "        observation key joint_states with shape (334, 7)\n", "    key: rewards with shape (334,)\n", "    key: robot_states with shape (334, 9)\n", "    key: states with shape (334, 47)\n", "episode demo_13 with 310 transitions\n", "    key: actions with shape (310, 7)\n", "    key: dones with shape (310,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (310, 128, 128, 3)\n", "        observation key ee_ori with shape (310, 3)\n", "        observation key ee_pos with shape (310, 3)\n", "        observation key ee_states with shape (310, 6)\n", "        observation key eye_in_hand_rgb with shape (310, 128, 128, 3)\n", "        observation key gripper_states with shape (310, 2)\n", "        observation key joint_states with shape (310, 7)\n", "    key: rewards with shape (310,)\n", "    key: robot_states with shape (310, 9)\n", "    key: states with shape (310, 47)\n", "episode demo_14 with 329 transitions\n", "    key: actions with shape (329, 7)\n", "    key: dones with shape (329,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (329, 128, 128, 3)\n", "        observation key ee_ori with shape (329, 3)\n", "        observation key ee_pos with shape (329, 3)\n", "        observation key ee_states with shape (329, 6)\n", "        observation key eye_in_hand_rgb with shape (329, 128, 128, 3)\n", "        observation key gripper_states with shape (329, 2)\n", "        observation key joint_states with shape (329, 7)\n", "    key: rewards with shape (329,)\n", "    key: robot_states with shape (329, 9)\n", "    key: states with shape (329, 47)\n", "episode demo_15 with 316 transitions\n", "    key: actions with shape (316, 7)\n", "    key: dones with shape (316,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (316, 128, 128, 3)\n", "        observation key ee_ori with shape (316, 3)\n", "        observation key ee_pos with shape (316, 3)\n", "        observation key ee_states with shape (316, 6)\n", "        observation key eye_in_hand_rgb with shape (316, 128, 128, 3)\n", "        observation key gripper_states with shape (316, 2)\n", "        observation key joint_states with shape (316, 7)\n", "    key: rewards with shape (316,)\n", "    key: robot_states with shape (316, 9)\n", "    key: states with shape (316, 47)\n", "episode demo_16 with 268 transitions\n", "    key: actions with shape (268, 7)\n", "    key: dones with shape (268,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (268, 128, 128, 3)\n", "        observation key ee_ori with shape (268, 3)\n", "        observation key ee_pos with shape (268, 3)\n", "        observation key ee_states with shape (268, 6)\n", "        observation key eye_in_hand_rgb with shape (268, 128, 128, 3)\n", "        observation key gripper_states with shape (268, 2)\n", "        observation key joint_states with shape (268, 7)\n", "    key: rewards with shape (268,)\n", "    key: robot_states with shape (268, 9)\n", "    key: states with shape (268, 47)\n", "episode demo_17 with 279 transitions\n", "    key: actions with shape (279, 7)\n", "    key: dones with shape (279,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (279, 128, 128, 3)\n", "        observation key ee_ori with shape (279, 3)\n", "        observation key ee_pos with shape (279, 3)\n", "        observation key ee_states with shape (279, 6)\n", "        observation key eye_in_hand_rgb with shape (279, 128, 128, 3)\n", "        observation key gripper_states with shape (279, 2)\n", "        observation key joint_states with shape (279, 7)\n", "    key: rewards with shape (279,)\n", "    key: robot_states with shape (279, 9)\n", "    key: states with shape (279, 47)\n", "episode demo_18 with 260 transitions\n", "    key: actions with shape (260, 7)\n", "    key: dones with shape (260,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (260, 128, 128, 3)\n", "        observation key ee_ori with shape (260, 3)\n", "        observation key ee_pos with shape (260, 3)\n", "        observation key ee_states with shape (260, 6)\n", "        observation key eye_in_hand_rgb with shape (260, 128, 128, 3)\n", "        observation key gripper_states with shape (260, 2)\n", "        observation key joint_states with shape (260, 7)\n", "    key: rewards with shape (260,)\n", "    key: robot_states with shape (260, 9)\n", "    key: states with shape (260, 47)\n", "episode demo_19 with 317 transitions\n", "    key: actions with shape (317, 7)\n", "    key: dones with shape (317,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (317, 128, 128, 3)\n", "        observation key ee_ori with shape (317, 3)\n", "        observation key ee_pos with shape (317, 3)\n", "        observation key ee_states with shape (317, 6)\n", "        observation key eye_in_hand_rgb with shape (317, 128, 128, 3)\n", "        observation key gripper_states with shape (317, 2)\n", "        observation key joint_states with shape (317, 7)\n", "    key: rewards with shape (317,)\n", "    key: robot_states with shape (317, 9)\n", "    key: states with shape (317, 47)\n", "episode demo_20 with 286 transitions\n", "    key: actions with shape (286, 7)\n", "    key: dones with shape (286,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (286, 128, 128, 3)\n", "        observation key ee_ori with shape (286, 3)\n", "        observation key ee_pos with shape (286, 3)\n", "        observation key ee_states with shape (286, 6)\n", "        observation key eye_in_hand_rgb with shape (286, 128, 128, 3)\n", "        observation key gripper_states with shape (286, 2)\n", "        observation key joint_states with shape (286, 7)\n", "    key: rewards with shape (286,)\n", "    key: robot_states with shape (286, 9)\n", "    key: states with shape (286, 47)\n", "episode demo_21 with 302 transitions\n", "    key: actions with shape (302, 7)\n", "    key: dones with shape (302,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (302, 128, 128, 3)\n", "        observation key ee_ori with shape (302, 3)\n", "        observation key ee_pos with shape (302, 3)\n", "        observation key ee_states with shape (302, 6)\n", "        observation key eye_in_hand_rgb with shape (302, 128, 128, 3)\n", "        observation key gripper_states with shape (302, 2)\n", "        observation key joint_states with shape (302, 7)\n", "    key: rewards with shape (302,)\n", "    key: robot_states with shape (302, 9)\n", "    key: states with shape (302, 47)\n", "episode demo_22 with 306 transitions\n", "    key: actions with shape (306, 7)\n", "    key: dones with shape (306,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (306, 128, 128, 3)\n", "        observation key ee_ori with shape (306, 3)\n", "        observation key ee_pos with shape (306, 3)\n", "        observation key ee_states with shape (306, 6)\n", "        observation key eye_in_hand_rgb with shape (306, 128, 128, 3)\n", "        observation key gripper_states with shape (306, 2)\n", "        observation key joint_states with shape (306, 7)\n", "    key: rewards with shape (306,)\n", "    key: robot_states with shape (306, 9)\n", "    key: states with shape (306, 47)\n", "episode demo_23 with 343 transitions\n", "    key: actions with shape (343, 7)\n", "    key: dones with shape (343,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (343, 128, 128, 3)\n", "        observation key ee_ori with shape (343, 3)\n", "        observation key ee_pos with shape (343, 3)\n", "        observation key ee_states with shape (343, 6)\n", "        observation key eye_in_hand_rgb with shape (343, 128, 128, 3)\n", "        observation key gripper_states with shape (343, 2)\n", "        observation key joint_states with shape (343, 7)\n", "    key: rewards with shape (343,)\n", "    key: robot_states with shape (343, 9)\n", "    key: states with shape (343, 47)\n", "episode demo_24 with 267 transitions\n", "    key: actions with shape (267, 7)\n", "    key: dones with shape (267,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (267, 128, 128, 3)\n", "        observation key ee_ori with shape (267, 3)\n", "        observation key ee_pos with shape (267, 3)\n", "        observation key ee_states with shape (267, 6)\n", "        observation key eye_in_hand_rgb with shape (267, 128, 128, 3)\n", "        observation key gripper_states with shape (267, 2)\n", "        observation key joint_states with shape (267, 7)\n", "    key: rewards with shape (267,)\n", "    key: robot_states with shape (267, 9)\n", "    key: states with shape (267, 47)\n", "episode demo_25 with 255 transitions\n", "    key: actions with shape (255, 7)\n", "    key: dones with shape (255,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (255, 128, 128, 3)\n", "        observation key ee_ori with shape (255, 3)\n", "        observation key ee_pos with shape (255, 3)\n", "        observation key ee_states with shape (255, 6)\n", "        observation key eye_in_hand_rgb with shape (255, 128, 128, 3)\n", "        observation key gripper_states with shape (255, 2)\n", "        observation key joint_states with shape (255, 7)\n", "    key: rewards with shape (255,)\n", "    key: robot_states with shape (255, 9)\n", "    key: states with shape (255, 47)\n", "episode demo_26 with 258 transitions\n", "    key: actions with shape (258, 7)\n", "    key: dones with shape (258,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (258, 128, 128, 3)\n", "        observation key ee_ori with shape (258, 3)\n", "        observation key ee_pos with shape (258, 3)\n", "        observation key ee_states with shape (258, 6)\n", "        observation key eye_in_hand_rgb with shape (258, 128, 128, 3)\n", "        observation key gripper_states with shape (258, 2)\n", "        observation key joint_states with shape (258, 7)\n", "    key: rewards with shape (258,)\n", "    key: robot_states with shape (258, 9)\n", "    key: states with shape (258, 47)\n", "episode demo_27 with 373 transitions\n", "    key: actions with shape (373, 7)\n", "    key: dones with shape (373,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (373, 128, 128, 3)\n", "        observation key ee_ori with shape (373, 3)\n", "        observation key ee_pos with shape (373, 3)\n", "        observation key ee_states with shape (373, 6)\n", "        observation key eye_in_hand_rgb with shape (373, 128, 128, 3)\n", "        observation key gripper_states with shape (373, 2)\n", "        observation key joint_states with shape (373, 7)\n", "    key: rewards with shape (373,)\n", "    key: robot_states with shape (373, 9)\n", "    key: states with shape (373, 47)\n", "episode demo_28 with 239 transitions\n", "    key: actions with shape (239, 7)\n", "    key: dones with shape (239,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (239, 128, 128, 3)\n", "        observation key ee_ori with shape (239, 3)\n", "        observation key ee_pos with shape (239, 3)\n", "        observation key ee_states with shape (239, 6)\n", "        observation key eye_in_hand_rgb with shape (239, 128, 128, 3)\n", "        observation key gripper_states with shape (239, 2)\n", "        observation key joint_states with shape (239, 7)\n", "    key: rewards with shape (239,)\n", "    key: robot_states with shape (239, 9)\n", "    key: states with shape (239, 47)\n", "episode demo_29 with 224 transitions\n", "    key: actions with shape (224, 7)\n", "    key: dones with shape (224,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (224, 128, 128, 3)\n", "        observation key ee_ori with shape (224, 3)\n", "        observation key ee_pos with shape (224, 3)\n", "        observation key ee_states with shape (224, 6)\n", "        observation key eye_in_hand_rgb with shape (224, 128, 128, 3)\n", "        observation key gripper_states with shape (224, 2)\n", "        observation key joint_states with shape (224, 7)\n", "    key: rewards with shape (224,)\n", "    key: robot_states with shape (224, 9)\n", "    key: states with shape (224, 47)\n", "episode demo_30 with 337 transitions\n", "    key: actions with shape (337, 7)\n", "    key: dones with shape (337,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (337, 128, 128, 3)\n", "        observation key ee_ori with shape (337, 3)\n", "        observation key ee_pos with shape (337, 3)\n", "        observation key ee_states with shape (337, 6)\n", "        observation key eye_in_hand_rgb with shape (337, 128, 128, 3)\n", "        observation key gripper_states with shape (337, 2)\n", "        observation key joint_states with shape (337, 7)\n", "    key: rewards with shape (337,)\n", "    key: robot_states with shape (337, 9)\n", "    key: states with shape (337, 47)\n", "episode demo_31 with 291 transitions\n", "    key: actions with shape (291, 7)\n", "    key: dones with shape (291,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (291, 128, 128, 3)\n", "        observation key ee_ori with shape (291, 3)\n", "        observation key ee_pos with shape (291, 3)\n", "        observation key ee_states with shape (291, 6)\n", "        observation key eye_in_hand_rgb with shape (291, 128, 128, 3)\n", "        observation key gripper_states with shape (291, 2)\n", "        observation key joint_states with shape (291, 7)\n", "    key: rewards with shape (291,)\n", "    key: robot_states with shape (291, 9)\n", "    key: states with shape (291, 47)\n", "episode demo_32 with 265 transitions\n", "    key: actions with shape (265, 7)\n", "    key: dones with shape (265,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (265, 128, 128, 3)\n", "        observation key ee_ori with shape (265, 3)\n", "        observation key ee_pos with shape (265, 3)\n", "        observation key ee_states with shape (265, 6)\n", "        observation key eye_in_hand_rgb with shape (265, 128, 128, 3)\n", "        observation key gripper_states with shape (265, 2)\n", "        observation key joint_states with shape (265, 7)\n", "    key: rewards with shape (265,)\n", "    key: robot_states with shape (265, 9)\n", "    key: states with shape (265, 47)\n", "episode demo_33 with 256 transitions\n", "    key: actions with shape (256, 7)\n", "    key: dones with shape (256,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (256, 128, 128, 3)\n", "        observation key ee_ori with shape (256, 3)\n", "        observation key ee_pos with shape (256, 3)\n", "        observation key ee_states with shape (256, 6)\n", "        observation key eye_in_hand_rgb with shape (256, 128, 128, 3)\n", "        observation key gripper_states with shape (256, 2)\n", "        observation key joint_states with shape (256, 7)\n", "    key: rewards with shape (256,)\n", "    key: robot_states with shape (256, 9)\n", "    key: states with shape (256, 47)\n", "episode demo_34 with 395 transitions\n", "    key: actions with shape (395, 7)\n", "    key: dones with shape (395,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (395, 128, 128, 3)\n", "        observation key ee_ori with shape (395, 3)\n", "        observation key ee_pos with shape (395, 3)\n", "        observation key ee_states with shape (395, 6)\n", "        observation key eye_in_hand_rgb with shape (395, 128, 128, 3)\n", "        observation key gripper_states with shape (395, 2)\n", "        observation key joint_states with shape (395, 7)\n", "    key: rewards with shape (395,)\n", "    key: robot_states with shape (395, 9)\n", "    key: states with shape (395, 47)\n", "episode demo_35 with 269 transitions\n", "    key: actions with shape (269, 7)\n", "    key: dones with shape (269,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (269, 128, 128, 3)\n", "        observation key ee_ori with shape (269, 3)\n", "        observation key ee_pos with shape (269, 3)\n", "        observation key ee_states with shape (269, 6)\n", "        observation key eye_in_hand_rgb with shape (269, 128, 128, 3)\n", "        observation key gripper_states with shape (269, 2)\n", "        observation key joint_states with shape (269, 7)\n", "    key: rewards with shape (269,)\n", "    key: robot_states with shape (269, 9)\n", "    key: states with shape (269, 47)\n", "episode demo_36 with 449 transitions\n", "    key: actions with shape (449, 7)\n", "    key: dones with shape (449,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (449, 128, 128, 3)\n", "        observation key ee_ori with shape (449, 3)\n", "        observation key ee_pos with shape (449, 3)\n", "        observation key ee_states with shape (449, 6)\n", "        observation key eye_in_hand_rgb with shape (449, 128, 128, 3)\n", "        observation key gripper_states with shape (449, 2)\n", "        observation key joint_states with shape (449, 7)\n", "    key: rewards with shape (449,)\n", "    key: robot_states with shape (449, 9)\n", "    key: states with shape (449, 47)\n", "episode demo_37 with 278 transitions\n", "    key: actions with shape (278, 7)\n", "    key: dones with shape (278,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (278, 128, 128, 3)\n", "        observation key ee_ori with shape (278, 3)\n", "        observation key ee_pos with shape (278, 3)\n", "        observation key ee_states with shape (278, 6)\n", "        observation key eye_in_hand_rgb with shape (278, 128, 128, 3)\n", "        observation key gripper_states with shape (278, 2)\n", "        observation key joint_states with shape (278, 7)\n", "    key: rewards with shape (278,)\n", "    key: robot_states with shape (278, 9)\n", "    key: states with shape (278, 47)\n", "episode demo_38 with 341 transitions\n", "    key: actions with shape (341, 7)\n", "    key: dones with shape (341,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (341, 128, 128, 3)\n", "        observation key ee_ori with shape (341, 3)\n", "        observation key ee_pos with shape (341, 3)\n", "        observation key ee_states with shape (341, 6)\n", "        observation key eye_in_hand_rgb with shape (341, 128, 128, 3)\n", "        observation key gripper_states with shape (341, 2)\n", "        observation key joint_states with shape (341, 7)\n", "    key: rewards with shape (341,)\n", "    key: robot_states with shape (341, 9)\n", "    key: states with shape (341, 47)\n", "episode demo_39 with 345 transitions\n", "    key: actions with shape (345, 7)\n", "    key: dones with shape (345,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (345, 128, 128, 3)\n", "        observation key ee_ori with shape (345, 3)\n", "        observation key ee_pos with shape (345, 3)\n", "        observation key ee_states with shape (345, 6)\n", "        observation key eye_in_hand_rgb with shape (345, 128, 128, 3)\n", "        observation key gripper_states with shape (345, 2)\n", "        observation key joint_states with shape (345, 7)\n", "    key: rewards with shape (345,)\n", "    key: robot_states with shape (345, 9)\n", "    key: states with shape (345, 47)\n", "episode demo_40 with 342 transitions\n", "    key: actions with shape (342, 7)\n", "    key: dones with shape (342,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (342, 128, 128, 3)\n", "        observation key ee_ori with shape (342, 3)\n", "        observation key ee_pos with shape (342, 3)\n", "        observation key ee_states with shape (342, 6)\n", "        observation key eye_in_hand_rgb with shape (342, 128, 128, 3)\n", "        observation key gripper_states with shape (342, 2)\n", "        observation key joint_states with shape (342, 7)\n", "    key: rewards with shape (342,)\n", "    key: robot_states with shape (342, 9)\n", "    key: states with shape (342, 47)\n", "episode demo_41 with 353 transitions\n", "    key: actions with shape (353, 7)\n", "    key: dones with shape (353,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (353, 128, 128, 3)\n", "        observation key ee_ori with shape (353, 3)\n", "        observation key ee_pos with shape (353, 3)\n", "        observation key ee_states with shape (353, 6)\n", "        observation key eye_in_hand_rgb with shape (353, 128, 128, 3)\n", "        observation key gripper_states with shape (353, 2)\n", "        observation key joint_states with shape (353, 7)\n", "    key: rewards with shape (353,)\n", "    key: robot_states with shape (353, 9)\n", "    key: states with shape (353, 47)\n", "episode demo_42 with 288 transitions\n", "    key: actions with shape (288, 7)\n", "    key: dones with shape (288,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (288, 128, 128, 3)\n", "        observation key ee_ori with shape (288, 3)\n", "        observation key ee_pos with shape (288, 3)\n", "        observation key ee_states with shape (288, 6)\n", "        observation key eye_in_hand_rgb with shape (288, 128, 128, 3)\n", "        observation key gripper_states with shape (288, 2)\n", "        observation key joint_states with shape (288, 7)\n", "    key: rewards with shape (288,)\n", "    key: robot_states with shape (288, 9)\n", "    key: states with shape (288, 47)\n", "episode demo_43 with 316 transitions\n", "    key: actions with shape (316, 7)\n", "    key: dones with shape (316,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (316, 128, 128, 3)\n", "        observation key ee_ori with shape (316, 3)\n", "        observation key ee_pos with shape (316, 3)\n", "        observation key ee_states with shape (316, 6)\n", "        observation key eye_in_hand_rgb with shape (316, 128, 128, 3)\n", "        observation key gripper_states with shape (316, 2)\n", "        observation key joint_states with shape (316, 7)\n", "    key: rewards with shape (316,)\n", "    key: robot_states with shape (316, 9)\n", "    key: states with shape (316, 47)\n", "episode demo_44 with 300 transitions\n", "    key: actions with shape (300, 7)\n", "    key: dones with shape (300,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (300, 128, 128, 3)\n", "        observation key ee_ori with shape (300, 3)\n", "        observation key ee_pos with shape (300, 3)\n", "        observation key ee_states with shape (300, 6)\n", "        observation key eye_in_hand_rgb with shape (300, 128, 128, 3)\n", "        observation key gripper_states with shape (300, 2)\n", "        observation key joint_states with shape (300, 7)\n", "    key: rewards with shape (300,)\n", "    key: robot_states with shape (300, 9)\n", "    key: states with shape (300, 47)\n", "episode demo_45 with 375 transitions\n", "    key: actions with shape (375, 7)\n", "    key: dones with shape (375,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (375, 128, 128, 3)\n", "        observation key ee_ori with shape (375, 3)\n", "        observation key ee_pos with shape (375, 3)\n", "        observation key ee_states with shape (375, 6)\n", "        observation key eye_in_hand_rgb with shape (375, 128, 128, 3)\n", "        observation key gripper_states with shape (375, 2)\n", "        observation key joint_states with shape (375, 7)\n", "    key: rewards with shape (375,)\n", "    key: robot_states with shape (375, 9)\n", "    key: states with shape (375, 47)\n", "episode demo_46 with 256 transitions\n", "    key: actions with shape (256, 7)\n", "    key: dones with shape (256,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (256, 128, 128, 3)\n", "        observation key ee_ori with shape (256, 3)\n", "        observation key ee_pos with shape (256, 3)\n", "        observation key ee_states with shape (256, 6)\n", "        observation key eye_in_hand_rgb with shape (256, 128, 128, 3)\n", "        observation key gripper_states with shape (256, 2)\n", "        observation key joint_states with shape (256, 7)\n", "    key: rewards with shape (256,)\n", "    key: robot_states with shape (256, 9)\n", "    key: states with shape (256, 47)\n", "episode demo_47 with 248 transitions\n", "    key: actions with shape (248, 7)\n", "    key: dones with shape (248,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (248, 128, 128, 3)\n", "        observation key ee_ori with shape (248, 3)\n", "        observation key ee_pos with shape (248, 3)\n", "        observation key ee_states with shape (248, 6)\n", "        observation key eye_in_hand_rgb with shape (248, 128, 128, 3)\n", "        observation key gripper_states with shape (248, 2)\n", "        observation key joint_states with shape (248, 7)\n", "    key: rewards with shape (248,)\n", "    key: robot_states with shape (248, 9)\n", "    key: states with shape (248, 47)\n", "episode demo_48 with 267 transitions\n", "    key: actions with shape (267, 7)\n", "    key: dones with shape (267,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (267, 128, 128, 3)\n", "        observation key ee_ori with shape (267, 3)\n", "        observation key ee_pos with shape (267, 3)\n", "        observation key ee_states with shape (267, 6)\n", "        observation key eye_in_hand_rgb with shape (267, 128, 128, 3)\n", "        observation key gripper_states with shape (267, 2)\n", "        observation key joint_states with shape (267, 7)\n", "    key: rewards with shape (267,)\n", "    key: robot_states with shape (267, 9)\n", "    key: states with shape (267, 47)\n", "episode demo_49 with 295 transitions\n", "    key: actions with shape (295, 7)\n", "    key: dones with shape (295,)\n", "    key: obs\n", "        observation key agentview_rgb with shape (295, 128, 128, 3)\n", "        observation key ee_ori with shape (295, 3)\n", "        observation key ee_pos with shape (295, 3)\n", "        observation key ee_states with shape (295, 6)\n", "        observation key eye_in_hand_rgb with shape (295, 128, 128, 3)\n", "        observation key gripper_states with shape (295, 2)\n", "        observation key joint_states with shape (295, 7)\n", "    key: rewards with shape (295,)\n", "    key: robot_states with shape (295, 9)\n", "    key: states with shape (295, 47)\n"]}, {"data": {"text/html": ["\n", "    <video width=\"640\" height=\"480\" controls>\n", "        <source src=\"output.mp4\" type=\"video/mp4\">\n", "    </video>\n", "    <script>\n", "        var video = document.getElementsByTagName('video')[0];\n", "        video.playbackRate = 2.0; // Increase the playback speed to 2x\n", "        </script>    \n"], "text/plain": ["<IPython.core.display.HTML object>"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["import h5py\n", "from libero.libero.utils.dataset_utils import get_dataset_info\n", "from IPython.display import HTML\n", "import imageio\n", "\n", "example_demo_file = demo_files[9]\n", "# Print the dataset info. We have a standalone script for doing the same thing available at `scripts/get_dataset_info.py`\n", "get_dataset_info(example_demo_file)\n", "\n", "with h5py.File(example_demo_file, \"r\") as f:\n", "    images = f[\"data/demo_0/obs/agentview_rgb\"][()]\n", "\n", "video_writer = imageio.get_writer(\"output.mp4\", fps=60)\n", "for image in images:\n", "    video_writer.append_data(image[::-1])\n", "video_writer.close()\n", "\n", "HTML(\"\"\"\n", "    <video width=\"640\" height=\"480\" controls>\n", "        <source src=\"output.mp4\" type=\"video/mp4\">\n", "    </video>\n", "    <script>\n", "        var video = document.getElementsByTagName('video')[0];\n", "        video.playbackRate = 2.0; // Increase the playback speed to 2x\n", "        </script>    \n", "\"\"\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### 2.7 Concate multiple datasets for multit-task training"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of tasks in the benchmark libero_10: 10\n"]}], "source": ["from torch.utils.data import ConcatDataset, Dataset\n", "from libero.lifelong.datasets import get_dataset, SequenceVLDataset\n", "\n", "num_tasks = benchmark_instance.get_num_tasks()\n", "print(f\"Number of tasks in the benchmark {benchmark_instance.name}: {num_tasks}\")\n", "\n", "# manip_datasets = []\n", "# for demo_file in demo_files:\n", "#     task_i_dataset, shape_meta = get_dataset(\n", "#             dataset_path=os.path.join(cfg.folder,\n", "#                                         benchmark.get_task_demonstration(i)),\n", "#             obs_modality=cfg.data.obs.modality,\n", "#             initialize_obs_utils=(i==0),\n", "#             seq_len=cfg.data.seq_len)    \n", "#     manip_datasets.append()\n", "\n", "# concat_dataset = ConcatDataset([get_dataset(demo_file) for demo_file in demo_files])"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### 2.8 Create datasets for Experience Replay algorithm\n", "\n", "In the algorithm of ER, we need to sample data from both dataset of the current task and data from previous experiences. To this end, a specific implementation is needed (`TruncatedSequenceDataset`)."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"interpreter": {"hash": "64fd624917382b0c0ee6e40067ed4768d5d5501e9a56437104405cabbecfa898"}, "kernelspec": {"display_name": "Python 3.8.13 64-bit ('libero': conda)", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.13"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}