{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Overview\n", "\n", "In this notebook, we walk through all the necessary components of running experiments on LIBERO, and some common usage such as defining your own algorithm and policy architectures in the codebase.\n", "\n", "1. Dataset preparation for your algorithms\n", "2. Write your own algorithm\n", "    - Subclassing from `Sequential` base class\n", "3. Write your own model\n", "4. Write your training loop\n", "5. Visualize results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Experiments"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_24770/2563292850.py:18: UserWarning: \n", "The version_base parameter is not specified.\n", "Please specify a compatability version level, or None.\n", "Will assume defaults for version 1.1\n", "  initialize(config_path=\"../libero/configs\")\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{ 'color_aug': { 'network': 'BatchWiseImgColorJitterAug',\n", "                 'network_kwargs': { 'brightness': 0.3,\n", "                                     'contrast': 0.3,\n", "                                     'epsilon': 0.1,\n", "                                     'hue': 0.3,\n", "                                     'input_shape': None,\n", "                                     'saturation': 0.3}},\n", "  'embed_size': 64,\n", "  'extra_hidden_size': 128,\n", "  'extra_num_layers': 0,\n", "  'image_encoder': { 'network': 'ResnetEncoder',\n", "                     'network_kwargs': { 'freeze': False,\n", "                                         'language_fusion': 'film',\n", "                                         'no_stride': <PERSON><PERSON><PERSON>,\n", "                                         'pretrained': <PERSON><PERSON><PERSON>,\n", "                                         'remove_layer_num': 4}},\n", "  'language_encoder': { 'network': 'MLPEncoder',\n", "                        'network_kwargs': { 'hidden_size': 128,\n", "                                            'input_size': 768,\n", "                                            'num_layers': 1,\n", "                                            'output_size': 128}},\n", "  'policy_head': { 'loss_kwargs': {'loss_coef': 1.0},\n", "                   'network': 'GMMHead',\n", "                   'network_kwargs': { 'activation': 'softplus',\n", "                                       'hidden_size': 1024,\n", "                                       'low_eval_noise': False,\n", "                                       'min_std': 0.0001,\n", "                                       'num_layers': 2,\n", "                                       'num_modes': 5}},\n", "  'policy_type': 'BCTransformerPolicy',\n", "  'temporal_position_encoding': { 'network': 'SinusoidalPositionEncoding',\n", "                                  'network_kwargs': { 'factor_ratio': None,\n", "                                                      'input_size': None,\n", "                                                      'inv_freq_factor': 10}},\n", "  'transformer_dropout': 0.1,\n", "  'transformer_head_output_size': 64,\n", "  'transformer_input_size': None,\n", "  'transformer_max_seq_len': 10,\n", "  'transformer_mlp_hidden_size': 256,\n", "  'transformer_num_heads': 6,\n", "  'transformer_num_layers': 4,\n", "  'translation_aug': { 'network': 'TranslationAug',\n", "                       'network_kwargs': { 'input_shape': None,\n", "                                           'translation': 8}}}\n", "('Note that the number of epochs used in this example is intentionally reduced '\n", " 'to 5.')\n", "[info] using task orders [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]\n", "\n", "============= Initialized Observation Utils with Obs Spec =============\n", "\n", "using obs modality: rgb with keys: ['eye_in_hand_rgb', 'agentview_rgb']\n", "using obs modality: depth with keys: []\n", "using obs modality: low_dim with keys: ['gripper_states', 'joint_states']\n", "SequenceDataset: loading dataset into memory...\n", "100%|██████████| 50/50 [00:00<00:00, 1126.19it/s]\n", "SequenceDataset: loading dataset into memory...\n", "100%|██████████| 50/50 [00:00<00:00, 1162.72it/s]\n", "SequenceDataset: loading dataset into memory...\n", "100%|██████████| 50/50 [00:00<00:00, 1175.77it/s]\n", "SequenceDataset: loading dataset into memory...\n", "100%|██████████| 50/50 [00:00<00:00, 1206.08it/s]\n", "SequenceDataset: loading dataset into memory...\n", "100%|██████████| 50/50 [00:00<00:00, 1134.37it/s]\n", "SequenceDataset: loading dataset into memory...\n", "100%|██████████| 50/50 [00:00<00:00, 967.75it/s]\n", "SequenceDataset: loading dataset into memory...\n", "100%|██████████| 50/50 [00:00<00:00, 1178.42it/s]\n", "SequenceDataset: loading dataset into memory...\n", "100%|██████████| 50/50 [00:00<00:00, 1222.58it/s]\n", "SequenceDataset: loading dataset into memory...\n", "100%|██████████| 50/50 [00:00<00:00, 1164.09it/s]\n", "SequenceDataset: loading dataset into memory...\n", "100%|██████████| 50/50 [00:00<00:00, 1151.78it/s]\n"]}], "source": ["from hydra import compose, initialize\n", "\n", "from libero.libero import benchmark, get_libero_path\n", "import hydra\n", "import pprint\n", "import os\n", "os.environ[\"TOKENIZERS_PARALLELISM\"] = \"false\"\n", "os.environ['PYOPENGL_PLATFORM'] = 'egl'\n", "from omegaconf import OmegaConf\n", "import yaml\n", "from easydict import EasyDict\n", "from libero.libero.benchmark import get_benchmark\n", "from libero.lifelong.datasets import (GroupedTaskDataset, SequenceVLDataset, get_dataset)\n", "from libero.lifelong.utils import (get_task_embs, safe_device, create_experiment_dir)\n", "hydra.core.global_hydra.GlobalHydra.instance().clear()\n", "\n", "### load the default hydra config\n", "initialize(config_path=\"../libero/configs\")\n", "hydra_cfg = compose(config_name=\"config\")\n", "yaml_config = OmegaConf.to_yaml(hydra_cfg)\n", "cfg = EasyDict(yaml.safe_load(yaml_config))\n", "\n", "pp = pprint.PrettyPrinter(indent=2)\n", "pp.pprint(cfg.policy)\n", "\n", "# prepare lifelong learning\n", "cfg.folder = get_libero_path(\"datasets\")\n", "cfg.bddl_folder = get_libero_path(\"bddl_files\")\n", "cfg.init_states_folder = get_libero_path(\"init_states\")\n", "cfg.eval.num_procs = 1\n", "cfg.eval.n_eval = 5\n", "\n", "cfg.train.n_epochs = 25\n", "\n", "pp.pprint(f\"Note that the number of epochs used in this example is intentionally reduced to 5.\")\n", "\n", "task_order = cfg.data.task_order_index # can be from {0 .. 21}, default to 0, which is [task 0, 1, 2 ...]\n", "cfg.benchmark_name = \"libero_object\" # can be from {\"libero_spatial\", \"libero_object\", \"libero_goal\", \"libero_10\"}\n", "benchmark = get_benchmark(cfg.benchmark_name)(task_order)\n", "\n", "# prepare datasets from the benchmark\n", "datasets = []\n", "descriptions = []\n", "shape_meta = None\n", "n_tasks = benchmark.n_tasks\n", "\n", "for i in range(n_tasks):\n", "    # currently we assume tasks from same benchmark have the same shape_meta\n", "    task_i_dataset, shape_meta = get_dataset(\n", "            dataset_path=os.path.join(cfg.folder, benchmark.get_task_demonstration(i)),\n", "            obs_modality=cfg.data.obs.modality,\n", "            initialize_obs_utils=(i==0),\n", "            seq_len=cfg.data.seq_len,\n", "    )\n", "    # add language to the vision dataset, hence we call vl_dataset\n", "    descriptions.append(benchmark.get_task(i).language)\n", "    datasets.append(task_i_dataset)\n", "\n", "task_embs = get_task_embs(cfg, descriptions)\n", "benchmark.set_task_embs(task_embs)\n", "\n", "datasets = [SequenceVLDataset(ds, emb) for (ds, emb) in zip(datasets, task_embs)]\n", "n_demos = [data.n_demos for data in datasets]\n", "n_sequences = [data.total_num_sequences for data in datasets]"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## 2. Write your own policy architecture"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import robomimic.utils.tensor_utils as TensorUtils\n", "import torch\n", "import torch.nn as nn\n", "\n", "from einops import rearrange, repeat\n", "from libero.lifelong.models.modules.rgb_modules import *\n", "from libero.lifelong.models.modules.language_modules import *\n", "from libero.lifelong.models.base_policy import BasePolicy\n", "from libero.lifelong.models.policy_head import *\n", "from libero.lifelong.models.modules.transformer_modules import *\n", "\n", "###############################################################################\n", "#\n", "# A model handling extra input modalities besides images at time t.\n", "#\n", "###############################################################################\n", "\n", "class ExtraModalityTokens(nn.Module):\n", "    def __init__(\n", "        self,\n", "        use_joint=False,\n", "        use_gripper=False,\n", "        use_ee=False,\n", "        extra_num_layers=0,\n", "        extra_hidden_size=64,\n", "        extra_embedding_size=32,\n", "    ):\n", "        \"\"\"\n", "        This is a class that maps all extra modality inputs into tokens of the same size\n", "        \"\"\"\n", "        super().__init__()\n", "        self.use_joint = use_joint\n", "        self.use_gripper = use_gripper\n", "        self.use_ee = use_ee\n", "        self.extra_embedding_size = extra_embedding_size\n", "\n", "        joint_states_dim = 7\n", "        gripper_states_dim = 2\n", "        ee_dim = 3\n", "\n", "        self.num_extra = int(use_joint) + int(use_gripper) + int(use_ee)\n", "\n", "        extra_low_level_feature_dim = (\n", "            int(use_joint) * joint_states_dim\n", "            + int(use_gripper) * gripper_states_dim\n", "            + int(use_ee) * ee_dim\n", "        )\n", "\n", "        assert extra_low_level_feature_dim > 0, \"[error] no extra information\"\n", "\n", "        self.extra_encoders = {}\n", "\n", "        def generate_proprio_mlp_fn(modality_name, extra_low_level_feature_dim):\n", "            assert extra_low_level_feature_dim > 0  # we indeed have extra information\n", "            if extra_num_layers > 0:\n", "                layers = [nn.Linear(extra_low_level_feature_dim, extra_hidden_size)]\n", "                for i in range(1, extra_num_layers):\n", "                    layers += [\n", "                        nn.Linear(extra_hidden_size, extra_hidden_size),\n", "                        nn.ReLU(inplace=True),\n", "                    ]\n", "                layers += [nn.Linear(extra_hidden_size, extra_embedding_size)]\n", "            else:\n", "                layers = [nn.Linear(extra_low_level_feature_dim, extra_embedding_size)]\n", "\n", "            self.proprio_mlp = nn.Sequential(*layers)\n", "            self.extra_encoders[modality_name] = {\"encoder\": self.proprio_mlp}\n", "\n", "        for (proprio_dim, use_modality, modality_name) in [\n", "            (joint_states_dim, self.use_joint, \"joint_states\"),\n", "            (gripper_states_dim, self.use_gripper, \"gripper_states\"),\n", "            (ee_dim, self.use_ee, \"ee_states\"),\n", "        ]:\n", "\n", "            if use_modality:\n", "                generate_proprio_mlp_fn(modality_name, proprio_dim)\n", "\n", "        self.encoders = nn.ModuleList(\n", "            [x[\"encoder\"] for x in self.extra_encoders.values()]\n", "        )\n", "\n", "    def forward(self, obs_dict):\n", "        \"\"\"\n", "        obs_dict: {\n", "            (optional) joint_stats: (B, T, 7),\n", "            (optional) gripper_states: (B, T, 2),\n", "            (optional) ee: (B, T, 3)\n", "        }\n", "        map above to a latent vector of shape (B, T, H)\n", "        \"\"\"\n", "        tensor_list = []\n", "\n", "        for (use_modality, modality_name) in [\n", "            (self.use_joint, \"joint_states\"),\n", "            (self.use_gripper, \"gripper_states\"),\n", "            (self.use_ee, \"ee_states\"),\n", "        ]:\n", "\n", "            if use_modality:\n", "                tensor_list.append(\n", "                    self.extra_encoders[modality_name][\"encoder\"](\n", "                        obs_dict[modality_name]\n", "                    )\n", "                )\n", "\n", "        x = torch.stack(tensor_list, dim=-2)\n", "        return x\n", "\n", "###############################################################################\n", "#\n", "# A Transformer policy\n", "#\n", "###############################################################################\n", "\n", "\n", "class MyTransformerPolicy(BasePolicy):\n", "    \"\"\"\n", "    Input: (o_{t-H}, ... , o_t)\n", "    Output: a_t or distribution of a_t\n", "    \"\"\"\n", "\n", "    def __init__(self, cfg, shape_meta):\n", "        super().__init__(cfg, shape_meta)\n", "        policy_cfg = cfg.policy\n", "\n", "        ### 1. encode image\n", "        embed_size = policy_cfg.embed_size\n", "        transformer_input_sizes = []\n", "        self.image_encoders = {}\n", "        for name in shape_meta[\"all_shapes\"].keys():\n", "            if \"rgb\" in name or \"depth\" in name:\n", "                kwargs = policy_cfg.image_encoder.network_kwargs\n", "                kwargs.input_shape = shape_meta[\"all_shapes\"][name]\n", "                kwargs.output_size = embed_size\n", "                kwargs.language_dim = (\n", "                    policy_cfg.language_encoder.network_kwargs.input_size\n", "                )\n", "                self.image_encoders[name] = {\n", "                    \"input_shape\": shape_meta[\"all_shapes\"][name],\n", "                    \"encoder\": eval(policy_cfg.image_encoder.network)(**kwargs),\n", "                }\n", "\n", "        self.encoders = nn.ModuleList(\n", "            [x[\"encoder\"] for x in self.image_encoders.values()]\n", "        )\n", "\n", "        ### 2. encode language\n", "        policy_cfg.language_encoder.network_kwargs.output_size = embed_size\n", "        self.language_encoder = eval(policy_cfg.language_encoder.network)(\n", "            **policy_cfg.language_encoder.network_kwargs\n", "        )\n", "\n", "        ### 3. encode extra information (e.g. gripper, joint_state)\n", "        self.extra_encoder = ExtraModalityTokens(\n", "            use_joint=cfg.data.use_joint,\n", "            use_gripper=cfg.data.use_gripper,\n", "            use_ee=cfg.data.use_ee,\n", "            extra_num_layers=policy_cfg.extra_num_layers,\n", "            extra_hidden_size=policy_cfg.extra_hidden_size,\n", "            extra_embedding_size=embed_size,\n", "        )\n", "\n", "        ### 4. define temporal transformer\n", "        policy_cfg.temporal_position_encoding.network_kwargs.input_size = embed_size\n", "        self.temporal_position_encoding_fn = eval(\n", "            policy_cfg.temporal_position_encoding.network\n", "        )(**policy_cfg.temporal_position_encoding.network_kwargs)\n", "\n", "        self.temporal_transformer = TransformerDecoder(\n", "            input_size=embed_size,\n", "            num_layers=policy_cfg.transformer_num_layers,\n", "            num_heads=policy_cfg.transformer_num_heads,\n", "            head_output_size=policy_cfg.transformer_head_output_size,\n", "            mlp_hidden_size=policy_cfg.transformer_mlp_hidden_size,\n", "            dropout=policy_cfg.transformer_dropout,\n", "        )\n", "\n", "        policy_head_kwargs = policy_cfg.policy_head.network_kwargs\n", "        policy_head_kwargs.input_size = embed_size\n", "        policy_head_kwargs.output_size = shape_meta[\"ac_dim\"]\n", "\n", "        self.policy_head = eval(policy_cfg.policy_head.network)(\n", "            **policy_cfg.policy_head.loss_kwargs,\n", "            **policy_cfg.policy_head.network_kwargs\n", "        )\n", "\n", "        self.latent_queue = []\n", "        self.max_seq_len = policy_cfg.transformer_max_seq_len\n", "\n", "    def temporal_encode(self, x):\n", "        pos_emb = self.temporal_position_encoding_fn(x)\n", "        x = x + pos_emb.unsqueeze(1)  # (B, T, num_modality, E)\n", "        sh = x.shape\n", "        self.temporal_transformer.compute_mask(x.shape)\n", "\n", "        x = TensorUtils.join_dimensions(x, 1, 2)  # (B, T*num_modality, E)\n", "        x = self.temporal_transformer(x)\n", "        x = x.reshape(*sh)\n", "        return x[:, :, 0]  # (B, T, E)\n", "\n", "    def spatial_encode(self, data):\n", "        # 1. encode extra\n", "        extra = self.extra_encoder(data[\"obs\"])  # (B, T, num_extra, E)\n", "\n", "        # 2. encode language, treat it as action token\n", "        B, T = extra.shape[:2]\n", "        text_encoded = self.language_encoder(data)  # (B, E)\n", "        text_encoded = text_encoded.view(B, 1, 1, -1).expand(\n", "            -1, T, -1, -1\n", "        )  # (B, T, 1, E)\n", "        encoded = [text_encoded, extra]\n", "\n", "        # 3. encode image\n", "        for img_name in self.image_encoders.keys():\n", "            x = data[\"obs\"][img_name]\n", "            B, T, C, H, W = x.shape\n", "            img_encoded = self.image_encoders[img_name][\"encoder\"](\n", "                x.reshape(B * T, C, H, W),\n", "                langs=data[\"task_emb\"]\n", "                .reshape(B, 1, -1)\n", "                .repeat(1, T, 1)\n", "                .reshape(B * T, -1),\n", "            ).view(B, T, 1, -1)\n", "            encoded.append(img_encoded)\n", "        encoded = torch.cat(encoded, -2)  # (B, T, num_modalities, E)\n", "        return encoded\n", "\n", "    def forward(self, data):\n", "        x = self.spatial_encode(data)\n", "        x = self.temporal_encode(x)\n", "        dist = self.policy_head(x)\n", "        return dist\n", "\n", "    def get_action(self, data):\n", "        self.eval()\n", "        with torch.no_grad():\n", "            data = self.preprocess_input(data, train_mode=False)\n", "            x = self.spatial_encode(data)\n", "            self.latent_queue.append(x)\n", "            if len(self.latent_queue) > self.max_seq_len:\n", "                self.latent_queue.pop(0)\n", "            x = torch.cat(self.latent_queue, dim=1)  # (B, T, H_all)\n", "            x = self.temporal_encode(x)\n", "            dist = self.policy_head(x[:, -1])\n", "        action = dist.sample().detach().cpu()\n", "        return action.view(action.shape[0], -1).numpy()\n", "\n", "    def reset(self):\n", "        self.latent_queue = []"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## 3. Write your own lifelong learning algorithm"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[1m\u001b[33m[robosuite WARNING] \u001b[0mNo private macro file found! (__init__.py:7)\n", "\u001b[1m\u001b[33m[robosuite WARNING] \u001b[0mIt is recommended to use a private macro file (__init__.py:8)\n", "\u001b[1m\u001b[33m[robosuite WARNING] \u001b[0mTo setup, run: python /home/<USER>/workspace/robosuite-master/robosuite/scripts/setup_macros.py (__init__.py:9)\n"]}], "source": ["from libero.lifelong.algos.base import Sequential\n", "\n", "### All lifelong learning algorithm should inherit the Sequential algorithm super class\n", "\n", "class MyLifelongAlgo(Sequential):\n", "    \"\"\"\n", "    The experience replay policy.\n", "    \"\"\"\n", "    def __init__(self,\n", "                 n_tasks,\n", "                 cfg,\n", "                 **policy_kwargs):\n", "        super().__init__(n_tasks=n_tasks, cfg=cfg, **policy_kwargs)\n", "        # define the learning policy\n", "        self.datasets = []\n", "        self.policy = eval(cfg.policy.policy_type)(cfg, cfg.shape_meta)\n", "\n", "    def start_task(self, task):\n", "        # what to do at the beginning of a new task\n", "        super().start_task(task)\n", "\n", "    def end_task(self, dataset, task_id, benchmark):\n", "        # what to do when finish learning a new task\n", "        self.datasets.append(dataset)\n", "\n", "    def observe(self, data):\n", "        # how the algorithm observes a data and returns a loss to be optimized\n", "        loss = super().observe(data)\n", "        return loss"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 4. Write your training script"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["experiment directory is:  ./experiments/libero_object/MyLifelongAlgo/MyTransformerPolicy_seed10000/run_006\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/.pyenv/versions/3.8.5/envs/new-continual-learning/lib/python3.8/site-packages/torchvision/models/_utils.py:208: UserWarning: The parameter 'pretrained' is deprecated since 0.13 and will be removed in 0.15, please use 'weights' instead.\n", "  warnings.warn(\n", "/home/<USER>/.pyenv/versions/3.8.5/envs/new-continual-learning/lib/python3.8/site-packages/torchvision/models/_utils.py:223: UserWarning: Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and will be removed in 0.15. The current behavior is equivalent to passing `weights=None`.\n", "  warnings.warn(msg)\n", "/home/<USER>/.pyenv/versions/3.8.5/envs/new-continual-learning/lib/python3.8/site-packages/torch/functional.py:478: UserWarning: torch.meshgrid: in an upcoming release, it will be required to pass the indexing argument. (Triggered internally at  ../aten/src/ATen/native/TensorShape.cpp:2894.)\n", "  return _VF.meshgrid(tensors, **kwargs)  # type: ignore[attr-defined]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["NOTE: the number of epochs used in this example is intentionally reduced to 30 for simplicity.\n", "NOTE: the number of evaluation episodes used in this example is intentionally reduced to 5 for simplicity.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["[info] Epoch:   0 | train loss:  5.45 | time: 0.64\n", "[info] evaluate task 0 takes 109.5 seconds\n", "[info] Epoch:   0 | succ: 0.00 ± 0.00 | best succ: 0.0 | succ. AoC 0.00 | time: 1.82\n", "[info] Epoch:   1 | train loss: -6.00 | time: 1.32\n", "[info] Epoch:   2 | train loss: -12.35 | time: 1.31\n", "[info] Epoch:   3 | train loss: -14.18 | time: 1.30\n", "[info] Epoch:   4 | train loss: -14.78 | time: 1.30\n", "[info] Epoch:   5 | train loss: -15.23 | time: 1.28\n", "[info] evaluate task 0 takes 105.7 seconds\n", "[info] Epoch:   5 | succ: 0.00 ± 0.00 | best succ: 0.0 | succ. AoC 0.00 | time: 1.76\n", "[info] Epoch:   6 | train loss: -15.64 | time: 1.22\n", "[info] Epoch:   7 | train loss: -16.03 | time: 1.22\n", "[info] Epoch:   8 | train loss: -16.58 | time: 1.22\n", "[info] Epoch:   9 | train loss: -16.81 | time: 1.22\n", "[info] Epoch:  10 | train loss: -17.12 | time: 1.22\n", "[info] evaluate task 0 takes 68.6 seconds\n", "[info] Epoch:  10 | succ: 0.60 ± 0.43 | best succ: 0.6 | succ. AoC 0.20 | time: 1.14\n", "[info] Epoch:  11 | train loss: -17.29 | time: 1.22\n", "[info] Epoch:  12 | train loss: -17.54 | time: 1.22\n", "[info] Epoch:  13 | train loss: -17.82 | time: 1.22\n", "[info] Epoch:  14 | train loss: -18.08 | time: 1.23\n", "[info] Epoch:  15 | train loss: -18.24 | time: 1.23\n", "[info] evaluate task 0 takes 62.2 seconds\n", "[info] Epoch:  15 | succ: 0.80 ± 0.35 | best succ: 0.8 | succ. AoC 0.35 | time: 1.04\n", "[info] Epoch:  16 | train loss: -18.42 | time: 1.22\n", "[info] Epoch:  17 | train loss: -18.62 | time: 1.22\n", "[info] Epoch:  18 | train loss: -18.84 | time: 1.22\n", "[info] Epoch:  19 | train loss: -19.02 | time: 1.22\n", "[info] Epoch:  20 | train loss: -19.19 | time: 1.22\n", "[info] evaluate task 0 takes 63.3 seconds\n", "[info] Epoch:  20 | succ: 0.80 ± 0.35 | best succ: 0.8 | succ. AoC 0.44 | time: 1.05\n", "[info] Epoch:  21 | train loss: -19.29 | time: 1.22\n", "[info] Epoch:  22 | train loss: -19.42 | time: 1.22\n", "[info] Epoch:  23 | train loss: -19.51 | time: 1.22\n", "[info] Epoch:  24 | train loss: -19.62 | time: 1.22\n", "[info] Epoch:  25 | train loss: -19.65 | time: 1.22\n", "[info] evaluate task 0 takes 108.8 seconds\n", "[info] Epoch:  25 | succ: 0.00 ± 0.00 | best succ: 0.8 | succ. AoC 0.50 | time: 1.81\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 10%|█         | 1/10 [41:46<6:15:58, 2506.46s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[info] evaluate task 0 takes 72.8 seconds\n", "[info] Epoch:   0 | train loss: -4.56 | time: 0.52\n", "[info] evaluate task 1 takes 109.4 seconds\n", "[info] Epoch:   0 | succ: 0.00 ± 0.00 | best succ: 0.0 | succ. AoC 0.00 | time: 1.82\n", "[info] Epoch:   1 | train loss: -15.55 | time: 1.11\n", "[info] Epoch:   2 | train loss: -16.33 | time: 1.12\n", "[info] Epoch:   3 | train loss: -16.65 | time: 1.12\n", "[info] Epoch:   4 | train loss: -16.91 | time: 1.12\n", "[info] Epoch:   5 | train loss: -17.16 | time: 1.12\n", "[info] evaluate task 1 takes 100.9 seconds\n", "[info] Epoch:   5 | succ: 0.20 ± 0.35 | best succ: 0.2 | succ. AoC 0.10 | time: 1.68\n", "[info] Epoch:   6 | train loss: -17.32 | time: 1.12\n", "[info] Epoch:   7 | train loss: -17.52 | time: 1.12\n", "[info] Epoch:   8 | train loss: -17.79 | time: 1.12\n", "[info] Epoch:   9 | train loss: -17.99 | time: 1.13\n", "[info] Epoch:  10 | train loss: -18.20 | time: 1.13\n", "[info] evaluate task 1 takes 68.1 seconds\n", "[info] Epoch:  10 | succ: 0.60 ± 0.43 | best succ: 0.6 | succ. AoC 0.27 | time: 1.14\n", "[info] Epoch:  11 | train loss: -18.49 | time: 1.12\n", "[info] Epoch:  12 | train loss: -18.73 | time: 1.13\n", "[info] Epoch:  13 | train loss: -18.94 | time: 1.12\n", "[info] Epoch:  14 | train loss: -19.16 | time: 1.13\n", "[info] Epoch:  15 | train loss: -19.41 | time: 1.13\n", "[info] evaluate task 1 takes 40.1 seconds\n", "[info] Epoch:  15 | succ: 1.00 ± 0.00 | best succ: 1.0 | succ. AoC 0.45 | time: 0.67\n", "[info] Epoch:  16 | train loss: -19.62 | time: 1.12\n", "[info] Epoch:  17 | train loss: -19.79 | time: 1.13\n", "[info] Epoch:  18 | train loss: -19.98 | time: 1.13\n", "[info] Epoch:  19 | train loss: -20.18 | time: 1.13\n", "[info] Epoch:  20 | train loss: -20.32 | time: 1.13\n", "[info] evaluate task 1 takes 66.0 seconds\n", "[info] Epoch:  20 | succ: 0.60 ± 0.43 | best succ: 1.0 | succ. AoC 0.56 | time: 1.10\n", "[info] Epoch:  21 | train loss: -20.45 | time: 1.12\n", "[info] Epoch:  22 | train loss: -20.59 | time: 1.13\n", "[info] Epoch:  23 | train loss: -20.66 | time: 1.13\n", "[info] Epoch:  24 | train loss: -20.78 | time: 1.13\n", "[info] Epoch:  25 | train loss: -20.83 | time: 1.13\n", "[info] evaluate task 1 takes 73.4 seconds\n", "[info] Epoch:  25 | succ: 0.60 ± 0.43 | best succ: 1.0 | succ. AoC 0.63 | time: 1.22\n", "[info] evaluate task 0 takes 104.8 seconds\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 20%|██        | 2/10 [1:21:19<5:23:42, 2427.81s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[info] evaluate task 1 takes 52.7 seconds\n", "[info] Epoch:   0 | train loss: -9.45 | time: 0.48\n", "[info] evaluate task 2 takes 109.7 seconds\n", "[info] Epoch:   0 | succ: 0.00 ± 0.00 | best succ: 0.0 | succ. AoC 0.00 | time: 1.83\n", "[info] Epoch:   1 | train loss: -16.82 | time: 1.03\n", "[info] Epoch:   2 | train loss: -17.54 | time: 1.04\n", "[info] Epoch:   3 | train loss: -17.92 | time: 1.04\n", "[info] Epoch:   4 | train loss: -18.20 | time: 1.04\n", "[info] Epoch:   5 | train loss: -18.40 | time: 1.04\n", "[info] evaluate task 2 takes 49.9 seconds\n", "[info] Epoch:   5 | succ: 0.80 ± 0.35 | best succ: 0.8 | succ. AoC 0.40 | time: 0.83\n", "[info] Epoch:   6 | train loss: -18.63 | time: 1.04\n", "[info] Epoch:   7 | train loss: -18.92 | time: 1.04\n", "[info] Epoch:   8 | train loss: -19.14 | time: 1.04\n", "[info] Epoch:   9 | train loss: -19.38 | time: 1.04\n", "[info] Epoch:  10 | train loss: -19.56 | time: 1.04\n", "[info] evaluate task 2 takes 68.7 seconds\n", "[info] Epoch:  10 | succ: 0.60 ± 0.43 | best succ: 0.8 | succ. AoC 0.53 | time: 1.14\n", "[info] Epoch:  11 | train loss: -19.80 | time: 1.04\n", "[info] Epoch:  12 | train loss: -20.00 | time: 1.04\n", "[info] Epoch:  13 | train loss: -20.23 | time: 1.04\n", "[info] Epoch:  14 | train loss: -20.45 | time: 1.04\n", "[info] Epoch:  15 | train loss: -20.64 | time: 1.04\n", "[info] evaluate task 2 takes 80.8 seconds\n", "[info] Epoch:  15 | succ: 0.40 ± 0.43 | best succ: 0.8 | succ. AoC 0.60 | time: 1.35\n", "[info] Epoch:  16 | train loss: -20.84 | time: 1.03\n", "[info] Epoch:  17 | train loss: -21.06 | time: 1.04\n", "[info] Epoch:  18 | train loss: -21.23 | time: 1.04\n", "[info] Epoch:  19 | train loss: -21.34 | time: 1.04\n", "[info] Epoch:  20 | train loss: -21.51 | time: 1.04\n", "[info] evaluate task 2 takes 93.5 seconds\n", "[info] Epoch:  20 | succ: 0.20 ± 0.35 | best succ: 0.8 | succ. AoC 0.64 | time: 1.56\n", "[info] Epoch:  21 | train loss: -21.63 | time: 1.04\n", "[info] Epoch:  22 | train loss: -21.76 | time: 1.04\n", "[info] Epoch:  23 | train loss: -21.83 | time: 1.04\n", "[info] Epoch:  24 | train loss: -21.94 | time: 1.04\n", "[info] Epoch:  25 | train loss: -21.99 | time: 1.05\n", "[info] evaluate task 2 takes 79.4 seconds\n", "[info] Epoch:  25 | succ: 0.40 ± 0.43 | best succ: 0.8 | succ. AoC 0.67 | time: 1.32\n", "[info] evaluate task 0 takes 108.2 seconds\n", "[info] evaluate task 1 takes 108.6 seconds\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 30%|███       | 3/10 [2:01:15<4:41:34, 2413.45s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[info] evaluate task 2 takes 50.7 seconds\n", "[info] Epoch:   0 | train loss: -13.37 | time: 0.53\n", "[info] evaluate task 3 takes 109.4 seconds\n", "[info] Epoch:   0 | succ: 0.00 ± 0.00 | best succ: 0.0 | succ. AoC 0.00 | time: 1.82\n", "[info] Epoch:   1 | train loss: -16.90 | time: 1.14\n", "[info] Epoch:   2 | train loss: -17.55 | time: 1.14\n", "[info] Epoch:   3 | train loss: -17.89 | time: 1.15\n", "[info] Epoch:   4 | train loss: -18.27 | time: 1.15\n", "[info] Epoch:   5 | train loss: -18.57 | time: 1.15\n", "[info] evaluate task 3 takes 89.3 seconds\n", "[info] Epoch:   5 | succ: 0.20 ± 0.35 | best succ: 0.2 | succ. AoC 0.10 | time: 1.49\n", "[info] Epoch:   6 | train loss: -18.87 | time: 1.14\n", "[info] Epoch:   7 | train loss: -19.15 | time: 1.15\n", "[info] Epoch:   8 | train loss: -19.43 | time: 1.15\n", "[info] Epoch:   9 | train loss: -19.69 | time: 1.15\n", "[info] Epoch:  10 | train loss: -19.93 | time: 1.15\n", "[info] evaluate task 3 takes 90.7 seconds\n", "[info] Epoch:  10 | succ: 0.20 ± 0.35 | best succ: 0.2 | succ. AoC 0.13 | time: 1.51\n", "[info] Epoch:  11 | train loss: -20.14 | time: 1.15\n", "[info] Epoch:  12 | train loss: -20.42 | time: 1.15\n", "[info] Epoch:  13 | train loss: -20.63 | time: 1.15\n", "[info] Epoch:  14 | train loss: -20.93 | time: 1.15\n", "[info] Epoch:  15 | train loss: -21.06 | time: 1.16\n", "[info] evaluate task 3 takes 78.1 seconds\n", "[info] Epoch:  15 | succ: 0.40 ± 0.43 | best succ: 0.4 | succ. AoC 0.20 | time: 1.30\n", "[info] Epoch:  16 | train loss: -21.28 | time: 1.15\n", "[info] Epoch:  17 | train loss: -21.54 | time: 1.15\n", "[info] Epoch:  18 | train loss: -21.69 | time: 1.16\n", "[info] Epoch:  19 | train loss: -21.90 | time: 1.16\n", "[info] Epoch:  20 | train loss: -22.00 | time: 1.16\n", "[info] evaluate task 3 takes 64.4 seconds\n", "[info] Epoch:  20 | succ: 0.60 ± 0.43 | best succ: 0.6 | succ. AoC 0.28 | time: 1.07\n", "[info] Epoch:  21 | train loss: -22.18 | time: 1.15\n", "[info] Epoch:  22 | train loss: -22.30 | time: 1.15\n", "[info] Epoch:  23 | train loss: -22.38 | time: 1.16\n", "[info] Epoch:  24 | train loss: -22.44 | time: 1.15\n", "[info] Epoch:  25 | train loss: -22.52 | time: 1.16\n", "[info] evaluate task 3 takes 63.9 seconds\n", "[info] Epoch:  25 | succ: 0.60 ± 0.43 | best succ: 0.6 | succ. AoC 0.33 | time: 1.07\n", "[info] evaluate task 0 takes 104.3 seconds\n", "[info] evaluate task 1 takes 105.9 seconds\n", "[info] evaluate task 2 takes 90.2 seconds\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 40%|████      | 4/10 [2:46:12<4:12:32, 2525.49s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[info] evaluate task 3 takes 64.2 seconds\n", "[info] Epoch:   0 | train loss:  5.97 | time: 0.58\n", "[info] evaluate task 4 takes 104.8 seconds\n", "[info] Epoch:   0 | succ: 0.00 ± 0.00 | best succ: 0.0 | succ. AoC 0.00 | time: 1.75\n", "[info] Epoch:   1 | train loss: -16.72 | time: 1.25\n", "[info] Epoch:   2 | train loss: -17.93 | time: 1.26\n", "[info] Epoch:   3 | train loss: -18.50 | time: 1.26\n", "[info] Epoch:   4 | train loss: -18.88 | time: 1.26\n", "[info] Epoch:   5 | train loss: -19.33 | time: 1.26\n", "[info] evaluate task 4 takes 97.5 seconds\n", "[info] Epoch:   5 | succ: 0.20 ± 0.35 | best succ: 0.2 | succ. AoC 0.10 | time: 1.63\n", "[info] Epoch:   6 | train loss: -19.63 | time: 1.25\n", "[info] Epoch:   7 | train loss: -19.99 | time: 1.26\n", "[info] Epoch:   8 | train loss: -20.30 | time: 1.27\n", "[info] Epoch:   9 | train loss: -20.58 | time: 1.27\n", "[info] Epoch:  10 | train loss: -20.83 | time: 1.27\n", "[info] evaluate task 4 takes 51.9 seconds\n", "[info] Epoch:  10 | succ: 0.80 ± 0.35 | best succ: 0.8 | succ. AoC 0.33 | time: 0.87\n", "[info] Epoch:  11 | train loss: -21.13 | time: 1.26\n", "[info] Epoch:  12 | train loss: -21.44 | time: 1.26\n", "[info] Epoch:  13 | train loss: -21.69 | time: 1.27\n", "[info] Epoch:  14 | train loss: -21.90 | time: 1.27\n", "[info] Epoch:  15 | train loss: -22.12 | time: 1.26\n", "[info] evaluate task 4 takes 55.2 seconds\n", "[info] Epoch:  15 | succ: 0.80 ± 0.35 | best succ: 0.8 | succ. AoC 0.45 | time: 0.92\n", "[info] Epoch:  16 | train loss: -22.37 | time: 1.26\n", "[info] Epoch:  17 | train loss: -22.58 | time: 1.27\n", "[info] Epoch:  18 | train loss: -22.76 | time: 1.27\n", "[info] Epoch:  19 | train loss: -22.93 | time: 1.27\n", "[info] Epoch:  20 | train loss: -23.10 | time: 1.27\n", "[info] evaluate task 4 takes 64.8 seconds\n", "[info] Epoch:  20 | succ: 0.60 ± 0.43 | best succ: 0.8 | succ. AoC 0.52 | time: 1.08\n", "[info] Epoch:  21 | train loss: -23.22 | time: 1.26\n", "[info] Epoch:  22 | train loss: -23.36 | time: 1.26\n", "[info] Epoch:  23 | train loss: -23.44 | time: 1.26\n", "[info] Epoch:  24 | train loss: -23.51 | time: 1.27\n", "[info] Epoch:  25 | train loss: -23.59 | time: 1.27\n", "[info] evaluate task 4 takes 52.3 seconds\n", "[info] Epoch:  25 | succ: 0.80 ± 0.35 | best succ: 0.8 | succ. AoC 0.57 | time: 0.87\n", "[info] evaluate task 0 takes 106.3 seconds\n", "[info] evaluate task 1 takes 107.7 seconds\n", "[info] evaluate task 2 takes 109.5 seconds\n", "[info] evaluate task 3 takes 108.0 seconds\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 50%|█████     | 5/10 [3:35:12<3:42:53, 2674.79s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[info] evaluate task 4 takes 52.7 seconds\n", "[info] Epoch:   0 | train loss: -2.67 | time: 0.58\n", "[info] evaluate task 5 takes 108.5 seconds\n", "[info] Epoch:   0 | succ: 0.00 ± 0.00 | best succ: 0.0 | succ. AoC 0.00 | time: 1.81\n", "[info] Epoch:   1 | train loss: -16.63 | time: 1.21\n", "[info] Epoch:   2 | train loss: -17.75 | time: 1.23\n", "[info] Epoch:   3 | train loss: -18.16 | time: 1.23\n", "[info] Epoch:   4 | train loss: -18.58 | time: 1.23\n", "[info] Epoch:   5 | train loss: -18.98 | time: 1.24\n", "[info] evaluate task 5 takes 68.5 seconds\n", "[info] Epoch:   5 | succ: 0.60 ± 0.43 | best succ: 0.6 | succ. AoC 0.30 | time: 1.14\n", "[info] Epoch:   6 | train loss: -19.26 | time: 1.21\n", "[info] Epoch:   7 | train loss: -19.62 | time: 1.22\n", "[info] Epoch:   8 | train loss: -19.95 | time: 1.24\n", "[info] Epoch:   9 | train loss: -20.17 | time: 1.24\n", "[info] Epoch:  10 | train loss: -20.52 | time: 1.23\n", "[info] evaluate task 5 takes 46.0 seconds\n", "[info] Epoch:  10 | succ: 1.00 ± 0.00 | best succ: 1.0 | succ. AoC 0.53 | time: 0.77\n", "[info] Epoch:  11 | train loss: -20.73 | time: 1.23\n", "[info] Epoch:  12 | train loss: -20.98 | time: 1.23\n", "[info] Epoch:  13 | train loss: -21.28 | time: 1.23\n", "[info] Epoch:  14 | train loss: -21.50 | time: 1.23\n", "[info] Epoch:  15 | train loss: -21.73 | time: 1.23\n", "[info] evaluate task 5 takes 58.5 seconds\n", "[info] Epoch:  15 | succ: 0.80 ± 0.35 | best succ: 1.0 | succ. AoC 0.65 | time: 0.97\n", "[info] Epoch:  16 | train loss: -21.88 | time: 1.22\n", "[info] Epoch:  17 | train loss: -22.15 | time: 1.22\n", "[info] Epoch:  18 | train loss: -22.33 | time: 1.24\n", "[info] Epoch:  19 | train loss: -22.51 | time: 1.26\n", "[info] Epoch:  20 | train loss: -22.64 | time: 1.24\n", "[info] evaluate task 5 takes 57.5 seconds\n", "[info] Epoch:  20 | succ: 0.80 ± 0.35 | best succ: 1.0 | succ. AoC 0.72 | time: 0.96\n", "[info] Epoch:  21 | train loss: -22.77 | time: 1.24\n", "[info] Epoch:  22 | train loss: -22.86 | time: 1.26\n", "[info] Epoch:  23 | train loss: -22.94 | time: 1.26\n", "[info] Epoch:  24 | train loss: -23.05 | time: 1.24\n", "[info] Epoch:  25 | train loss: -23.09 | time: 1.27\n", "[info] evaluate task 5 takes 62.0 seconds\n", "[info] Epoch:  25 | succ: 0.80 ± 0.35 | best succ: 1.0 | succ. AoC 0.77 | time: 1.03\n", "[info] evaluate task 0 takes 105.9 seconds\n", "[info] evaluate task 1 takes 109.7 seconds\n", "[info] evaluate task 2 takes 105.9 seconds\n", "[info] evaluate task 3 takes 33.0 seconds\n", "[info] evaluate task 4 takes 104.3 seconds\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 60%|██████    | 6/10 [4:24:02<3:04:06, 2761.53s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[info] evaluate task 5 takes 53.1 seconds\n", "[info] Epoch:   0 | train loss: 10.09 | time: 0.57\n", "[info] evaluate task 6 takes 107.1 seconds\n", "[info] Epoch:   0 | succ: 0.00 ± 0.00 | best succ: 0.0 | succ. AoC 0.00 | time: 1.79\n", "[info] Epoch:   1 | train loss: -16.66 | time: 1.22\n", "[info] Epoch:   2 | train loss: -17.90 | time: 1.22\n", "[info] Epoch:   3 | train loss: -18.42 | time: 1.23\n", "[info] Epoch:   4 | train loss: -18.89 | time: 1.23\n", "[info] Epoch:   5 | train loss: -19.28 | time: 1.23\n", "[info] evaluate task 6 takes 89.7 seconds\n", "[info] Epoch:   5 | succ: 0.20 ± 0.35 | best succ: 0.2 | succ. AoC 0.10 | time: 1.50\n", "[info] Epoch:   6 | train loss: -19.69 | time: 1.22\n", "[info] Epoch:   7 | train loss: -20.03 | time: 1.23\n", "[info] Epoch:   8 | train loss: -20.33 | time: 1.23\n", "[info] Epoch:   9 | train loss: -20.61 | time: 1.23\n", "[info] Epoch:  10 | train loss: -20.87 | time: 1.23\n", "[info] evaluate task 6 takes 102.4 seconds\n", "[info] Epoch:  10 | succ: 0.00 ± 0.00 | best succ: 0.2 | succ. AoC 0.13 | time: 1.71\n", "[info] Epoch:  11 | train loss: -21.17 | time: 1.22\n", "[info] Epoch:  12 | train loss: -21.49 | time: 1.23\n", "[info] Epoch:  13 | train loss: -21.69 | time: 1.23\n", "[info] Epoch:  14 | train loss: -21.90 | time: 1.23\n", "[info] Epoch:  15 | train loss: -22.16 | time: 1.23\n", "[info] evaluate task 6 takes 65.0 seconds\n", "[info] Epoch:  15 | succ: 0.60 ± 0.43 | best succ: 0.6 | succ. AoC 0.20 | time: 1.08\n", "[info] Epoch:  16 | train loss: -22.37 | time: 1.22\n", "[info] Epoch:  17 | train loss: -22.60 | time: 1.23\n", "[info] Epoch:  18 | train loss: -22.71 | time: 1.23\n", "[info] Epoch:  19 | train loss: -22.94 | time: 1.24\n", "[info] Epoch:  20 | train loss: -23.07 | time: 1.23\n", "[info] evaluate task 6 takes 66.9 seconds\n", "[info] Epoch:  20 | succ: 0.60 ± 0.43 | best succ: 0.6 | succ. AoC 0.28 | time: 1.11\n", "[info] Epoch:  21 | train loss: -23.18 | time: 1.22\n", "[info] Epoch:  22 | train loss: -23.30 | time: 1.23\n", "[info] Epoch:  23 | train loss: -23.40 | time: 1.23\n", "[info] Epoch:  24 | train loss: -23.47 | time: 1.23\n", "[info] Epoch:  25 | train loss: -23.50 | time: 1.23\n", "[info] evaluate task 6 takes 78.0 seconds\n", "[info] Epoch:  25 | succ: 0.40 ± 0.43 | best succ: 0.6 | succ. AoC 0.33 | time: 1.30\n", "[info] evaluate task 0 takes 105.5 seconds\n", "[info] evaluate task 1 takes 106.6 seconds\n", "[info] evaluate task 2 takes 104.8 seconds\n", "[info] evaluate task 3 takes 101.1 seconds\n", "[info] evaluate task 4 takes 92.5 seconds\n", "[info] evaluate task 5 takes 101.2 seconds\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 70%|███████   | 7/10 [5:17:38<2:25:30, 2910.15s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[info] evaluate task 6 takes 76.7 seconds\n", "[info] Epoch:   0 | train loss: 11.02 | time: 0.53\n", "[info] evaluate task 7 takes 103.9 seconds\n", "[info] Epoch:   0 | succ: 0.00 ± 0.00 | best succ: 0.0 | succ. AoC 0.00 | time: 1.73\n", "[info] Epoch:   1 | train loss: -16.80 | time: 1.12\n", "[info] Epoch:   2 | train loss: -18.12 | time: 1.13\n", "[info] Epoch:   3 | train loss: -18.80 | time: 1.13\n", "[info] Epoch:   4 | train loss: -19.26 | time: 1.13\n", "[info] Epoch:   5 | train loss: -19.79 | time: 1.14\n", "[info] evaluate task 7 takes 48.8 seconds\n", "[info] Epoch:   5 | succ: 0.80 ± 0.35 | best succ: 0.8 | succ. AoC 0.40 | time: 0.81\n", "[info] Epoch:   6 | train loss: -20.14 | time: 1.13\n", "[info] Epoch:   7 | train loss: -20.53 | time: 1.14\n", "[info] Epoch:   8 | train loss: -20.87 | time: 1.14\n", "[info] Epoch:   9 | train loss: -21.19 | time: 1.14\n", "[info] Epoch:  10 | train loss: -21.49 | time: 1.14\n", "[info] evaluate task 7 takes 63.7 seconds\n", "[info] Epoch:  10 | succ: 0.60 ± 0.43 | best succ: 0.8 | succ. AoC 0.53 | time: 1.06\n", "[info] Epoch:  11 | train loss: -21.81 | time: 1.13\n", "[info] Epoch:  12 | train loss: -22.06 | time: 1.14\n", "[info] Epoch:  13 | train loss: -22.29 | time: 1.14\n", "[info] Epoch:  14 | train loss: -22.51 | time: 1.14\n", "[info] Epoch:  15 | train loss: -22.77 | time: 1.14\n", "[info] evaluate task 7 takes 89.2 seconds\n", "[info] Epoch:  15 | succ: 0.20 ± 0.35 | best succ: 0.8 | succ. AoC 0.60 | time: 1.49\n", "[info] Epoch:  16 | train loss: -22.97 | time: 1.13\n", "[info] Epoch:  17 | train loss: -23.15 | time: 1.14\n", "[info] Epoch:  18 | train loss: -23.35 | time: 1.14\n", "[info] Epoch:  19 | train loss: -23.47 | time: 1.14\n", "[info] Epoch:  20 | train loss: -23.67 | time: 1.14\n", "[info] evaluate task 7 takes 76.1 seconds\n", "[info] Epoch:  20 | succ: 0.40 ± 0.43 | best succ: 0.8 | succ. AoC 0.64 | time: 1.27\n", "[info] Epoch:  21 | train loss: -23.76 | time: 1.15\n", "[info] Epoch:  22 | train loss: -23.88 | time: 1.22\n", "[info] Epoch:  23 | train loss: -23.97 | time: 1.23\n", "[info] Epoch:  24 | train loss: -24.06 | time: 1.22\n", "[info] Epoch:  25 | train loss: -24.11 | time: 1.22\n", "[info] evaluate task 7 takes 85.6 seconds\n", "[info] Epoch:  25 | succ: 0.40 ± 0.43 | best succ: 0.8 | succ. AoC 0.67 | time: 1.43\n", "[info] evaluate task 0 takes 83.1 seconds\n", "[info] evaluate task 1 takes 109.1 seconds\n", "[info] evaluate task 2 takes 108.1 seconds\n", "[info] evaluate task 3 takes 108.5 seconds\n", "[info] evaluate task 4 takes 68.3 seconds\n", "[info] evaluate task 5 takes 109.7 seconds\n", "[info] evaluate task 6 takes 96.3 seconds\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 80%|████████  | 8/10 [6:10:06<1:39:31, 2985.96s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[info] evaluate task 7 takes 65.3 seconds\n", "[info] Epoch:   0 | train loss: -7.76 | time: 0.62\n", "[info] evaluate task 8 takes 110.4 seconds\n", "[info] Epoch:   0 | succ: 0.00 ± 0.00 | best succ: 0.0 | succ. AoC 0.00 | time: 1.84\n", "[info] Epoch:   1 | train loss: -17.95 | time: 1.32\n", "[info] Epoch:   2 | train loss: -18.97 | time: 1.33\n", "[info] Epoch:   3 | train loss: -19.41 | time: 1.34\n", "[info] Epoch:   4 | train loss: -19.73 | time: 1.35\n", "[info] Epoch:   5 | train loss: -20.18 | time: 1.33\n", "[info] evaluate task 8 takes 68.3 seconds\n", "[info] Epoch:   5 | succ: 0.60 ± 0.43 | best succ: 0.6 | succ. AoC 0.30 | time: 1.14\n", "[info] Epoch:   6 | train loss: -20.51 | time: 1.33\n", "[info] Epoch:   7 | train loss: -20.74 | time: 1.35\n", "[info] Epoch:   8 | train loss: -21.01 | time: 1.36\n", "[info] Epoch:   9 | train loss: -21.28 | time: 1.34\n", "[info] Epoch:  10 | train loss: -21.57 | time: 1.34\n", "[info] evaluate task 8 takes 51.7 seconds\n", "[info] Epoch:  10 | succ: 0.80 ± 0.35 | best succ: 0.8 | succ. AoC 0.47 | time: 0.86\n", "[info] Epoch:  11 | train loss: -21.80 | time: 1.32\n", "[info] Epoch:  12 | train loss: -22.06 | time: 1.33\n", "[info] Epoch:  13 | train loss: -22.23 | time: 1.33\n", "[info] Epoch:  14 | train loss: -22.50 | time: 1.33\n", "[info] Epoch:  15 | train loss: -22.82 | time: 1.34\n", "[info] evaluate task 8 takes 41.1 seconds\n", "[info] Epoch:  15 | succ: 1.00 ± 0.00 | best succ: 1.0 | succ. AoC 0.60 | time: 0.69\n", "[info] Epoch:  16 | train loss: -22.89 | time: 1.38\n", "[info] Epoch:  17 | train loss: -23.16 | time: 1.40\n", "[info] Epoch:  18 | train loss: -23.36 | time: 1.40\n", "[info] Epoch:  19 | train loss: -23.47 | time: 1.36\n", "[info] Epoch:  20 | train loss: -23.65 | time: 1.35\n", "[info] evaluate task 8 takes 39.6 seconds\n", "[info] Epoch:  20 | succ: 1.00 ± 0.00 | best succ: 1.0 | succ. AoC 0.68 | time: 0.66\n", "[info] Epoch:  21 | train loss: -23.77 | time: 1.34\n", "[info] Epoch:  22 | train loss: -23.90 | time: 1.38\n", "[info] Epoch:  23 | train loss: -23.99 | time: 1.38\n", "[info] Epoch:  24 | train loss: -24.10 | time: 1.37\n", "[info] Epoch:  25 | train loss: -24.17 | time: 1.34\n", "[info] evaluate task 8 takes 54.1 seconds\n", "[info] Epoch:  25 | succ: 0.80 ± 0.35 | best succ: 1.0 | succ. AoC 0.73 | time: 0.90\n", "[info] evaluate task 0 takes 106.3 seconds\n", "[info] evaluate task 1 takes 105.2 seconds\n", "[info] evaluate task 2 takes 104.6 seconds\n", "[info] evaluate task 3 takes 104.8 seconds\n", "[info] evaluate task 4 takes 103.5 seconds\n", "[info] evaluate task 5 takes 104.0 seconds\n", "[info] evaluate task 6 takes 78.3 seconds\n", "[info] evaluate task 7 takes 104.0 seconds\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 90%|█████████ | 9/10 [7:08:19<52:24, 3144.50s/it]  "]}, {"name": "stdout", "output_type": "stream", "text": ["[info] evaluate task 8 takes 55.9 seconds\n", "[info] Epoch:   0 | train loss: 49.85 | time: 0.54\n", "[info] evaluate task 9 takes 105.7 seconds\n", "[info] Epoch:   0 | succ: 0.00 ± 0.00 | best succ: 0.0 | succ. AoC 0.00 | time: 1.76\n", "[info] Epoch:   1 | train loss: -17.30 | time: 1.08\n", "[info] Epoch:   2 | train loss: -18.84 | time: 1.08\n", "[info] Epoch:   3 | train loss: -19.23 | time: 1.09\n", "[info] Epoch:   4 | train loss: -19.55 | time: 1.09\n", "[info] Epoch:   5 | train loss: -19.99 | time: 1.10\n", "[info] evaluate task 9 takes 34.9 seconds\n", "[info] Epoch:   5 | succ: 1.00 ± 0.00 | best succ: 1.0 | succ. AoC 0.50 | time: 0.58\n", "[info] Epoch:   6 | train loss: -20.42 | time: 1.10\n", "[info] Epoch:   7 | train loss: -20.64 | time: 1.10\n", "[info] Epoch:   8 | train loss: -21.00 | time: 1.09\n", "[info] Epoch:   9 | train loss: -21.22 | time: 1.09\n", "[info] Epoch:  10 | train loss: -21.49 | time: 1.09\n", "[info] evaluate task 9 takes 65.4 seconds\n", "[info] Epoch:  10 | succ: 0.60 ± 0.43 | best succ: 1.0 | succ. AoC 0.67 | time: 1.09\n", "[info] Epoch:  11 | train loss: -21.96 | time: 1.09\n", "[info] Epoch:  12 | train loss: -22.12 | time: 1.09\n", "[info] Epoch:  13 | train loss: -22.42 | time: 1.09\n", "[info] Epoch:  14 | train loss: -22.65 | time: 1.09\n", "[info] Epoch:  15 | train loss: -22.94 | time: 1.10\n", "[info] evaluate task 9 takes 34.6 seconds\n", "[info] Epoch:  15 | succ: 1.00 ± 0.00 | best succ: 1.0 | succ. AoC 0.75 | time: 0.58\n", "[info] Epoch:  16 | train loss: -23.13 | time: 1.09\n", "[info] Epoch:  17 | train loss: -23.37 | time: 1.09\n", "[info] Epoch:  18 | train loss: -23.52 | time: 1.09\n", "[info] Epoch:  19 | train loss: -23.68 | time: 1.09\n", "[info] Epoch:  20 | train loss: -23.85 | time: 1.09\n", "[info] evaluate task 9 takes 35.8 seconds\n", "[info] Epoch:  20 | succ: 1.00 ± 0.00 | best succ: 1.0 | succ. AoC 0.80 | time: 0.60\n", "[info] Epoch:  21 | train loss: -23.98 | time: 1.09\n", "[info] Epoch:  22 | train loss: -24.09 | time: 1.09\n", "[info] Epoch:  23 | train loss: -24.21 | time: 1.09\n", "[info] Epoch:  24 | train loss: -24.32 | time: 1.10\n", "[info] Epoch:  25 | train loss: -24.40 | time: 1.09\n", "[info] evaluate task 9 takes 49.9 seconds\n", "[info] Epoch:  25 | succ: 0.80 ± 0.35 | best succ: 1.0 | succ. AoC 0.83 | time: 0.83\n", "[info] evaluate task 0 takes 110.0 seconds\n", "[info] evaluate task 1 takes 110.4 seconds\n", "[info] evaluate task 2 takes 72.3 seconds\n", "[info] evaluate task 3 takes 93.2 seconds\n", "[info] evaluate task 4 takes 106.4 seconds\n", "[info] evaluate task 5 takes 103.6 seconds\n", "[info] evaluate task 6 takes 106.1 seconds\n", "[info] evaluate task 7 takes 104.8 seconds\n", "[info] evaluate task 8 takes 104.3 seconds\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 10/10 [8:00:57<00:00, 2885.72s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["[info] evaluate task 9 takes 33.5 seconds\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["cfg.policy.policy_type = \"MyTransformerPolicy\"\n", "cfg.lifelong.algo = \"MyLifelongAlgo\"\n", "\n", "create_experiment_dir(cfg)\n", "cfg.shape_meta = shape_meta\n", "\n", "import numpy as np\n", "from tqdm import trange\n", "from libero.lifelong.metric import evaluate_loss, evaluate_success\n", "\n", "print(\"experiment directory is: \", cfg.experiment_dir)\n", "algo = safe_device(MyLifelongAlgo(n_tasks, cfg), cfg.device)\n", "\n", "result_summary = {\n", "    'L_conf_mat': np.zeros((n_tasks, n_tasks)),   # loss confusion matrix\n", "    'S_conf_mat': np.zeros((n_tasks, n_tasks)),   # success confusion matrix\n", "    'L_fwd'     : np.zeros((n_tasks,)),           # loss AUC, how fast the agent learns\n", "    'S_fwd'     : np.zeros((n_tasks,)),           # success AUC, how fast the agent succeeds\n", "}\n", "\n", "gsz = cfg.data.task_group_size\n", "\n", "if (cfg.train.n_epochs < 50):\n", "    print(\"NOTE: the number of epochs used in this example is intentionally reduced to 30 for simplicity.\")\n", "if (cfg.eval.n_eval < 20):\n", "    print(\"NOTE: the number of evaluation episodes used in this example is intentionally reduced to 5 for simplicity.\")\n", "\n", "for i in trange(n_tasks):\n", "    algo.train()\n", "    s_fwd, l_fwd = algo.learn_one_task(datasets[i], i, benchmark, result_summary)\n", "    # s_fwd is success rate AUC, when the agent learns the {0, e, 2e, ...} epochs\n", "    # l_fwd is BC loss AUC, similar to s_fwd\n", "    result_summary[\"S_fwd\"][i] = s_fwd\n", "    result_summary[\"L_fwd\"][i] = l_fwd\n", "\n", "    if cfg.eval.eval:\n", "        algo.eval()\n", "        # we only evaluate on the past tasks: 0 .. i\n", "        L = evaluate_loss(cfg, algo, benchmark, datasets[:i+1]) # (i+1,)\n", "        S = evaluate_success(cfg, algo, benchmark, list(range((i+1)*gsz))) # (i+1,)\n", "        result_summary[\"L_conf_mat\"][i][:i+1] = L\n", "        result_summary[\"S_conf_mat\"][i][:i+1] = S\n", "\n", "        torch.save(result_summary, os.path.join(cfg.experiment_dir, f'result.pt'))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 5. Visualize the results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 5.1 Load results"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[0.6 0.  0.  0.  0.  0.  0.  0.  0.  0. ]\n", " [0.  0.8 0.  0.  0.  0.  0.  0.  0.  0. ]\n", " [0.  0.  0.8 0.  0.  0.  0.  0.  0.  0. ]\n", " [0.  0.  0.2 0.6 0.  0.  0.  0.  0.  0. ]\n", " [0.  0.  0.  0.  0.8 0.  0.  0.  0.  0. ]\n", " [0.  0.  0.  1.  0.  0.8 0.  0.  0.  0. ]\n", " [0.  0.  0.  0.  0.2 0.  0.4 0.  0.  0. ]\n", " [0.4 0.  0.  0.  0.8 0.  0.2 0.6 0.  0. ]\n", " [0.  0.  0.  0.  0.  0.  0.4 0.  0.8 0. ]\n", " [0.  0.  0.8 0.2 0.  0.  0.  0.  0.  1. ]]\n", "[0.5        0.63333333 0.66666667 0.33333333 0.56666667 0.76666667\n", " 0.33333333 0.66666667 0.73333333 0.83333333]\n"]}], "source": ["result_summary = torch.load(os.path.join(cfg.experiment_dir, f'result.pt'))\n", "print(result_summary[\"S_conf_mat\"])\n", "print(result_summary[\"S_fwd\"])"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### 5.2 Compute FWT, BWT, and AUC of the experiments"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["import torch\n", "import numpy as np\n", "from pathlib import Path\n", "\n", "benchmark_map = {\n", "    \"libero_10\"     : \"LIBERO_10\",\n", "    \"libero_90\"     : \"LIBERO_90\",\n", "    \"libero_spatial\": \"LIBERO_SPATIAL\",\n", "    \"libero_object\" : \"LIBERO_OBJECT\",\n", "    \"libero_goal\"   : \"LIBERO_GOAL\",\n", "}\n", "\n", "algo_map = {\n", "    \"base\"     : \"Sequential\",\n", "    \"er\"       : \"ER\",\n", "    \"ewc\"      : \"EWC\",\n", "    \"packnet\"  : \"PackNet\",\n", "    \"multitask\": \"Multitask\",\n", "    \"custom_algo\"   : \"MyLifelongAlgo\",\n", "}\n", "\n", "policy_map = {\n", "    \"bc_rnn_policy\"        : \"BCRNNPolicy\",\n", "    \"bc_transformer_policy\": \"BCTransformerPolicy\",\n", "    \"bc_vilt_policy\"       : \"BCViLTPolicy\",\n", "    \"custom_policy\"        : \"MyTransformerPolicy\",\n", "}\n", "\n", "seeds = [10000]\n", "N_SEEDS = len(seeds)\n", "N_TASKS = 10\n", "\n", "def get_auc(experiment_dir, bench, algo, policy):\n", "    N_EP = cfg.train.n_epochs // cfg.eval.eval_every + 1\n", "    fwds = np.zeros((N_TASKS, N_EP, N_SEEDS))\n", "\n", "    for task in range(N_TASKS):\n", "        counter = 0\n", "        for k, seed in enumerate(seeds):\n", "            name = f\"{experiment_dir}/task{task}_auc.log\"\n", "            try:\n", "                succ = torch.load(name)[\"success\"] # (n_epochs)\n", "                idx = succ.argmax()\n", "                succ[idx:] = succ[idx]\n", "                fwds[task, :, k] = succ\n", "            except:\n", "                print(\"Some errors when loading results\")\n", "                continue\n", "    return fwds\n", "\n", "def compute_metric(res):\n", "    mat, fwts  = res # fwds: (num_tasks, num_save_intervals, num_seeds)\n", "    num_tasks, num_seeds = mat.shape[1:]\n", "    ret = {}\n", "\n", "    # compute fwt\n", "    fwt = fwts.mean(axis=(0,1))\n", "    ret[\"fwt\"] = fwt\n", "    # compute bwt\n", "    bwts = []\n", "    aucs = []\n", "    for seed in range(num_seeds):\n", "        bwt = 0.0\n", "        auc = 0.0\n", "        for k in range(num_tasks):\n", "            bwt_k = 0.0\n", "            auc_k = 0.0\n", "            for tau in range(k+1, num_tasks):\n", "                bwt_k += mat[k,k,seed] - mat[tau,k,seed]\n", "                auc_k += mat[tau,k,seed]\n", "            if k + 1 < num_tasks:\n", "                bwt_k /= (num_tasks - k - 1)\n", "            auc_k = (auc_k + fwts[k,:,seed].mean()) / (num_tasks - k)\n", "\n", "            bwt += bwt_k\n", "            auc += auc_k\n", "        bwts.append(bwt / num_tasks)\n", "        aucs.append(auc / num_tasks)\n", "    bwts = np.array(bwts)\n", "    aucs = np.array(aucs)\n", "    ret[\"bwt\"] = bwts\n", "    ret[\"auc\"] = aucs\n", "    return ret"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'fwt': array([0.60333333]), 'bwt': array([0.54126984]), 'auc': array([0.26577513])}\n"]}], "source": ["experiment_dir = \"experiments\"\n", "benchmark_name = \"libero_object\"\n", "algo_name = \"custom_algo\"\n", "policy_name = \"custom_policy\"\n", "\n", "fwds = get_auc(cfg.experiment_dir, benchmark_name, algo_name, policy_name)\n", "\n", "conf_mat = result_summary[\"S_conf_mat\"][..., np.newaxis]\n", "\n", "metric = compute_metric((conf_mat, fwds))\n", "print(metric)\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### 5.3 Visualize policy rollouts\n", "\n", " This is an example of how to use the trained model to do inference. We will take the policy from training on the first task as an example. More concrete example, please see `evaluate_one_task_success` in the file `lifelong/lifelong/metric.py`."]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/html": ["<video controls alt=\"test\" src=\"data:video/mp4;base64,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\">"], "text/plain": ["<IPython.core.display.HTML object>"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["from IPython.display import HTML\n", "from base64 import b64encode\n", "import imageio\n", "\n", "from libero.libero.envs import OffScreenRenderEnv, DummyVectorEnv\n", "from libero.lifelong.metric import raw_obs_to_tensor_obs\n", "\n", "# You can turn on subprocess\n", "env_num = 1\n", "action_dim = 7\n", "\n", "\n", "# If it's packnet, the weights need to be processed first\n", "task_id = 9\n", "task = benchmark.get_task(task_id)\n", "task_emb = benchmark.get_task_emb(task_id)\n", "\n", "if cfg.lifelong.algo == \"PackNet\":\n", "    algo = algo.get_eval_algo(task_id)\n", "\n", "algo.eval()\n", "env_args = {\n", "    \"bddl_file_name\": os.path.join(\n", "        cfg.bddl_folder, task.problem_folder, task.bddl_file\n", "    ),\n", "    \"camera_heights\": cfg.data.img_h,\n", "    \"camera_widths\": cfg.data.img_w,\n", "}\n", "\n", "env = DummyVectorEnv(\n", "            [lambda: OffScreenRenderEnv(**env_args) for _ in range(env_num)]\n", ")\n", "\n", "init_states_path = os.path.join(\n", "    cfg.init_states_folder, task.problem_folder, task.init_states_file\n", ")\n", "init_states = torch.load(init_states_path)\n", "\n", "env.reset()\n", "\n", "init_state = init_states[0:1]\n", "dones = [False]\n", "\n", "algo.reset()\n", "\n", "obs = env.set_init_state(init_state)\n", "\n", "\n", "# Make sure the gripepr is open to make it consistent with the provided demos.\n", "dummy_actions = np.zeros((env_num, action_dim))\n", "for _ in range(5):\n", "    obs, _, _, _ = env.step(dummy_actions)\n", "\n", "steps = 0\n", "\n", "obs_tensors = [[]] * env_num\n", "while steps < cfg.eval.max_steps:\n", "    steps += 1\n", "    data = raw_obs_to_tensor_obs(obs, task_emb, cfg)\n", "    action = algo.policy.get_action(data)\n", "\n", "    obs, reward, done, info = env.step(action)\n", "\n", "    for k in range(env_num):\n", "        dones[k] = dones[k] or done[k]\n", "        obs_tensors[k].append(obs[k][\"agentview_image\"])\n", "    if all(dones):\n", "        break\n", "\n", "# visualize video\n", "# obs_tensor: (env_num, T, H, W, C)\n", "\n", "images = [img[::-1] for img in obs_tensors[0]]\n", "fps = 30\n", "writer  = imageio.get_writer('tmp_video.mp4', fps=fps)\n", "for image in images:\n", "    writer.append_data(image)\n", "writer.close()\n", "\n", "video_data = open(\"tmp_video.mp4\", \"rb\").read()\n", "video_tag = f'<video controls alt=\"test\" src=\"data:video/mp4;base64,{b64encode(video_data).decode()}\">'\n", "HTML(data=video_tag)\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 2}