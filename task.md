| **方法** | **作用对象与关注点** | **计算复杂度与关键步骤** | **使用过程与模型范围（泛化性）** | **怎么做到的？** | **作用对象** | **前端展示** | **什么时候用** | **与输出确定性关联** | **是否需梯度 / GT** | **跨层与残差** | **必要性前提** | **经典论文/实例** |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| **注意力图可视化 (Raw Attention Maps)** | 作用于**内部注意力矩阵**（例如自注意力或交叉注意力层）。用于高亮模型关注的输入元素（图像patch或文本token），例如通过注意力分数显示哪些图像区域与具体单词对应。 | **时间：** 除了前向推理外几乎无额外开销（只需提取注意力权重）。**空间：** 需要存储注意力矩阵（每层为 O(N²)，N 为 token 数），可管理。**步骤：** 正常前向，提取注意力权重，按头/层汇总或投影到图像空间生成热图。 | 适用于所有可访问注意力权重的 transformer 型 VLM（如 ViT、带 cross-attention 的多模态 transformer）。不需要梯度。直观但不一定反映因果重要性。 | 直接读取Transformer某层某头的注意力权重矩阵，将其可视化。例如固定一个查询token/词，查看在该层它对其他键（图像patch或文本token）的注意力分布。这样高权重位置即被高亮，表示模型在该层“关注”了那些部分。 | **Transformer的注意力权重**。可选择**视觉编码器**自注意力（ViT的patch对patch）或**跨模态交叉注意**（文本->图像）乃至**LLM自身的自注意**，具体取决于想看哪个模块的注意力分布。一般在多模态VLM中，常看**文本对图像的交叉注意**来对齐词与视觉区域。 | 前端可提供**交互式热力图**：例如用户点击或选择一句问话中的某个词，界面在图像上用半透明的颜色热力图高亮该词在模型注意力中对应权重最高的图像区域。对于文本内部注意力，可用不同深浅的底色高亮其他词语，或用弧线连接表示注意力权重大小。这种可视化应与原输入并置，方便用户对照理解。 | 当希望**直观了解模型关注了哪些位置**时使用。例如检查视觉问答模型中文本某词对应的图像区域，或ViT中哪些图像区域彼此有注意力连接。这种可视化能快速发现模型注意力热点，判断模型是否“看对了地方”，但不保证这些热点对输出有决定性作用。 | **弱关联**。只是展示模型内部某一层的注意分布，并未直接考虑最终输出，因此和要解释的具体输出没有直接因果联系。这往往仅反映该层的信息流动倾向，而非该部分对最终决策的重要性（正如争论所示“Attention is not Explanation”）。 | **不需要梯度**，也不需要标签/真值。仅利用模型前向计算生成的注意力权重即可，无需任何反向传播。 | **单层**。通常针对单个注意力层或单个注意力头进行可视化，不涉及跨层累积。残差连接的影响未明确体现（因为注意力矩阵通常已在残差分支之内归一化处理），可将其视为每层独立的注意力图观察。 | 模型需采用**Transformer架构**且提供注意力权重（大多数Transformer都有）。无额外特殊要求，任何有注意力机制的VLM都可直接提取内部注意力进行可视化。 | Transformer注意力机制：Vaswani 等 2017；**BERTViz** 等工具（Vig 2019）用于可视化Transformer注意力。注意力非解释之讨论：Jain & Wallace 2019。 |
| **注意力逐层传播 (Attention Rollout/Flow)** | 作用于**模型的注意力层级**，通过层间累积计算输入到输出的信息流。把各层注意力链起来，找出输入 patch/token 最终如何影响输出。 | **时间：** O(L·N²)（L 层，每层 N×N 注意力）；若对单一输出 token 限定，计算量可降。**空间：** O(N²)（或若仅保留每个输出向量则 O(N)）。**步骤：** 逐层或乘积方式组合注意力矩阵（考虑残差/跳接），得到从输入到输出的整体注意力影响矩阵。 | 仅适用于具有堆叠注意力结构的 transformer（如 ViT、跨模态 transformer）。需访问全部注意力权重。比单层注意力更能追踪深层流动。 | 将各层的注意力矩阵逐层**递乘/传播**，计算输入各部分对输出token的总体注意力。具体做法是：每层的注意力权重矩阵加上恒等$I$后，与前一层累积矩阵相乘，最终得到输入->输出的综合注意力分数。这样实现信息沿多层注意力图的流动 (Abnar和Zuidema提出Attention Rollout用于累积Transformer的信息流)。Attention Flow则是在Rollout基础上更精确地建模注意力传递（例如用最大流等方法）以追踪每个输入到输出的贡献。 | **Transformer各层的注意力**。典型用于**视觉Transformer**的自注意力层（如ViT的每层patch注意力），也可用于多模态Transformer的**交叉注意**（将多层文本->图像注意力矩阵相乘）。通常针对**Encoder部分**逐层累积，但Encoder-Decoder模型也可对Encoder自注意或Decoder交叉注意分别做rollout。 | 前端展示与单层注意力类似，但结果是**输入整体的重要性热力图**。对于图像，可直接显示一个累积注意力热力图叠加在图像上，颜色强度表示该区域对最终输出的综合关注度。对于文本，也可给出每个词的一个总体权重，高亮词语背景深浅表示其在最终回答中的作用大小。 | 在希望**估计输入总体的重要性**且模型有多层注意力时使用。比如ViT图像分类，可用CLS token的Attention Rollout热力图展示**整张图上哪些区域最终影响分类**。比单层注意力更全面，适用于分析深层模型的信息流，比如判断模型最终决策主要来自图像哪个部分。 | **中等关联**。相对单层注意力，逐层传播后得到的热力图更接近最终输出的关注点，因为它考虑了深层传递影响。但依然**不直接使用输出信号**，属于基于结构的启发式，因此热点与最终决策的因果关联仍不严谨（可能包含噪声或冗余注意）。 | **不需要梯度**，只需各层注意力矩阵。也不需要提供任何标签，只基于模型自身的注意力权重计算。 | **多层**。涉及将**多层**Transformer的注意力权重相乘累积。通过跨层传播来体现深层的信息传递：例如在ViT中，将浅层patch注意力逐层传到最终CLS token，可得到每个patch对CLS的综合注意力。残差连接通过在每层加入恒等矩阵$I$来处理（保证每层输入自身有一路直接传递），这样在传播时隐含考虑了残差路径的影响。 | 模型需要**多层堆叠的注意力机制**（Transformer结构）。需能提取每一层的注意力权重矩阵。不适用于没有多层注意力的网络。除此之外无特殊要求。 | **Attention Rollout**: Abnar & Zuidema (ACL 2020)提出；Attention Flow 同源论文提出更精细流量分析。应用于ViT可解释：Dosovitskiy 2020 ViT论文附带Rollout可视化。学术实现如🛠️*vit-explain*库。 |
| **通用Transformer解释 (Chefer et al.)** | 作用于**transformer 内部（所有层）**，包括自注意力、共注意力与 encoder-decoder attention。通过注意力与激活传播“相关性”，把输出的“相关度”分配回输入 token/图像区域，得到输入级别的重要性分数。 | **时间：** 类似一次定制的反向传播（O(L·P)，P 为参数量），在注意力上应用特殊反向规则。**空间：** 需在各层存储相关性信息（O(L·N)）。**步骤：** 从输出层初始化相关性（例如对预测答案设 1），按注意力与神经元贡献比例迭代向后分配相关性，直至输入，产出输入归因。 | 对所有基于 transformer 的 VLM 通用（自注意力、跨模态共注意力等）。需要白盒访问（模型权重与注意力张量）。不需重训练，通常比原始注意力可视化更“可信”。 | 通过**自定义反向传播**将输出相关性分配回输入。具体而言，方法将模型预测的“重要性”从输出层向后传递：在注意力层，根据注意力权重将当前层输出的相关性划分并分配给各输入token；在FFN层，根据梯度和权重分配给输入Neuron；处理残差时相关性沿多个分支按比例传递，确保总和守恒。如此逐层回推，最后得到每个**输入图像patch和文本token**的得分，即对该输出的贡献程度。这种方法结合了LRP和梯度，专门针对Transformer结构设计，从而生成比直接注意力更准确的可视化解释。 | **Transformer网络的所有模块**：包括**自注意力层**、**交叉注意力层**（如果有）以及**FFN层**。在多模态模型中，它会同时产出图像patch和文本token的归因分值，是全局性的解释。因此作用对象涵盖**视觉编码器输出**和**语言序列输入**等所有输入单元。 | 前端可以同时展示**图像热力图+文本高亮**。将计算得到的图像patch归因分数在图像上以热力图显示，高亮颜色表示该区域对输出的重要性。对文本输入，将单词按贡献大小以背景色深浅标注。用户可一目了然地看到模型输出由哪些视觉证据和文本依据所支撑。这种双模态可视化有助于验证模型是否把多模态信息正确结合。 | 当需要**高保真度的输入归因**时使用。适用于Transformer架构的任意任务，包括视觉Transformer分类、VQA、图文检索等。它能指出**哪部分图像和文字最影响模型给出的答案**，相比注意力图更能真实反映模型内部决策依据。在需要解释可信度（如学术或医疗应用）时，此方法因考虑整个网络更具说服力。 | **强关联**。该方法专门针对模型**特定输出**进行归因，从最终输出分数出发逐层分配相关性，因此得到的输入重要性直接对应于对那个输出的贡献大小。相比纯注意力可视化更具因果含义。 | **需要**定制的反向传播计算，但不直接使用标准梯度。无需提供外部真值标注，只需选择需要解释的输出（通常用模型预测的输出自身）。通过模型**参数和激活**进行一次特殊的反向分配，无需重新训练模型。 | **多层**。该方法遍历**模型所有层**（包括多头注意力和FFN子层）进行相关性传播。每层都应用特定规则：例如注意力层按注意力矩阵将上一层的相关性分给各输入token；残差连接则将相关性分摊到主支和残差支。这样确保相关性在各层间守恒传递。通过跨层回传，可完整反映输出是如何由输入经层层计算得到的。 | **Chefer等 (ICCV 2021)**“跨模态Transformer解释”方法。相关实现公开在GitHub: *hila-chefer/Transformer-MM-Explainability*。该方法在VQA等任务上表现出较高解释质量。 |  |
| **GLIMPSE (Gradient-Layer Mapping, 2025)** | 跨模态Transformer的关键层：主要是**视觉-语言交互层**（如BLIP-2的Q-Former输出与LLM交互部分、Flamingo的跨注意力层等）。GLIMPSE关注这些层里连接图像与文本的信息。此外，语言生成层的梯度也用于衡量文本token的重要性。因此作用对象涵盖**图像输入的patch/区域**和**文本提示/问题的词**，输出为二者共同的相关热力图。 | **时间：** 需要一次或少数次反向传导（复杂度与常规梯度反向相当，O(L·P)）；只关注关键层可降低。**空间：** 存储选定层的梯度/注意力（每层 O(N) 或 O(N²) 视表示而定）。**步骤：** 前向得出输出，计算输出对中间表示/注意力的梯度；将梯度与注意力加权融合，逐层传播并汇总成视觉热图与文本重要性。 | 针对**生成性 VLM（如 BLIP-2、LLaVA、Flamingo 等）**的白盒方法。需梯度与注意力输出，适合在微调前对基础模型做事后分析，能同时产生跨模态（图像+文本）解释。 | 结合**梯度和多层注意力**产生跨模态解释的新方法。具体而言：对大型生成式VLM给出回答后，GLIMPSE计算该输出相对于模型**跨模态层**的梯度，并与相应层的注意力权重相乘得到**梯度加权注意力**映射。然后沿模型层次将这些映射作为“相关性”逐层向输入传播（带有自适应的层权重，强调对输出贡献大的层）。最后，将不同层不同模态的贡献融合成一个统一的热力图，突出最相关的图像区域和文本token。该方法相当于在注意力可视化基础上引入梯度信号进行加权和传播，生成对自由文本回答的整体解释。 | **跨模态Transformer的关键层**：主要是**视觉-语言交互层**（如BLIP-2的Q-Former输出与LLM交互部分、Flamingo的跨注意力层等）。GLIMPSE关注这些层里连接图像与文本的信息。此外，语言生成层的梯度也用于衡量文本token的重要性。因此作用对象涵盖**图像输入的patch/区域**和**文本提示/问题的词**，输出为二者共同的相关热力图。 | 前端将GLIMPSE生成的**视觉和文本重要性**同时展示：在图像上叠加颜色热力图，红色等暖色显示对回答影响大的区域；同时将问题或提示句中关键的词用高亮底色标出。这样，用户可看到模型回答所依据的视觉证据（图像哪个部分）以及相关的语言内容。如果需要，还可在图像热力图上标注对应的重要单词，以指示图文对应关系，从而更直观地理解模型的多模态推理路径。 | 适用于**大型生成式VLM**的输出解释，尤其在模型生成自由文本答案时。比如在机器人问答或对话系统中，我们希望知道模型根据图像哪部分和问题哪些词得出了回答。GLIMPSE在不修改模型的前提下，提供了**完整的跨模态可视化**，有助于诊断模型的推理过程（检测错误依据、幻觉等）。 | **很强关联**。GLIMPSE完全针对模型**生成的特定回答**进行归因：它利用输出对内部表示的梯度来衡量哪些视觉和文本特征支持了该输出。因此，高亮的区域和词直接对应支撑该回答的信息，具有明确的针对性。实验显示GLIMPSE生成的热点与人类注视更吻合，因而提高了解释的有效性。 | **需要梯度**。需要获取输出对多个层次表示/注意力的梯度。但不需要提供人工标注的真值，只需使用模型自己的输出作为目标（例如回答的对数概率）。梯度通过标准反传计算，配合注意力权重进行处理。GLIMPSE不需要重新训练模型，只是在推理后进行附加的梯度计算和融合。 | **多层**。GLIMPSE显式利用了**多个层**的信息：特别是中间若干**跨模态注意力层**（例如视觉->文本融合层）都会参与计算。它对不同层设置**自适应权重**，以平衡浅层和深层贡献，并通过相关性传播将信息从这些层汇聚到输入。残差连接等在梯度传播中天然考虑。最终它汇总了各层的结果形成单一的全局解释。 | 模型需要支持**梯度计算**且具有**图文交互机制**。通常要求模型规模较大（如视觉Transformer+LLM），但GLIMPSE是**模型无关**的解释框架：任何能取出注意力和算梯度的VLM都可用。模型不需特殊修改，但需要访问其注意力权重及输出梯度。因此对封闭API不适用，需要模型可运行在可微分平台上。 | **GLIMPSE (Shen et al. 2025)**提出，用于解释如LLaVA、BLIP-2这类模型的生成答案。在VQA-HAT数据集上的人为注视对齐度超过先前方法46%。这是首个可解释整段回答的注意-梯度融合方法。目前作为前沿研究（ICLR 2025提交）有开源实现。 |
| **Grad-CAM** | 主要作用于**视觉编码器的最后卷积层或空间特征图**（对 ViT 可用最后的 patch 表征）。突出对特定输出（分类分数或答案 logit）有影响的图像区域。 | **时间：** 一次前向 + 一次后向（O(P)），计算每个通道上的梯度均值并线性组合特征图。**空间：** 保存特征图与梯度（O(HW)）。**步骤：** 前向得到目标输出，反向到目标卷积层取梯度，计算每个通道的重要权重（梯度均值），按权重线性组合该层激活，插值到输入分辨率得到热图。 | 原用于 CNN，但已扩展至 VQA/多模态场景（以答案 token 的分数作为目标）。需要梯度访问。对自由文本生成较难直接应用，但可对特定候选答案 token 应用。 | 对模型**最后卷积层/特征图**计算梯度，加权得到热力图。具体流程：以要解释的输出（例如某类别的logit）对最后一层每个通道的特征激活求导，得到每个通道的重要性梯度；取各通道梯度在空间位置上的平均，作为该通道的权重；再将通道特征图按这些权重加权相加，得到一个粗略的**类别激活图** (CAM)。最后将其插值上采样到输入图像尺寸并叠加到图像上，即得凸显重要区域的热力图。 | **视觉分支的最后特征图**。通常是**卷积神经网络**最后的feature map，但对于Transformer可取最后一层的patch表示并reshape成2D图。如果是多模态模型，可对其中**视觉编码器**部分应用Grad-CAM来高亮视觉证据。但Grad-CAM不直接解释文本成分。 | 前端将Grad-CAM生成的**热力图**叠加在原始图像上。通常采用**半透明的彩色热力图**（如红色表示高重要性，蓝色低）覆盖输入图像。用户可以直观看出模型主要关注的区域。例如对猫狗分类，猫类Grad-CAM会在猫脸区域呈红色。可提供交互选择不同输出目标以显示不同热力图，也可导出热力图用于报告。 | 当需要**定位模型决策依据的图像区域**时使用。Grad-CAM应用非常广泛于图像分类、目标检测，以及VQA等（将答案视作类别）。在机器人视觉中，可用来让用户理解“机器人关注到了哪个部分”。优点是实现简单、计算高效，仅需一次前传后传就能得到可视化结果。 | **强关联**。Grad-CAM专门针对某一输出类别/答案的score进行梯度计算，因此生成的热力图是解释该特定输出的（通常是模型预测的类别）。每次只能解释一个输出目标。热力图中的高亮区域表示那些位置的特征对提升该输出score有较大正贡献。 | **需要梯度**。需要对选定输出$y$相对于最后视觉特征图$A$计算$\partial y/\partial A$. 通常选择模型预测的类别为目标（也可用真值类别）。计算时一般取**正梯度**（ReLU）以关注正向贡献。需要标准反传一次即可。 | **单层**。Grad-CAM只作用于模型的**最后一层卷积或特征**（例如ResNet的layer4输出或ViT的最后Attention块输出）。不显式涉及多层，也无特殊处理残差（残差已体现在最终特征中）。因此它提供的是较粗粒度的定位，不跟踪中间层细节。 | 模型需要**可计算梯度**且有明确的**空间特征层**。卷积网络天然符合；ViT这类需将patch嵌入视作“像素”处理。输出要有可导的score，例如分类logit或VQA中答案的score（对生成式答案可选定某关键词的概率）。此外无特殊要求，黑盒则无法用，需要模型可导访问。 | **Grad-CAM**: Selvaraju et al. (ICCV 2017)提出。常用于CNN解释，许多工具库实现了Grad-CAM (TensorFlow/PyTorch官方教程)。拓展如Grad-CAM++ (2018)改进权重计算。 |
| **Score-CAM / gScore-CAM** | 同上（视觉特征图，不使用梯度或部分使用梯度的CAM变体）。 | **时间：** 比 Grad-CAM 更昂贵：若有 K 个通道/特征图，需 O(K) 次前向（每次对掩码图像推理）。gScore-CAM 通过对通道分组采样来降低开销。**空间：** 存储多个上采样后的掩码图与对应输出分数。**步骤：** 对每个特征图（或分组），上采样为输入尺寸形成掩码，用掩码/加权图像做前向，记录输出分数，将分数作为该通道权重，按权重合并得到热图。 | 黑盒（仅需输出），适用于无梯度访问场景，如 CLIP 或其他 VLM。可用于分类、定位任务或对输出定义评分函数的生成模型。运行时间随通道数增长；分组采样（gScore-CAM）可缓解对大型模型的开销。 | **Score-CAM**通过**前向遮蔽**获取特征重要性。做法是：对最后卷积层每个通道的特征图，先将其归一化并上采样到输入大小作为掩膜，将输入图像按此掩膜强调对应区域，送入模型计算该目标类别的score。以这个score作为该通道的权重。遍历所有通道得到一组权重后，再将原特征图加权求和得到热力图。这样避免了使用梯度。**gScore-CAM**是Score-CAM的改进版本，引入梯度指导：并非逐通道遍历所有特征图，而是借助**梯度**先粗略筛选出对输出有较大影响的通道或通道组合，再对这些进行分组扰动评估。这样减少前向次数并降低噪声影响，使其适用于CLIP等多模态模型的大特征图。 | **视觉模型的最后卷积层特征**。与Grad-CAM相同，主要作用于**CNN特征图**或类似2D激活。多模态模型如CLIP，可以对其视觉编码器最后层用gScore-CAM来可视化“CLIP关注的物体”。方法本身不直接处理文本，但对于CLIP这种图文模型，可以选择输出为特定文本标签的匹配分数进行解释。 | 前端输出为**图像热力图**。Score-CAM得到的热力图可与原图叠加，颜色亮度表示区域的重要性，与Grad-CAM的可视化形式类似。因为Score-CAM往往更平滑、无噪声，可得到清晰的热点区域。在交互界面中，可以提供“解释”按钮，计算并显示热力图，让用户了解模型关注点。若是gScore-CAM，对CLIP这类模型，可在图像上标出模型认为与给定文本标签最相关的区域，实现零样本目标检测式的可视化。 | 当**无法使用梯度或怀疑梯度不可靠**时使用Score-CAM。例如某些封装的推理API不提供梯度时，可通过不断前向遮挡计算得到重要区域。gScore-CAM适用于**大规模多模态模型**（如CLIP）的解释：它利用少量梯度减少运算，相比Score-CAM在高维特征下更高效稳定。在机器人应用中，如果模型是黑盒，只能反复测试遮挡效果来推断模型关注点，这时Score-CAM是一种方案。 | **强关联**。和Grad-CAM类似，Score-CAM每次针对特定输出score进行前向测量，其权重完全由**目标输出**的变化确定，因此得到的热力图是输出相关的因果性衡量。gScore-CAM同样围绕指定输出，只是在评估过程中利用梯度挑选通道，使关联更聚焦于与该输出高度相关的部分。 | **不需要梯度（Score-CAM）**：完全采用前向计算输出score，无需反传梯度。**gScore-CAM**部分使用梯度信息指导，但最终热力图权重仍由前向score决定。两者都不需要真值标签，直接用模型预测分数即可。相比Grad-CAM，它们适合模型梯度不易获取或想避免梯度噪声的情况。 | **单层**。Score-CAM/gScore-CAM只对**最后一层特征图**操作，与Grad-CAM作用层相同。它们不显式处理跨层影响，也未特殊考虑残差（残差效果已含在特征图中）。gScore-CAM通过梯度预筛选通道，但整体仍是针对单层输出的分析。 | **Score-CAM**: Wang *et al.* (CVPRW 2020)提出，无梯度的CAM方法。**gScore-CAM**: Chen *et al.* (ACCV 2022)提出，用于解释CLIP等跨模态模型，对提高弱定位效果显著。两者的代码均有开源，实现简单易用。 |  |
| **Integrated Gradients (IG)** | 作用于**输入特征**（图像像素或 token 嵌入维度），通过沿从基线输入到真实输入路径的梯度积分，将输出归因到每个输入特征上。 | **时间：** O(k) 次前向/后向（k 为积分步数，通常 50–300），总成本 O(k·P)。**空间：** 只需逐步保存当前梯度与最终归因图（O(M)，M 为特征数）。**步骤：** 选定基线（如全黑图像或空文本），按比例插值生成 k 个中间输入并在每个点计算输出对输入的梯度，取平均梯度并乘以输入差值，得到每个特征的归因值。可结合 SmoothGrad 减少噪声。基线选择很重要（文本可用 padding 或特殊 token）。 | 需梯度访问，适用于任何可微 VLM（分类或可定义标量目标的生成模型）。IG 满足理论上的完备性（completeness），常用于把答案置信度或特定 token 的概率归因到图像/文本输入。 | 对每个输入特征计算沿从**基线到当前输入路径**的梯度累积。做法：选一个没有信号的基线输入$x_0$（如全零图像、空白文本）；将输入逐步线性插值为$ x_0 \to x$分成$k$步；对路径上每一步插值$ x_i$计算输出对输入的梯度$\nabla_x f(x_i)$；对这些梯度求平均并乘以输入差$(x - x_0)$，得到每个维度的归因。这一结果即为各输入分量对输出的综合影响值。IG理论上满足**完备性**（所有特征归因之和等于输出差值），因而是一种有良好理论性质的归因方法。 | **输入特征本身**。IG直接作用于**原始输入空间**的特征维度：对于图像是每个像素（或每个像素通道），对于文本可作用于词向量每个维度（常将每个词embedding的总范数归因作为该词重要性）。在多模态模型中，可同时对图像像素和文本token embedding计算梯度，但通常分别对其中一个模态进行归因分析。 | 对图像，可将IG计算得到的每像素贡献值可视化为**彩色热力图**：通常用红蓝两色表示正负贡献，颜色深浅表示绝对值大小。这样一张图直观展示哪些像素增加了输出（红色），哪些像素抑制了输出（蓝色）。对文本，可对每个单词标注一个贡献分数并以配色高亮，比如绿色表示该词提高了答案置信度，红色表示降低。为了更平滑，也有用IG生成细粒度蒙版再叠加在图像上的，实现类似Grad-CAM效果但更加精细。总之，IG的可视化强调正负作用，帮助理解模型是靠哪些特征做出的判定。 | **适用于需要精细量化每个输入维度贡献**的时候。例如验证一个图像分类模型是否利用了某些像素区域，或NLP模型对每个词的重要性排序。IG在学术中常用来确保归因结果**严格满足数学性质**，比如做对比实验验证模型是否依赖偏置特征。也用于产生平滑的像素重要性图，配合SmoothGrad可以减少噪声。 | **强关联**。积分梯度完全围绕某个特定输出（例如置信度）的梯度进行积分，因此得到的贡献值直接解释该输出。其中正值表示该特征从基线增加至当前值**提高了**输出，负值表示**降低了**该输出。归因结果与输出定量相关，对不同输出需要分别计算。 | **需要梯度**。IG本质是多次梯度求和，所以模型必须可微。需要选择解释的目标输出（一般用模型预测的输出）。不要求人工真值，但需要**定义一个基线输入**作为参考点。计算开销是对每一步插值做一次反向传播，如果选50步就需50次梯度计算（可并行）。 | **贯穿所有层**。IG通过梯度链式求导，会遍历模型所有层，但并非逐层单独分析，而是直接将最终输出对输入的梯度沿路径积分。因此没有显式考虑残差等结构，但只要梯度能通过，残差影响就自动包含在梯度值中了。它不提供层级细节，只提供输入级别的归因。 | **Integrated Gradients**: Sundararajan et al. (ICML 2017)，提出满足完备性公理的归因法。常用于图像（如Google提供的IG示例）和文本解释。扩展方法包括SmoothGrad (Smilkov et al. 2017) 将多次添加噪声取平均以稳定IG结果。IG也是许多开源解释库（如Captum）的核心方法之一。 |  |
| **LRP (Layer-wise Relevance Propagation)** | 作用于**模型所有层**，通过逐层将输出相关性分配回输入，保持相关性守恒（各层相关性之和保持不变）。最终得到像素或 token 的重要性分布。 | **时间：** 类似一次带特殊规则的反向传播（O(P)）。每层按其类型使用相应的 LRP 规则分配相关性。**空间：** 需要按层存储相关性（总体 O(P) 或分层处理减少峰值内存）。**步骤：** 从输出得相关性总量，按层向前分配到前一层神经元（按权重/激活比例），逐层迭代至输入，生成输入层的归因热图或 token 重要性。 | 可扩展到 CNN、transformer 与多模态模型（需为注意力、残差等设计传播规则）。需要白盒访问与权重信息。通常比简单梯度更稳健、噪声更少，但对每种层类型的传播规则敏感。适用于解释 VQA 或分类决策的实际计算路径。 | 从输出开始，逐层**逆向传播“相关性”**给输入。LRP为每种层定义了保守的分配规则，使每层分配给其输入的相关性之和等于该单元自身的相关性。例如，对于全连接层，按照各输入神经元的加权输入占输出的比例分配相关性；对于ReLU激活，只将正相关性向后传，负的剪除；卷积层则类似全连接在空间维度上分配相关性。在Transformer注意力层，有专门规则处理softmax：如AttnLRP中将softmax梯度截断，以保持相关性守恒。最终，经过逐层传播后，每个输入像素或token都会得到一个相关性值，表示它对输出的贡献（正负皆可）。 | **整个模型的神经元**。LRP没有特定关注某模块，而是**逐层处理所有单元**。例如在视觉模型中，它会遍历卷积层和全连接层；在VLM中，会处理**注意力层**（需要特定适配）和**FFN层**等。因此作用对象涵盖Transformer的**注意力权重分配**、**FFN隐含节点**等等，最终落脚到**输入像素和文本token**。 | 前端展示和IG类似，产生**逐像素/逐词贡献热力图**。对图像，将LRP得到的像素相关值绘制为热力图，红色表示该像素对提高输出有贡献，蓝色表示抑制输出。LRP的图像热力图往往覆盖区域更加集中，容易与实际物体对应。对文本，同样用颜色标注单词：如在VQA中高亮出真正引导模型答案的关键词。也可以结合图像：例如在图像上框选LRP找出的最相关目标，同时在问题句中标出相关性最高的词，以组合形成多模态解释。 | 当要求**高保真和严格守恒**的解释时使用。比如在**安全关键**的应用中，需要确保解释完整分解模型决策，LRP提供了这种分解机制。它常用于卷积网络（如图像分类）的解释，相比Grad-CAM得到的热力图往往更清晰、没有不相关噪点。对于多模态Transformer，也有研究应用LRP来同时解释图像和文本（通过分层分配，可找到真正引起答案的图像区域和问题词）。总之在需要验证模型决策路径、排查模型偏差时，LRP是一种**可信度较高**的方法。 | **强关联**。LRP直接以**输出预测得分**为总相关性进行分解，因而完全针对该输出进行解释。通过确保每一步分解都严格遵守加和守恒，LRP生成的输入归因可以被解释为对输出的真实“贡献”（满足整体和=输出）。因此其高亮区域与输出间具备一定因果意义。 | **不需要标准梯度**（非梯度法），但需要网络参数和激活值的白盒访问，按LRP规则手工计算反向分配。无需真值标签，用模型输出自身进行解释即可。与梯度法相比不受梯度消散等影响，但需要实现复杂的传播算法。 | **多层**。LRP遍历**网络所有层**逐步反传相关性，在每层保持总和守恒。针对Transformer这类有残差和softmax的结构，需要特殊处理以避免相关性“漏掉”或发散。近年来有Transformer版LRP（如AttnLRP）通过修改softmax处理确保Transformer各层也能守恒地分配相关性。总之LRP必须贯穿模型的每层计算单元，才能最终把相关性分到输入。 | **LRP**: Bach *et al.* (PLOS 2015)首创，用于解释CNN分类；Montavon *et al.* (2019)扩展理论。**Transformer LRP**: Voita 2020尝试，将LRP用于Transformer注意力但遇挑战；最新的AttnLRP (Chefer 2022)解决Softmax问题，实现Transformer的守恒归因。LRP方法已集成在一些XAI库中（如 *innvestigation*）。 |  |
| **LIME** | 作用于**单个样本的输入特征**（图像的超像素或句子的词）。把目标模型视作黑盒，在输入邻域内用简单可解释的代理模型（如线性模型）拟合局部行为，从而解释该预测。 | **时间：** O(N·M) 次前向（N 为扰动样本数，M 为特征数；实务上通过分组超像素使 M 变小），通常 N 为几百。**空间：** 存储 N 个样本与输出结果，拟合代理模型开销极小。**步骤：** 随机生成输入扰动（如隐藏某些超像素或词），获得对应模型输出；按与原样本相似性加权样本，拟合简单代理模型（常为线性回归），使用代理模型系数作为特征重要性。 | 黑盒、模型无关。适用于任何可查询输出的 VLM。对多模态 VQA，可把图像区域与问题词作为特征，LIME 会指示哪些区域/词的移除最改变答案。易于理解，但局部线性假设与扰动策略会影响解释的保真性。 | 通过学习一个**局部代理模型**来解释单次预测。具体来说：针对某输入，LIME生成一批邻近样本（对图像随机遮盖某些超像素区域，对文本随机去掉某些词等），并用原模型预测这些样本的输出。然后以这些样本的特征存在/缺失作为自变量、模型输出作为因变量，训练一个简单的可解释模型（通常是加权线性回归）来近似原模型在该邻域的行为。这个线性模型的系数即各特征对输出的正负贡献。对于图像，特征一般定义为预先分割的**超像素区域**；对于文本，特征可取为单词出现与否。LIME通常还加一个稀疏性约束，只保留几个重要特征以便解释易懂。 | **输入特征片段**。对图像通常以**超像素区域**为基本单元（预先用分割算法得到）；对文本以**单词或n-gram**为特征。LIME实际上在衡量输入哪些部分（片段）对输出影响最大。因此作用对象不是模型内部，而是原始输入经过人为划分后的组成部分。对于多模态输入，也可以将图像超像素和文本词共同作为特征参与LIME。 | 图像情况下，前端可突出显示**若干超像素区域**来解释。例如在原图上用彩色高亮3个超像素区域，并用+/-符号或颜色表示它们对预测的正/负贡献，以及相应权重大小。文本情况下，可直接将**重要单词**用背景色标黄/标红等表示其贡献方向和大小，同时在侧边列出权重数值。由于LIME通常只选最重要的少数特征，前端展示应简洁，比如注明“模型预测‘猫’主要因为图像中耳朵和胡子的特征”。这种输出易于人理解和验证。 | 在希望解释**任意模型单次预测**又无权访问其内部时使用。LIME适用于各种数据类型的分类/回归任务，包括图像分类（如解释为什么识别为猫，突出猫耳朵区域）、文本分类（突出句子中影响分类的词）等。它的优点是**模型无关**、易于理解，可快速用于验证模型是否利用了预期的特征（例如模型是否在看背景）。缺点是随机采样可能不稳定，需要多次取平均来更可靠。 | **中等关联**。LIME得到的是对模型**局部行为**的近似解释，围绕模型在当前输入附近的预测来拟合，因此与该预测输出相关。但由于代理模型是近似的，其解释不保证与真实模型完全一致（代理模型拟合误差会影响解释可信度）。相比直接梯度或注意力，LIME的解释具有**输出指向性**但精度有限。 | **不需要梯度**，模型可当作纯黑盒。也不需要真值标签，使用模型预测输出本身作为拟合目标。整个过程只需重复调用模型做推理。LIME只假设模型的输出可被模拟，不关心内部原理。 | **无跨层**。LIME在输入空间构建线性近似，不涉及模型内部结构。残差、注意力等内部结构都被视作黑盒的一部分，没有直接处理。LIME等价于在模型输入输出间做一个局部线性模型，因此不存在跨层信息重建的问题。 | **LIME**: Ribeiro *et al.* (KDD 2016)提出经典方法。有名的实现如Python库*lime*可支持图像/文本解释。LIME被广泛应用于工业场景的模型审核，例如AWS的Clarify工具中就包含类似LIME的功能，用于解释黑盒模型。局限在于解释局部有效，全局可靠性需谨慎。 |  |
| **SHAP** | 黑盒且理论严格，适用于任何可查询的模型预测。可用于图像/文本（将有意义部分作为特征）。适合对 VLM 的特定输出（如某答案概率）进行精确但可能昂贵的归因分析。常用近似方案以降低计算成本。 | **时间：** 理论上对 M 个特征需 2^M 次评估（不可行）。实务用采样近似：Kernel SHAP 通常需要 O(M²) 或 O(M·N)（N 为样本数）次模型评估；对图像常用分段后 M 为几十可行。**空间：** 存储采样子集与评估结果（O(N)）。**步骤：** 定义“缺失”基线（如被掩码的超像素），随机采样特征子集并评估模型输出，借加权线性回归或线性系统求解得到每个特征的 Shapley 值（满足一致性与公平性公理）。 | 黑盒且理论严格，适用于任何可查询的模型预测。可用于图像/文本（将有意义部分作为特征）。适合对 VLM 的特定输出（如某答案概率）进行精确但可能昂贵的归因分析。常用近似方案以降低计算成本。 | 利用**Shapley值**公平分摊特征贡献的一类方法。核心思想是：每个特征的贡献等于考虑它加入各种特征组合时对输出提升的平均。实际计算中，由于遍历所有$2^M$种组合不可行，采用**蒙特卡洛采样近似**。常用的是KernelSHAP算法：对输入特征（如图像分块或文本词）反复随机采样不同的存在/遮蔽组合，送入模型计算输出；以这些数据为训练集，用特征存在指示作为输入训练一个加性模型来逼近原模型，约束解出其特征系数即近似的Shapley值。这样得到每个特征的一个实数，表示该特征对该预测输出相比于缺失时的贡献大小。 | **输入特征**。一般将输入拆解为有限个**可组合的特征**：如一个图像划分成10块区域，一个文本取若干关键单词。SHAP计算的是这些特征的贡献值。对于多模态，可把图像区域和文本片段一起作为特征集。从本质上讲，作用对象仍然是**输入的组成部分**，与LIME相同，只是理论上以Shapley框架计算其重要性。 | 前端可以以**贡献条形图或重要性列表**形式呈现SHAP结果，也可以图像/文本标注。对于分类任务，常见做法是显示每个特征的Shapley值条形图：正值用红条，负值用蓝条，长度表示贡献大小，列出比如“毛发纹理:+0.8, 背景树林:-0.3”等等。也可将图像中贡献最高的几个区域高亮，并在旁边附文字说明它们的正负作用。对于文本，同样高亮有积极作用的词（如绿色加分）和负面作用词（红色减分）。由于SHAP提供了绝对数值，可在界面上允许用户查看精确的数值和排名，从而详细了解模型决策依据的构成。 | 在需要**严格的理论保障**和**公平分摊**时使用。SHAP适合对模型输出进行详尽的特征贡献分析，常用于金融风控、医疗等要求解释公平的场景。例如给出某贷款审批模型中每个输入因素（收入、年龄等）对最终评分的贡献，以证明没有不公平决策。对视觉模型，SHAP也可用来**验证模型关注点**：比如VALE框架就用SHAP选出图像中最影响分类的区域。不过由于SHAP计算慢，通常特征数不会太大，在图像中常以几个粗粒度区域或超像素为特征。 | **强关联**。SHAP值本质上针对**当前预测输出**计算，每个特征的值表示对该输出的作用，而且满足所有特征贡献和等于输出与基线的差异。因此它和输出的定量关联明确。不过SHAP值是对模型行为的“均衡”视角，代表特征在各种组合中的平均影响，可能与实际这一次输入中的作用方式有所区别，但总体仍然针对该输出。 | **不需要梯度**，也是模型无关。一般不需要真值标签，直接以模型预测的score或输出概率为依据（也可针对真值做归因以解释模型为何没预测正确）。计算过程中只需反复查询模型输出，无需内部信息。要注意计算复杂度高，需要利用近似算法。 | **无跨层**。SHAP在输入特征层面建立线性模型，类似LIME，不考虑模型内部结构。因此不存在跨层传播过程。一些针对模型内部的Shapley方法（如针对中间神经元计算Shap值）也有，但经典SHAP指输入特征的Shapley值归因。残差等内部细节同样被视作黑盒处理。 | **SHAP**: Lundberg & Lee (NIPS 2017)提出，将Shapley理论引入模型解释领域。开源实现有**SHAP库**，支持图像、文本、表格数据的解释，被广泛使用。值得注意的应用包括Google的Cloud AI Explanations等。理论上SHAP结合了LIME思想和博弈论分摊。由于耗时大，衍生方法如KernelSHAP、TreeSHAP等用于不同模型高效计算。 |  |
| **遮挡测试 (Occlusion / Perturbation)** | **输入组件**（图像 patch、场景对象、句子中的词）进行逐一遮挡或替换，测量输出的变化——如果移除某部分显著改变输出，则说明其重要。是直接的因果测试。 | **时间：** 若按每个特征逐一遮挡需 O(M) 次前向；RISE 类随机掩码方法需 O(N) 次前向（N 可为几百到几千），通过多次随机采样得到像素重要性。**空间：** 极小，仅需记录每次输出差异。**步骤：** 对每个候选特征（或随机掩码）做遮挡/替换（如置灰、模糊或删除词），计算模型输出差值，将差值汇总或统计关联（RISE 通过随机掩码并根据输出与掩码的相关性重建显著性图）。 | 黑盒方法，直接测量因果影响，适用于任意 VLM。简单易懂但速度慢，且掩码可能引入分布偏移（如灰块对模型造成异常响应）。仍是多模态模型的强基线方法，也适用于在微调前对基础模型做因果验证。 | 直接**干预输入**评估输出变化的简单可解释方法。具体方法是在输入上**系统地遮挡或移除某一部分**，记录模型输出如何变化。以图像为例：可以用固定大小的灰色补丁在图像各个位置滑动遮挡，每次计算模型预测置信度的下降幅度，最后生成一个与图像对应的敏感度图，其中数值高的区域表示遮挡它对输出影响大（模型强依赖）。文本中，可逐个去掉问句中的单词，观察答案变化（如预测概率降低多少）。更高级的如RISE方法：随机产生大量遮挡掩膜叠加输入，多次前向取输出平均相关性，以估计每个像素的重要性。无论方法细节，遮挡法的核心是在**局部移除信息**来测试该信息对模型决策的重要程度。 | **输入的各部分**。对图像，可以是**像素邻域**（滑窗遮挡）或**预定义区域**（如语义分割区域）。对文本，是**单词或短语**。在多模态场景，可以分别遮挡图像或文本：如遮挡图像看答案是否变，或遮挡问题词看回答如何变。从解释角度，作用对象就是输入中具体的一块内容（区域、词、时间段等）。 | 前端可以将遮挡分析结果直接展示为**敏感度热力图**。对图像，把每个像素位置遮挡后的输出变化量映射为一个热力值，构成与图像同尺寸的热图叠加在原图上。这样用户可以看到哪些区域变红（高重要，遮掉就性能下降）、哪些区域蓝或无色（无关）。对文本，可以以列表或高亮形式呈现每个单词的重要性分值，例如在问句上方注明“去掉这个词，模型回答正确率下降X%”，或者简单地将影响大的词用醒目颜色标出。此方法得到的可视化直观易懂，因为它直示“拿掉这里，输出就变了”的效果，非常符合人对因果的理解。 | 作为**最直观可靠**的基准方法经常使用。无论模型多复杂，遮挡法都能适用，因此在验证其它解释方法时常用作参考。比如在新模型上，先用遮挡检查模型确实依赖了主要对象区域，再用更复杂的方法做更精细解释。遮挡法也适合在**时间和要求不苛刻**的情况下给出简单解释，例如机器人导航中临时遮挡视觉输入的一部分看行为是否变化，以确定该部分的重要性。不过由于遮挡需要多次运行模型（可能上百次上千次），在实时或大模型场景下可能过慢。 | **强关联（因果）**。遮挡实验被认为提供了较直接的因果证据：如果移除某部分导致输出显著变化，说明该部分对当前输出**具有因果贡献**。因此遮挡得到的高亮区域与输出高度相关，并且具有明确的“没有它就不行”含义。不过需要小心，有时遮挡引入不自然信息（如一块灰色方块）也可能影响模型，需对这种分布外影响进行控制。整体而言，这是衡量输入对输出影响的**直接方法**。 | **不需要梯度**，模型完全当作黑盒使用。也无需提供真值标签，比较的是模型预测在完整输入 vs 缺少部分输入时的改变。只要能获取模型输出（如分类概率或生成文字变化）即可评估。不涉及任何反向传播。 | **不涉及跨层**。仅对输入进行操作，模型内部无需任何改动。残差、注意力等都自然而然地对遮挡后的输入再计算一遍输出。因此这是**输入层级**的因果测试方法，不牵涉对模型内部机理的分析。 | 最早用于CNN可视化：**遮挡法**来自 Zeiler & Fergus (ECCV 2014) 的经典工作，用滑动窗遮挡定位物体部位。近来**RISE** (Petsiuk et al. BMVC 2018) 提供了高效随机遮挡实现。遮挡法没有特定论文专利，因为它方法朴素但可靠，被广泛采用作为解释的baseline。许多开源工具（例如Torchray）也实现了遮挡分析。 |  |
| **DIME (Disentangled Multimodal Explanations)** | 作用于**多模态输出**，旨在**解耦每种模态的贡献**——分别高亮“图像中什么”和“文本中什么”影响了预测。 | **时间：** 需对每个模态计算对输出的敏感度或训练小型解释器，通常为少次前向/后向即可。**空间：** 中等，需同时保存两种模态的表示与各自的归因信息。**步骤：** 固定其中一模态改变另一模态（或使用注意力分解）以量化各自对结果的贡献，输出为图像热图与文本高亮，两者分别解释其模态的作用。 | 专为视觉+语言模型设计（如 VQA）。可在审计或机器人场景中分别验证视觉接地与语言理解是否合理。通常需要模型内部访问（梯度或注意力）或基于输入操纵的策略。 | 通过将多模态模型的输出贡献**解耦为单模态和多模态**部分，实现细粒度解释。DIME的方法包括：分别估计输入中**视觉部分**单独对输出的贡献、**文本部分**单独对输出的贡献，以及两者交互产生的额外贡献。技术细节上，DIME扩展了LIME的思想到多模态：它会在保持文本不变只看图像、或保持图像不变只看文本的情况下，生成邻域样本并训练代理模型，从而得到图像特征的重要性分值和文本特征的重要性分值；然后再考虑图文同时存在时输出提升的那部分，归因为**跨模态交互**。比如，如果图像中一辆红色公交车单靠图像特征和单靠文本描述都无法确定答案“公交车”，但图文结合使模型输出正确，则这部分贡献归于交互作用。通过这种拆解，DIME能输出“三份”解释：纯视觉解释、纯文本解释、以及它们共同作用的解释。 | **多模态输入各自的特征**。具体到VQA，作用对象包括**图像的视觉特征**（可以是图像分区、检测到的物体等）和**问题文本的词**。DIME会分别输出这些视觉特征的重要性、文本特征的重要性；另外交互部分实际上对应**图像+文本组合**产生的影响，可能无法用单一对象表示，但可以理解为模型需要某些图文组合才能决定答案。例如可能突出“图片中红色物体”+“问题提到车辆”共同决定了答案。 | 前端可以将DIME的结果呈现为**双视图**：左侧显示图像及其解释（高亮重要区域），右侧显示文本及其解释（高亮重要词）。分别用颜色或标注说明这些部分各自对输出的贡献值。如果有交互贡献，可以在界面上附一句说明，如“图文交互贡献: 输出置信度还额外提高了30%来自图文结合”。或者以饼图形式表示三部分贡献占比，让用户直观了解模型主要依赖哪一方面信息。通过这种界面，用户能清楚地分辨模型决策中视觉讯息和语言讯息的作用程度。 | 当希望**明确区分模型决策中的视觉贡献和文本贡献**时使用。例如在VQA系统中，我们想知道模型的答案多少是依据图片，多少是依据问题文本本身。DIME可以回答这种问题：如果发现视觉单模态贡献很低，文本单模态就能给出类似答案，说明模型可能更多地在“猜问题”而非看图。它有助于**模型诊断**，比如发现偏向语言猜测的现象，提升多模态融合质量。也适用于其它多模态任务（音频-文本等），以验证模型有没有真正利用所有模态信息。 | **强关联**。DIME的解释紧紧围绕模型的**最终输出**，但精妙之处在于区分了不同来源：哪部分来自图像，哪部分来自文本，哪部分是非单独看任一模态能得出的（交互）。因此它不仅告诉你输出依赖哪些特征，还告诉你这些特征是来自视觉还是语言还是两者兼有。这对分析模型决策**是否真正融合了多模态信息**非常有用。每一部分解释都是针对该输出局部线性近似得到的，因此关联性强但也继承LIME的局部近似属性。 | **不需要梯度**，是完全模型无关的方法。也无需真值标签，使用模型的预测输出即可。DIME通过构造输入扰动和观察输出的方式进行归因分析，与LIME类似而扩展到多模态，因此整个过程不涉及模型内部梯度或参数。 | **无跨层**。DIME是在输入/输出层面进行归因分析，没有追踪模型内部层次。它关注的是**不同模态输入子集**对输出的影响，所以本质是在输入空间做分析。模型内部的注意力或融合机制不直接体现，但其效果会反映在交互贡献中（如果模型确实需要图文协作才能得出输出，则交互贡献就会显著）。 | 无需模型结构信息，只需要能**单独提供各模态输入**。模型最好支持对单一模态输入给出输出（或者可以人为地将另一模态置为空白/默认）。以VQA为例，需要能够让模型只看图像（无提问）或只看问题（无图像）时仍然产生一个输出（可能是不可靠的输出，但可用于比较）。如果模型在缺失一模态时无法运行，则需模拟一种缺失状态的输出（例如固定一个特殊输出表示“不确定”）。此外，需要预先有将图像划分特征（如超像素或物体检测）的方法供LIME使用。 | **DIME**: Lyu *et al.* (AAAI 2022) 提出。论文通过在合成和真实多模态任务上验证，显示了DIME能够准确地分离单模态和多模态交互贡献。它是模型无关的，因而可用于各种多模态架构的解释。在实践中，可结合超像素分割与LIME实现。 |
| **VALE (Visual And Language Explanation, 2024)** | 针对**视觉分类器**提供**视觉与文本双重解释**：先用 XAI（如 SHAP）找到重要图像区域，用 SAM（Segment Anything Model）精确分割，再用预训练 VLM/LLM 生成自然语言解释——把视觉证据翻成句子。 | **时间：** 多阶段流水线：计算 SHAP（最耗时，需数百次前向评估）、SAM 分割较快（几秒），LLM 生成文本一次前向。**空间：** 保存图像、分割掩码与文本生成上下文，中等开销。**步骤：** 用 SHAP 找出重要图像超像素/区域；用 SAM 提取精确掩码；把原图与掩码或裁切结果输入到 VLM/LLM 生成解释句（例如“模型关注制服上的徽章，所以被分类为警察”）。 | 管道化方法，原模型可为黑盒。适用于需要可读文本解释的场景（例如机器人向人类解释其视觉判断）。依赖外部分割与语言模型。 | 采用一个**多阶段框架**为视觉模型提供图文解释。第一步，使用XAI方法找出**图像中最有影响力的区域**：论文中采用SHAP，对图像划分的区域计算Shapley值，选出贡献最大的几个区域。第二步，利用**SAM（Segment Anything Model）对这些重要区域进行图像分割，获得精确的蒙版或轮廓。第三步，将原图和提取的关键区域送入一个强大的预训练视觉语言模型**（如BLIP-2或GPT-4V），生成一段**自然语言解释**。这解释通常说明那些高影响区域的内容以及它们如何导致模型的预测。例如，如果分类模型预测一只鸟，解释可能生成：“模型关注到该区域的红色羽毛，这是红鹦鹉的典型特征，因此将其分类为红鹦鹉。”通过这三步，VALE实现了视觉与语言相结合的解释。 | **图像分类模型**（或检测模型）及相关输入。VALE主要围绕**视觉模型输出**来构建解释，因此作用对象首先是**图像的关键区域**。然后通过VLM将这些区域内容转化为文字说明，作用对象转为**文本解释句子**。所以VALE同时涉及视觉和语言，但语言部分是解释，不是模型原输出的一部分。它并非解释文本模态输入，因此在VQA等双模态输入任务中未直接应用，一般用于纯图像模型的结果解释。 | 前端展示VALE的结果可以如下：在图像上用清晰的标记（如边框或半透明遮罩）标出模型关注的关键区域，每个区域用不同颜色以示区分；在图像下方或旁边，给出生成的文本解释，对应地提及这些颜色区域包含的内容和意义。比如某区块用红框圈出，同时文字说明里有“红框区域显示了XX”。这样用户能直接将解释文字与图中高亮部分对照起来，达到**图文并茂**的效果。此外，可在界面上提供交互：比如鼠标悬停在解释句子的某个名词时，高亮对应的图像区域，增强理解联动。 | VALE适用于**图像识别场景下需要对最终用户提供自然语言解释**的情况。例如一个移动App识别植物品种后，除了给品种名，还想给用户一句解释：“因为我看到了叶片的锯齿形状和对生排列，这是典型特征”。机器人领域，如果机器人用视觉模型分类某物体，VALE框架可以让机器人高亮图像区域并**用一句话解释**它为何那样判断。这非常适合提高用户对模型决策的信任度和理解度。 | **强关联**。VALE的视觉热力图来源于SHAP，对模型预测输出的依赖区域进行定位，具有直接关联。文字解释则是围绕这些定位区域来描述模型决策原因，是对模型输出的二次表述。这种解释对输出的关联性较高，而且形式对人类友好。不过要注意文字部分由另一个模型生成，可能未必100%反映原模型真实机理，但一般通过提示会忠实于高亮区域内容，间接保持关联。 | **不需要原模型梯度**。主模型被视为黑盒，仅用其输出做SHAP分析。也不需要真值标签，解释针对模型自己的预测。需要梯度的是外部VL生成模型，但那只是用来生成描述的，一般不涉及反传。GroundTruth同样不需要提供，因为解释不是通过学习得到，而是通过预训练模型生成。 | **无跨层（主模型）**。VALE不涉及解释主模型内部层次，只在输入/输出层做分析。使用的SHAP也是输入级方法。整个流程实际上将模型视作black-box组件。倒是用了另一个VL模型生成文字，但那不是被解释的模型，而是解释器。本质上VALE属于**后置解释**，不对原模型结构做任何处理。 | **VALE**: Natarajan & Nambiar (arXiv 2024)提出，用于ImageNet分类和水下声纳图像分类的解释。它整合了SHAP+SAM+BLIP等最新技术，极大缩小了机器判断与人类理解之间的差距。文章提供了实例图，展示模型热力图叠加及生成的解释语句，效果令人直观信服。目前仍是研究原型，但展示了面向用户解释AI决策的前景。 |  |
| **Embedding Probing** | 作用于**模型内部嵌入/表征**（视觉编码器输出、联合视文嵌入或中间隐藏态），通过训练轻量 probe（线性分类器等）来评估这些嵌入是否编码特定信息（如物体类别、颜色或空间位置）。 | **时间：** 提取 N 个样本的前向嵌入 O(N·P_forward)，训练 probe 的时间 O(N·d·C)（d 为嵌入维度，C 为训练步数），总体通常以特征提取为主开销。**空间：** 需保存 N×d 的嵌入矩阵。**步骤：** 冻结基础模型，采集某层嵌入与对应标签，训练简单分类器或回归器以评估是否能线性解码出目标属性；也可使用表示相似性分析（RSA）或聚类以无监督地解析语义结构。 | 适用于任意可获取中间表示的 VLM（视觉层、语言层、融合层）。可用于验证模型是否在其潜在表示中编码了机器人任务所需的信息（例如目标位置）。注意：能解码出信息并不意味着模型在最终预测时使用了这些信息（仅表示信息存在性）。 | 检查模型内部**表示空间**是否含有某种信息的技术。方法是在模型训练完毕后，**提取中间层的embedding**，然后训练一个轻量级的监督模型来预测某属性或任务标签。如果这个线性分类器（或回归）在该embedding上性能很好，则说明该embedding以线性可分的形式编码了该属性。例如，从CLIP的图像嵌入提取1000张图片，分别标注它们是否含有“汽车”；训练一个线性分类器，用这些embedding预测“有无汽车”，若准确率接近1，则表明原模型的图像embedding已经捕捉了汽车这个概念。通过在不同层重复此流程，可以观察信息在模型内部的演化：如在浅层embedding上可能无法分离某概念，但在深层embedding上线性可分度提高，说明模型逐层提取出了该概念。 | **模型的中间表示**。可以选择**视觉编码器的输出embedding**（如CLIP的图像向量）、**多模态融合层的隐含表示**，甚至**语言模型某层的隐状态**等作为探针输入。关键是这些embedding是固定的高维向量，探针以它们为输入。本质上，作用对象是模型内部的抽象特征空间，而非具体输入或输出元素。通过探针，我们探究这些抽象特征是否含有人类可解释的属性。 | 探针结果一般以**数表或曲线**呈现。比如可以绘制一张图：横轴是模型层号，纵轴是探针在某任务上的准确率，来展示表示中该任务相关信息随层的变化趋势。如果分析多个属性，可以画多条曲线或用柱状图比较。同时还可以可视化**聚类**：将embedding降维（PCA/TSNE）后，用颜色标出不同属性类别，看是否分离。例如在CLIP图像嵌入的2D投影上，不同物体类别是否各成簇。如果想面向非研究人员展示，可简单描述：“在模型的倒数第二层，能够以95%的准确率分辨出图像中是否有天空”，配合一个示意图。这类结果主要供开发者和研究员理解模型表征，而非终端用户。 | 用于**分析模型知识、特征分布**。研究者在调研模型时常用探针：例如检查BERT某层是否编码了语法结构，CLIP的多模态空间是否区分开不同语义类。在多模态机器人中，或许会用探针看视觉模块是否嵌入了位置信息、大小信息等，以判断模型有没有利用这些关键信息。探针还可用于对比不同模型的表示差异。需要注意，如果探针太强（非线性）可能会“学习”出信息，即过拟合，使我们误以为原模型有那信息。因此通常使用简单线性探针作为上限。 | **弱关联（非直接针对输出）**。探针分析关注的是模型表示与某外部定义的属性之间的关系，而不是直接解释模型当前输出。它回答的是“模型的这一层是否知道X信息”，但不一定意味着模型用到了X来给当前输出。比如探针发现视觉Embedding能预测物体颜色，但模型输出任务可能不涉及颜色。因此探针结果更多是关于模型内部**潜在知识**的评估，而非具体决策的因果解释。 | **不需要模型梯度**（除非用更复杂的探针训练，但通常线性探针训练可视作独立过程）。需要**外部标注的数据**来训练探针，即所谓Ground Truth属性。例如想探测“是否存在人脸”属性，需要准备一些输入及其人脸有/无标签供探针学习。如果是无监督的探针（如聚类或RSA分析），则不需要标签但也需要样本数据。总之探针训练是额外的，有监督探针需要相应真值。 | **可跨层比较**。通常会对模型**不同层**的embedding各自训练探针，观察性能曲线。例如看在Transformer从1层到12层，表示中某属性的可预测性如何变化。如果要详细分析，也可以针对每层不同部分（如每个attention head输出）训练多个探针，但每个探针本身只作用于单层输出，不做层间传播。残差、LayerNorm对embedding的影响都会反映在那层表示中，探针不用单独处理这些结构。 | 探针方法大量用于理解语言模型和多模态模型，如**Alain & Bengio (ICLR 2017)**提出线性探针衡量中间层可分性；Belinkov等2017用探针测机器翻译模型的语法知识；近期LLaVA论文附录中也用探针验证视觉层逐渐学到物体类别。探针分析已有综述（Hewitt & Liang 2019等）讨论其局限：探针检测到信息≠模型真的用到信息。这需要配合因果干预方法佐证。 |  |
| **TCAV (Testing with Concept Activation Vectors)** | 作用于**中间层激活单元**，关注**人类可理解的概念**（例如“条纹”、“车辆”）。通过在激活空间中学出代表概念的方向（概念激活向量），测量模型输出对该概念方向的敏感度，从而判断概念对预测的影响。 | **时间：** 需为概念与对照集做前向提取激活（O(N_concept·P_forward + N_random·P_forward)），训练线性分隔器（开销小），测试时计算方向导数代价低。**空间：** 保存所选层的激活（O(N·d)）。**步骤：** 收集包含概念与不含概念的样本，在选层激活空间训练线性分类器，分类器的法向量即为概念向量。对新样本，计算输出关于该概念向量的方向导数（或激活与向量的内积）以量化影响。 | 需模型与梯度访问（或至少激活）。适合把抽象/人类语义映射到模型内部以检测是否以及在何处编码了该概念。适用于视觉层或跨模态层，常用于偏见审计或概念重要性检测。 | 利用人定义的**高层概念**来解释模型的全局行为。步骤：首先收集若干**代表该概念的实例**（正类，例如多张有条纹的动物图片）和若干无关实例（负类，例如无条纹的随机图片）。选择模型某一层的激活作为概念判别空间：对这些样本提取该层激活，然后训练一个线性分类器将概念类和非概念类区分。训练完成后，取这个分类器的**法向量**作为该概念的“概念激活向量 (CAV)”。接着，对感兴趣的模型输出（例如某类别的logit），计算其沿这个CAV方向的**梯度（方向导数）**。这个值衡量了在该层表示上沿概念方向变化对输出的影响程度：值大表示概念存在会显著提高该输出。为了更稳健，TCAV会对多张不同输入计算方向导数，看有多少比例的样本在概念存在方向上输出增加（称为TCAV分数）。如果比如在100张斑马图片里，有95张沿“条纹”方向的导数为正，则TCAV分数=0.95，说明“条纹”概念对模型判定斑马很重要。 | **模型某层的激活空间**。通常选择**视觉模型倒数第二层**或**语言模型隐层**等作为概念检测空间。作用对象不是具体单元，而是一条**概念方向**。概念由一组样本定义，所以TCAV的对象有点抽象：它不是图像中具体哪个像素，而是“这组图像共有的概念”。因此它提供的是全局层面的解释，而非定位具体区域。但可以结合Grad-CAM等：先用TCAV定量指出概念重要，再用Grad-CAM验证概念出现时的激活部位。 | 前端可以以**概念影响力圖表**呈现TCAV结果。例如列出若干概念对某输出类别的TCAV得分：用横条长度表示正向影响的圖片比例，并用颜色区分正负影响。比如对“斑马”类别，展示“条纹: 0.95（++++）”、“草地: 0.10（+）”、“水域: -0.05（无明显影响）”等，用户可一目了解模型判定斑马几乎完全取决于条纹花纹。在报告中，也可以配几个概念示例图片帮助理解概念含义。对于多输出情形（多个类别），可以做成热力矩阵：行是概念，列是类别，颜色表示TCAV分值，从而看出哪个概念对哪些类别影响大。总之，TCAV的可视化偏重**概念层面的对比**，易于向管理者或专家解释模型的关注点是否合理。 | 用于**回答模型对特定高层概念的敏感度**问题。比如在医疗影像模型中，定义概念“肿块边缘毛刺状”，用TCAV测试该概念对模型预测“恶性/良性”的影响，可以验证模型是否利用了医学上重要的特征。又如在自动驾驶模型中，测试概念“行人存在”对刹车决策的重要性。TCAV适合**有明确语义概念关切**的场景，包括公平性审查：如测试“性别”概念对招聘模型输出的影响是否过大，以检查偏见。 | **强关联（全局）**。TCAV回答的是**“概念C对输出Y有多重要”**，这是全局层面的解释而非针对单一样本。它实质上度量了当输入更“像概念C”时，输出Y变化多少。因此其关联性是面向输出类别的整体相关，而不是具体一例的局部因果。但对于模型决策的**语义层次**理解非常有帮助：例如发现模型的“救护车”类别高度依赖“红色”概念，就可推断模型决策跟车身颜色有关。TCAV满足一定因果意义，因为方向导数可以视为在表示空间做了一个微扰试验。 | **需要梯度**用于计算方向导数，但只是针对概念方向，不是对所有输入维度。另需要**概念示例的数据**来学习CAV，有人工标注的性质。Ground Truth概念定义由用户提供（选定哪些样本算概念）。梯度计算非常简单（一个标量对一向量的导数点积）。除了梯度，探测概念需要对模型该层激活有访问权和提取能力。 | **单层（多层可重复）**。TCAV必须选定模型的某一层来定义概念向量。通常会选靠后的语义层，这样概念更可分。在该层内部进行线性分割得到CAV，没有跨层传播过程。如果需要，也可以对多个层分别进行TCAV分析，看概念影响在不同层是否存在。但概念向量不跨层迁移。残差、LN等对激活的影响在提取时都如实存在，TCAV分类器自然会利用它们，无需特别处理。 | **TCAV**: Kim *et al.* (ICML 2018)提出，将概念定量测试引入解释领域。Google大脑团队的工作，概念如“条纹”、“混凝土建筑”等在Inception网络上测试，评估模型对人类概念的敏感度。TCAV工具在GitHub (tcav团队) 有实现，支持用户自定义概念进行模型分析。它被用于审查模型偏见（Science 2018报道）等。 |  |
| **因果干预 / 内部消融 (Causal Interventions)** | 作用于**模型内部组件（神经元、注意力头、层等）**做操控（禁用、置零或替换激活），观察输出的变化，以判断该组件的因果作用。例如禁用某注意力头若导致输出丢失某知识，则该头对该知识具有因果性。 | **时间：** 视干预范围而定。单组件消融为 O(K) 次前向（K 为被测组件数）；复杂的层间替换或追踪可能需更多前向。总体可控（数百个头/神经元可逐一测试）。**空间：** 与常规推理相同（除非并行比较多个状态）。**步骤：** 选定待测组件，运行原模型得到基线输出；对组件做干预（置零、替换等），再次推理并比较输出差异。差异显著则说明组件具有因果影响。可拓展为多组件组合或“因果掩码”分析以追踪信息路径。 | 白盒的机理解释方法，适用于想精确定位知识处理位置的研究。已用于发现 LLM/VLM 中的“知识神经元”或特定注意力头负责的对齐功能。需要对模型进行可控实验（适合研究/开发阶段，部署时用于鲁棒性检测或验证）。 | 通过**操纵模型内部**来测试因果作用的方法集合。例如，选定模型某个注意力头，将其输出强制设为零，然后观察模型最终输出有什么变化。如果输出（如答案置信度）显著下降，则可推断该注意力头在生成该输出时发挥了重要作用。同理，可以对某隐层神经元做干预：将其激活值固定或置零，看模型行为变化。这些实验基于“禁止某部件工作”来测其必要性。另外一种干预是**信息替换**：比如将第L层的中间表示替换为另一输入的对应表示（保持其他部分不变），如果输出变成了另一输入的结果，说明该层承载了决定性信息。通过系统地干预不同部件并评估输出变化，可以绘制出模型内部哪些部分在特定知识或任务上起因果作用。 | **模型内部各级元素**。包括：Transformer的**注意力头**（常见研究对象，逐个mask掉看对性能影响）、**MLP层中的特定神经元**、**特定层输出**（整体替换或归零）、甚至**模型的某embedding记忆**（如词嵌入层某向量替换）。总之，作用对象可以非常细粒度（单权重/单神经元）也可以粗粒度（整层/整头）。视分析目的选择，例如知识定位倾向选单个神经元，网络压缩分析倾向整体裁剪头或层。 | 前端呈现可采用**定量图表**和**示意图**结合。定量方面，可以列出被干预的部件及其对输出的影响幅度，例如表格或柱状图：行列对应“消除X后性能下降多少”。比如显示“移除注意力头(L3-H4)：回答正确率-15%”，“冻结Neuron#1234：回答‘是’概率从90%降至20%”。这些数据可排序高低，突出最关键的部分。示意图方面，如果找到某些“知识神经元”，可以用图表示该Neuron及其连入连出关系，以及干预前后输出对比。例如对于CLIP模型，可以标注出几个人工发现的“多模态Neuron”以及可视化这个Neuron激活时网络注意的图像区域。还有如OpenAI Microscope那样，可视化单Neuron最大激活的图像集合，帮助理解其语义。如果面向专业人士，也可以画模型结构图，在其中高亮出重要组件的位置，让读者知道“第N层第K个头很重要”。总之，此方法的结果主要以研究报告形式呈现，前端多为图表，不像热力图那样直观，但对于深入理解模型非常有价值。 | 主要用于**模型内部机理研究和安全性检验**。研究人员常用该方法定位“知识神经元”或“重要注意力头”。例如在GPT中找到负责某一事实的神经元，将其调控能改变模型是否输出该事实。在多模态里，也可找某些跨模态注意力头是否专门对齐字幕和图像区域。一些论文通过**头剪枝**发现大部分注意力头可被移除而性能几乎不变，说明很多头不重要。而另一方面，也找到了**“多模态神经元”**（OpenAI 2021）等有趣现象，表明个别神经元同时响应图像和文本概念。对于实际部署，如果担心模型内部有对抗性触发或隐含决策规则，因果干预是排查的有效手段：逐个中断可疑部件，看模型输出变化，找到潜在风险点。 | **强关联（直接因果）**。这种方法实际上是在模型中做**因果实验**：修改原因看效果。因此如果某部件被消融导致输出改变，我们几乎可以断定该部件对该输出是因果相关的（当然前提是改变方式不引入副作用）。因此它是验证模型决策路径最有力的手段之一。但需要注意有时多个部件冗余，即消融一个也许输出不变，并不一定表示无作用，可能只是有冗余路径。总体来说，因果干预找到的是“没有它就不行”的关键部件或“替换它输出就变”的信息流位置，关联度明确。 | **不一定需要梯度**。大多数干预实验只需要能访问和修改模型内部张量。无需使用梯度下降，只需一次前向对比即可（有时为了定位关键部件会借助梯度或损失敏感度先挑选候选）。GroundTruth不需要提供，因为验证使用的还是模型自己的输出。只是在某些知识定位任务中，可能需要提供触发该知识的输入（如测试某知识 neuron，需要输入包含相关知识的句子）。 | **可逐层扫描**。干预可以针对**各层各单元**分别进行，具有灵活性。比如可以对Transformer**每一层的每个注意力头**进行一次消融试验。因此通常会产出一个随层的影响分布或者找出最关键的层。**残差**和其它结构对干预的响应各异：残差网络中，切除一个残差支路通常不会完全破坏功能，因为主路还有信息，所以需要看累计影响。有时需要多部件联合干预才能观察到显著变化（说明模型有冗余机制)。因此跨层因果追踪也有：例如利用信息替换方法，可以逐层地把特定输入的信息替换为另一输入，确定是哪一层开始输出就改变成另一输入了，这相当于找到信息注入的层。总之，该方法可以灵活选择干预粒度，从单神经元到整个模块再到多层联合干预。 | 代表性工作：**Michel et al. (NeurIPS 2019)**移除Transformer头分析重要性，发现多数头可裁剪；**Voita et al. (2019)**类似地筛选出关键头提升翻译性能。**Dai et al. (ACL 2022)定位LLM中的“知识神经元”，通过消融验证其存储了特定事实。OpenAI博客(2021)展示CLIP中存在同时响应文本概念和图像概念的多模态神经元。由于这类方法灵活多样，相关研究繁多，是可解释性和安全领域**的重要工具。 |  |
| **Prompt-Based Self-Explanation (Chain-of-Thought)** | **模型自身的输出**。具体对象是**模型生成的解释文本**。这段文本可能会引用视觉内容、之前的推理等。例如对于图像QA模型的自我解释，会提及**图像中的细节**和**问题的关键词**，相当于模型自己高亮了这些要点。但它本质上是模型的话语（语言模态），内部 attention/hidden 不是直接对象。 | **时间：** 只需额外一次推理生成解释（长度为 k token），开销为生成文本的时间（O(k)）。**空间：** 仅占用上下文窗口与生成文本所需内存。**步骤：** 设计提示（例如“让我们逐步思考”或“解释你为何这么回答”），让模型生成推理链或解释。可交互式追问具体图像部分以细化解释。 | 适用于具有强文本生成能力的生成式 VLM（LLM 能力强的模型）。便捷且即时，但解释可能是“合理化后的口述”而非严格反映内部计算过程（可能并非真实内部机制）。常与其他可视化/归因方法结合使用以增强可信度。 | 借助模型本身的**语言生成能力**来输出对其决策的解释。实现方法通常有两种：**Chain-of-Thought (思维链)提示和后置提问**。思维链是指在模型回答之前，先让模型生成一系列推理步骤，再给最终答案。这通常通过在提示中加一句“让我们一步步思考”或提供示范，使模型先吐露中间推理。后置提问则是在模型给出答案后，追加一句例如“你是根据什么判断的？”的提问，再由模型生成解释。对于多模态模型，也可以在提示中明确要求解释视觉依据，如“描述图片中哪些细节支持你的回答”。模型在训练中或零样本时往往可以遵循这些提示，给出一个合乎逻辑的解释过程或理由。由于解释是模型输出的一部分，它可能不完全准确（模型可能编造），但大模型往往能给出看似合理的自洽解释。 | **模型本身的输出**。具体对象是**模型生成的解释文本**。这段文本可能会引用视觉内容、之前的推理等。例如对于图像QA模型的自我解释，会提及**图像中的细节**和**问题的关键词**，相当于模型自己高亮了这些要点，但用自然语言来表达。相比其它方法没有显式的heatmap或数值，它完全是模型的话语。所以作用对象上，算是模型**输出模态**的一种（语言模态）。模型内部的attention或hidden并非直接对象，但往往模型会在解释中提到那些重要元素（间接对应注意力集中处）。 | 前端可以将模型生成的**思维链/解释文本**直接展示给用户。常见形式：模型回答后，在其下方或气泡框中出现“解释：…”。为了区分回答和解释，可以使用不同的字体颜色或样式，或者放在单独对话框里。一些应用可能把解释折叠，需要用户点击“查看详细推理”才展开，以避免一般用户被过多文字干扰。如果模型给出了逐步推理，可以考虑以**逐条列表**形式显示每步推理，以增强可读性。另外，如果解释中提到了图像细节，也可以结合可视化：比如模型在解释中说“左边的人穿着红衣服”，前端可以在图像上闪烁该红衣服区域来佐证模型所述。当然，这需要额外解析文本的能力，属于锦上添花。简单情况下，直接将模型的解释文本原样呈现即可。 | 这种方法适用于**需要模型给出可读推理过程或理由**的场景。例如在多步骤推理题（如奥数、常识推理）中，启用CoT能显著提高正确率并提供清晰步骤。对于多模态任务，比如ScienceQA，结合图像信息的思维链可以帮助模型理解问题。在人机交互中，用户可能会问：“你为什么这么认为？”，模型可以直接以对话形式解释，提升透明度。在机器人应用里，也可以让机器人在执行指令时口头描述自己的计划（这其实也是一种自我解释，以语言形式）。总之，只要模型具备一定生成能力和事实基础，自我解释能增强用户对系统的信任和理解，同时在一些场景下还能提高模型性能。 | **关联性取决于模型诚实度**。理想情况下，模型的自我解释**直接针对它刚才输出的答案**，相关性强。例如模型回答“Yes”后解释“因为我看到他戴着安全帽，所以我认为答案是Yes”，那么这解释清楚表明了模型输出Yes的原因。然而，大语言模型有时会生成与实际内部原因不同但表面合理的解释，即**可能存在偏差**。不过在许多QA场景下，引导模型输出chain-of-thought可以提升准确率，这意味着这些解释与正确推理相关联。因此，一般认为这类解释具有**一定相关性**，但需要和其它方法交叉验证其忠实度。 | **不需要梯度**。整个过程就是额外的一次（或一系列）**前向推理**。仅通过调整输入prompt即可，不涉及对模型参数的调整或梯度计算。也不需要真值标注来训练解释（除非使用监督微调获得更好解释质量，但这里讨论的是推理阶段提示）。因此实现上非常方便。 | **无跨层**。模型产生解释完全是在输出空间操作，没有试图解析模型内部。Chain-of-thought虽然让模型说出了中间步骤，但那些步骤并非模型内部某层的激活，而是模型自己生成的文本。可以理解为模型本身做了一次多步骤推理，但在解释维度上并没有对内部计算图做任何处理。所以它不涉及模型架构层面的信息，只是在**输出层添加额外内容**。 | **Chain-of-Thought**: Kojima *et al.* (2022) 等发现零样本提示“让我们一步步思考”能让LLM输出推理过程并提高准确率。**Multimodal-CoT**: Zhang *et al.* (TMLR 2024)将思维链用于图文场景，在ScienceQA等取得SOTA。很多开源多模态模型（如MiniGPT-4）支持通过系统提示要求解释。OpenAI的ChatGPT也经常能在对话中回答“为什么”，这类自解释已逐渐成为大型AI助手的标准能力。 |  |